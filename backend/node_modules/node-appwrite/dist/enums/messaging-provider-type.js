'use strict';

var MessagingProviderType = /* @__PURE__ */ ((MessagingProviderType2) => {
  MessagingProviderType2["Email"] = "email";
  MessagingProviderType2["Sms"] = "sms";
  MessagingProviderType2["Push"] = "push";
  return MessagingProviderType2;
})(MessagingProviderType || {});

exports.MessagingProviderType = MessagingProviderType;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=messaging-provider-type.js.map