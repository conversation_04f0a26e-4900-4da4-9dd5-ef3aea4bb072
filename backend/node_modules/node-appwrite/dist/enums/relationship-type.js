'use strict';

var RelationshipType = /* @__PURE__ */ ((RelationshipType2) => {
  RelationshipType2["OneToOne"] = "oneToOne";
  RelationshipType2["ManyToOne"] = "manyToOne";
  RelationshipType2["ManyToMany"] = "manyToMany";
  RelationshipType2["OneToMany"] = "oneToMany";
  return RelationshipType2;
})(RelationshipType || {});

exports.RelationshipType = RelationshipType;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=relationship-type.js.map