'use client';

import { useEffect } from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ImageUpload from '@/components/ImageUpload';
import { useAuthStore, useAuthGetters } from '@/stores/auth';
import { useImagesStore, useImagesGetters } from '@/stores/images';
import { 
  Image, 
  Upload, 
  HardDrive, 
  Users, 
  TrendingUp, 
  TrendingDown,
  Copy,
  Download,
  Key
} from 'lucide-react';

export default function DashboardPage() {
  const { initAuth } = useAuthStore();
  const { userName } = useAuthGetters();
  const { fetchImages } = useImagesStore();
  const { sortedImages } = useImagesGetters();

  useEffect(() => {
    const init = async () => {
      await initAuth();
      await fetchImages(1, 10); // Fetch first 10 images for recent display
    };
    init();
  }, [initAuth, fetchImages]);

  // Mock stats data - in real app this would come from API
  const stats = [
    {
      title: 'Total Images',
      value: '1,234',
      change: '+12%',
      trend: 'up',
      icon: Image,
      color: 'text-blue-400'
    },
    {
      title: 'Storage Used',
      value: '2.1 GB',
      change: '+5%',
      trend: 'up',
      icon: HardDrive,
      color: 'text-green-400'
    },
    {
      title: 'Monthly Uploads',
      value: '89',
      change: '+23%',
      trend: 'up',
      icon: Upload,
      color: 'text-purple-400'
    },
    {
      title: 'Total Views',
      value: '12.5K',
      change: '+8%',
      trend: 'up',
      icon: Users,
      color: 'text-orange-400'
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">
              Welcome{userName ? `, ${userName}` : ''}
            </h1>
            <p className="text-gray-400">Upload and manage your images with ease.</p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-100">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    {stat.trend === 'up' ? (
                      <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-400 mr-1" />
                    )}
                    <span className={`text-sm ${stat.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-800/50 ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Upload Section */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-100 mb-2">Quick Upload</h2>
            <p className="text-gray-400">Drag and drop your images or click to browse</p>
          </div>
          <ImageUpload />
        </div>

        {/* Recent Images */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-100">Recent Images</h2>
            <a href="/dashboard/images" className="text-purple-400 hover:text-purple-300 text-sm">
              View all →
            </a>
          </div>
          
          {sortedImages.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {sortedImages.slice(0, 6).map((image) => (
                <div key={image.id} className="group relative bg-gray-800/50 rounded-lg overflow-hidden hover:bg-gray-800/70 transition-colors">
                  <div className="aspect-square bg-gray-700">
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <div className="flex gap-2">
                      <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                        <Copy className="w-4 h-4 text-white" />
                      </button>
                      <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                        <Download className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Image className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 mb-2">No images uploaded yet</p>
              <p className="text-gray-500 text-sm">Upload your first image to get started</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Key className="w-5 h-5 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-100">API Access</h3>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Generate API keys to integrate with your applications
            </p>
            <button className="text-purple-400 hover:text-purple-300 text-sm font-medium">
              Manage API Keys →
            </button>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Download className="w-5 h-5 text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-100">ShareX Config</h3>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Download ShareX configuration for automatic uploads
            </p>
            <button className="text-blue-400 hover:text-blue-300 text-sm font-medium">
              Download Config →
            </button>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <HardDrive className="w-5 h-5 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-100">Storage</h3>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Monitor your storage usage and upgrade if needed
            </p>
            <button className="text-green-400 hover:text-green-300 text-sm font-medium">
              View Usage →
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
