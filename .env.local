# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT="https://api.avehubs.com/v1"
NEXT_PUBLIC_APPWRITE_PROJECT_ID="your_project_id"
NEXT_PUBLIC_APPWRITE_BUCKET_ID="your_bucket_id"
NEXT_PUBLIC_APPWRITE_DATABASE_ID="your_database_id"
NEXT_PUBLIC_APPWRITE_IMAGES_COLLECTION_ID="images"
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID="users"

# Application Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="AveImage Cloud"

# Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE="52428800"
NEXT_PUBLIC_ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp"

# Development
NODE_ENV="development"
