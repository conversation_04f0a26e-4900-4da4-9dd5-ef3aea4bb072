<template>
  <div class="image-upload">
    <!-- Drop Zone -->
    <div
      ref="dropZone"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="openFileDialog"
      :class="[
        'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors duration-200',
        isDragOver ? 'border-purple-400 bg-purple-50 dark:bg-purple-900/20' : 'border-gray-300 dark:border-gray-600',
        'hover:border-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20'
      ]"
    >
      <input
        ref="fileInput"
        type="file"
        multiple
        accept="image/*"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <div v-if="!imageStore.isLoading" class="flex flex-col items-center">
        <div class="w-16 h-16 mb-4 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
          <Upload class="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Drop images here or click to upload
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          PNG, JPG, GIF, WebP up to 50MB
        </p>
        <button
          type="button"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Choose Files
        </button>
      </div>
      
      <!-- Loading State -->
      <div v-else class="flex flex-col items-center">
        <div class="w-16 h-16 mb-4 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Uploading...
        </h3>
        <div class="w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-purple-600 h-2 rounded-full transition-all duration-300"
            :style="`width: ${uploadProgress}%`"
          ></div>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
          {{ uploadProgress }}% complete
        </p>
      </div>
    </div>
    
    <!-- Image Preview -->
    <div v-if="selectedFiles.length > 0 && !imageStore.isLoading" class="mt-6">
      <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">
        Selected Files ({{ selectedFiles.length }})
      </h4>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="relative group"
        >
          <img
            :src="getFilePreview(file)"
            :alt="file.name"
            class="w-full h-24 object-cover rounded-lg"
          />
          <button
            @click="removeFile(index)"
            class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <X class="w-4 h-4" />
          </button>
          <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b-lg truncate">
            {{ file.name }}
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex justify-between items-center">
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Total: {{ formatFileSize(totalSize) }}
        </p>
        <div class="space-x-2">
          <button
            @click="clearFiles"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            Clear All
          </button>
          <button
            @click="uploadFiles"
            class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Upload {{ selectedFiles.length }} file{{ selectedFiles.length !== 1 ? 's' : '' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Upload, X } from 'lucide-vue-next'
import { useImagesStore } from '../stores/images.js'
import Swal from 'sweetalert2'

const imageStore = useImagesStore()

const dropZone = ref(null)
const fileInput = ref(null)
const selectedFiles = ref([])
const isDragOver = ref(false)
const uploadProgress = ref(0)

const totalSize = computed(() => {
  return selectedFiles.value.reduce((total, file) => total + file.size, 0)
})

const openFileDialog = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const files = Array.from(event.target.files)
  addFiles(files)
  // Clear the input so the same file can be selected again
  event.target.value = ''
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = Array.from(event.dataTransfer.files)
  const imageFiles = files.filter(file => file.type.startsWith('image/'))
  
  if (imageFiles.length !== files.length) {
    Swal.fire({
      icon: 'warning',
      title: 'Some files skipped',
      text: 'Only image files are allowed'
    })
  }
  
  addFiles(imageFiles)
}

const handleDragOver = (event) => {
  event.preventDefault()
}

const handleDragEnter = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event) => {
  event.preventDefault()
  if (!dropZone.value?.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

const addFiles = (files) => {
  const validFiles = files.filter(file => {
    // Check file size (50MB limit)
    if (file.size > 50 * 1024 * 1024) {
      Swal.fire({
        icon: 'error',
        title: 'File too large',
        text: `${file.name} is larger than 50MB`
      })
      return false
    }
    return true
  })
  
  selectedFiles.value.push(...validFiles)
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

const clearFiles = () => {
  selectedFiles.value = []
}

const getFilePreview = (file) => {
  return URL.createObjectURL(file)
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const uploadFiles = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    const results = await imageStore.uploadMultipleImages(
      selectedFiles.value,
      (progress) => {
        uploadProgress.value = progress
      }
    )
    
    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length
    
    if (failed === 0) {
      Swal.fire({
        icon: 'success',
        title: 'Upload Complete!',
        text: `Successfully uploaded ${successful} image${successful !== 1 ? 's' : ''}`,
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      Swal.fire({
        icon: 'warning',
        title: 'Upload Partially Complete',
        text: `${successful} uploaded successfully, ${failed} failed`
      })
    }
    
    // Clear selected files after upload
    selectedFiles.value = []
    
  } catch (error) {
    Swal.fire({
      icon: 'error',
      title: 'Upload Failed',
      text: error.message || 'Failed to upload images'
    })
  }
}
</script>
