import { defineStore } from 'pinia';
import { account } from '../config/appwrite.js';
import { ID } from 'appwrite';
import api from '../config/api.js';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    session: null,
    isLoading: false,
    isAuthenticated: false,
    _initialized: false
  }),

  getters: {
    isLoggedIn: (state) => !!state.user && !!state.session && state.isAuthenticated,
    userName: (state) => state.user?.name || '',
    userEmail: (state) => state.user?.email || '',
    isAdmin: (state) => state.user?.labels?.includes('admin') || false
  },

  actions: {
    // Initialize auth state on app start
    async initAuth() {
      if (this.isLoading || this._initialized) return; // Prevent multiple calls

      this.isLoading = true;
      try {
        // First, try to get current session from Appwrite (most reliable)
        try {
          const currentUser = await account.get();
          const currentSession = await account.getSession('current');

          // Set auth state
          this.user = currentUser;
          this.session = currentSession.$id;
          this.isAuthenticated = true;

          // Store in localStorage
          localStorage.setItem('user', JSON.stringify(currentUser));
          localStorage.setItem('session', currentSession.$id);

          console.log('Auth initialized from Appwrite session:', currentUser.email);
        } catch (noSessionError) {
          // No active session in Appwrite, check localStorage as fallback
          const storedSession = localStorage.getItem('session');
          const storedUser = localStorage.getItem('user');

          if (storedSession && storedUser) {
            console.log('Found stored session, attempting to verify...');
            this.session = storedSession;
            this.user = JSON.parse(storedUser);
            this.isAuthenticated = true;

            // Try to verify the stored session is still valid
            try {
              const currentUser = await account.get();
              // Update user data if successful
              this.user = currentUser;
              localStorage.setItem('user', JSON.stringify(currentUser));
              console.log('Stored session verified and updated');
            } catch (sessionError) {
              console.warn('Stored session is invalid, clearing auth state');
              this.clearAuth();
            }
          } else {
            // No stored session either, user is not authenticated
            console.log('No session found, user not authenticated');
            this.clearAuth();
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        this.clearAuth();
      } finally {
        this.isLoading = false;
        this._initialized = true;
      }
    },

    // Register new user
    async register(userData) {
      this.isLoading = true;
      try {
        // Create user directly with Appwrite
        const user = await account.create(
          ID.unique(), // Appwrite will generate unique ID
          userData.email,
          userData.password,
          userData.name
        );
        
        // Create session for the new user
        const session = await account.createEmailPasswordSession(userData.email, userData.password);
        this.session = session.$id;
        localStorage.setItem('session', session.$id);
        
        // Get user data
        const userAccount = await account.get();
        this.user = userAccount;
        localStorage.setItem('user', JSON.stringify(userAccount));
        
        // Initialize user profile in our database
        await this.initializeProfile();
        
        this.isAuthenticated = true;
        return { success: true, message: 'Registration successful!' };
      } catch (error) {
        console.error('Registration error:', error);
        const message = error.message || 'Registration failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Initialize user profile after registration
    async initializeProfile() {
      try {
        const response = await api.post('/user/init-profile');
        return response.data;
      } catch (error) {
        console.error('Initialize profile error:', error);
        throw error;
      }
    },

    // Login user
    async login({ email, password }) {
      this.isLoading = true;
      try {
        // Create session directly with Appwrite
        const session = await account.createEmailPasswordSession(email, password);
        
        // Store session
        this.session = session.$id;
        localStorage.setItem('session', session.$id);
        
        // Get user data directly from Appwrite
        const user = await account.get();
        this.user = user;
        localStorage.setItem('user', JSON.stringify(user));
        
        this.isAuthenticated = true;
        return { success: true, message: 'Login successful!' };
      } catch (error) {
        console.error('Login error:', error);
        const message = error.message || 'Login failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Logout user
    async logout() {
      this.isLoading = true;
      try {
        // Delete session directly with Appwrite
        if (this.session) {
          await account.deleteSession('current');
        }
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.clearAuth();
        this.isLoading = false;
      }
    },

    // Get current user
    async getCurrentUser() {
      try {
        // Get user directly from Appwrite
        const user = await account.get();
        this.user = user;
        localStorage.setItem('user', JSON.stringify(user));
        this.isAuthenticated = true;
        return user;
      } catch (error) {
        console.error('Get user error:', error);
        this.clearAuth();
        throw error;
      }
    },

    // Update user profile
    async updateProfile(profileData) {
      this.isLoading = true;
      try {
        // Update profile directly with Appwrite
        const user = await account.updateName(profileData.name);
        
        this.user = { ...this.user, ...user };
        localStorage.setItem('user', JSON.stringify(this.user));
        return { success: true, message: 'Profile updated successfully!' };
      } catch (error) {
        console.error('Update profile error:', error);
        const message = error.message || 'Update failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Change password
    async changePassword({ oldPassword, newPassword }) {
      this.isLoading = true;
      try {
        // Change password directly with Appwrite
        await account.updatePassword(newPassword, oldPassword);
        return { success: true, message: 'Password changed successfully!' };
      } catch (error) {
        console.error('Change password error:', error);
        const message = error.message || 'Password change failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Forgot password
    async forgotPassword(email) {
      this.isLoading = true;
      try {
        const resetUrl = `${window.location.origin}/auth/reset-password`;
        await account.createRecovery(email, resetUrl);
        return { success: true, message: 'Reset email sent successfully!' };
      } catch (error) {
        console.error('Forgot password error:', error);
        const message = error.message || 'Failed to send reset email';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Reset password
    async resetPassword({ userId, secret, password }) {
      this.isLoading = true;
      try {
        await account.updateRecovery(userId, secret, password);
        return { success: true, message: 'Password reset successful!' };
      } catch (error) {
        console.error('Reset password error:', error);
        const message = error.message || 'Password reset failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // OAuth login
    async loginWithOAuth(provider) {
      try {
        this.isLoading = true;
        
        const successUrl = `${window.location.origin}/auth/callback`;
        const failureUrl = `${window.location.origin}/auth/login?error=oauth_failed`;
        
        // Redirect to OAuth provider using createOAuth2Session
        account.createOAuth2Session(provider, successUrl, failureUrl);
      } catch (error) {
        console.error('OAuth login error:', error);
        this.isLoading = false;
        return { success: false, error: error.message || 'OAuth login failed' };
      }
    },

    // Handle OAuth callback
    async handleOAuthCallback(urlParams) {
      try {
        this.isLoading = true;
        
        // After OAuth redirect, the session should already be established
        // Try to get the current user and session
        const user = await account.get();
        const session = await account.getSession('current');
        
        this.user = user;
        this.session = session.$id;
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('session', session.$id);
        this.isAuthenticated = true;
        
        return { success: true, message: 'OAuth login successful!' };
      } catch (error) {
        console.error('OAuth callback error:', error);
        this.clearAuth();
        return { success: false, error: error.message || 'OAuth login failed' };
      } finally {
        this.isLoading = false;
      }
    },

    // Clear authentication state
    clearAuth() {
      this.user = null;
      this.session = null;
      this.isAuthenticated = false;
      this._initialized = false;
      localStorage.removeItem('session');
      localStorage.removeItem('user');
    }
  }
});
