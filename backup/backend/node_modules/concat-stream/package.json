{"name": "concat-stream", "version": "1.6.2", "description": "writable stream that concatenates strings or binary data and calls a callback with the result", "tags": ["stream", "simple", "util", "utility"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/maxogden/concat-stream.git"}, "bugs": {"url": "http://github.com/maxogden/concat-stream/issues"}, "engines": ["node >= 0.8"], "main": "index.js", "files": ["index.js"], "scripts": {"test": "tape test/*.js test/server/*.js"}, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "devDependencies": {"tape": "^4.6.3"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}