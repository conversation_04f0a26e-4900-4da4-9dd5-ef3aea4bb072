import axios from 'axios';
import { config } from './appwrite.js';

// Create axios instance
const api = axios.create({
  baseURL: config.api.baseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('session');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      console.warn('API request failed with 401, session may be invalid');
      // Clear invalid session but don't redirect automatically
      // Let the auth store and router handle the redirect
      localStorage.removeItem('session');
      localStorage.removeItem('user');

      // Only redirect if we're not already on a login page
      if (!window.location.pathname.includes('/auth/')) {
        // Use router instead of direct window.location to preserve redirect
        import('../router.js').then(({ default: router }) => {
          router.push({ name: 'Login', query: { redirect: window.location.pathname } });
        });
      }
    }
    return Promise.reject(error);
  }
);

export default api;
