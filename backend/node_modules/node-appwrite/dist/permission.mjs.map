{"version": 3, "sources": ["../src/permission.ts"], "names": [], "mappings": ";AAAO,IAAM,aAAN,MAAiB;AAqBxB;AArBa,WAEF,OAAO,CAAC,SAAyB;AACpC,SAAO,SAAS,IAAI;AACxB;AAJS,WAMF,QAAQ,CAAC,SAAyB;AACrC,SAAO,UAAU,IAAI;AACzB;AARS,WAUF,SAAS,CAAC,SAAyB;AACtC,SAAO,WAAW,IAAI;AAC1B;AAZS,WAcF,SAAS,CAAC,SAAyB;AACtC,SAAO,WAAW,IAAI;AAC1B;AAhBS,WAkBF,SAAS,CAAC,SAAyB;AACtC,SAAO,WAAW,IAAI;AAC1B", "sourcesContent": ["export class Permission {\n\n    static read = (role: string): string => {\n        return `read(\"${role}\")`\n    }\n\n    static write = (role: string): string => {\n        return `write(\"${role}\")`\n    }\n\n    static create = (role: string): string => {\n        return `create(\"${role}\")`\n    }\n\n    static update = (role: string): string => {\n        return `update(\"${role}\")`\n    }\n\n    static delete = (role: string): string => {\n        return `delete(\"${role}\")`\n    }\n}\n"]}