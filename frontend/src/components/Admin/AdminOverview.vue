<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h2 class="text-xl font-semibold text-gray-100">Admin Overview</h2>
      <p class="text-gray-400">System statistics and recent activity</p>
    </div>

    <!-- Main Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="flex items-center">
          <Users class="h-8 w-8 text-blue-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Total Users</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalUsers || 0 }}</p>
            <p class="text-xs text-green-400">+{{ stats.newUsersThisMonth || 0 }} this month</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="flex items-center">
          <Image class="h-8 w-8 text-purple-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Total Images</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalImages || 0 }}</p>
            <p class="text-xs text-green-400">+{{ stats.newImagesThisMonth || 0 }} this month</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="flex items-center">
          <HardDrive class="h-8 w-8 text-yellow-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Storage Used</p>
            <p class="text-2xl font-bold text-gray-100">{{ formatBytes(stats.totalStorageUsed || 0) }}</p>
            <p class="text-xs text-gray-400">{{ formatBytes(stats.totalStorageLimit || 0) }} total</p>
          </div>
        </div>
      </div>

      <div class="bg-gray-800 p-6 rounded-lg">
        <div class="flex items-center">
          <Coins class="h-8 w-8 text-green-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Credits in System</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalCredits || 0 }}</p>
            <p class="text-xs text-blue-400">{{ stats.creditsSpentThisMonth || 0 }} spent this month</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- User Growth Chart -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h3 class="text-lg font-medium text-gray-100 mb-4">User Growth</h3>
        <div class="h-64 flex items-center justify-center text-gray-400">
          <div class="text-center">
            <BarChart3 class="h-12 w-12 mx-auto mb-2" />
            <p>Chart visualization would go here</p>
            <p class="text-sm">{{ stats.totalUsers || 0 }} total users</p>
          </div>
        </div>
      </div>

      <!-- Storage Usage Chart -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h3 class="text-lg font-medium text-gray-100 mb-4">Storage Usage</h3>
        <div class="h-64 flex items-center justify-center text-gray-400">
          <div class="text-center">
            <PieChart class="h-12 w-12 mx-auto mb-2" />
            <p>Chart visualization would go here</p>
            <p class="text-sm">{{ formatBytes(stats.totalStorageUsed || 0) }} used</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Users -->
      <div class="bg-gray-800 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-700">
          <h3 class="text-lg font-medium text-gray-100">Recent Users</h3>
        </div>
        <div class="p-6">
          <div v-if="recentUsers.length === 0" class="text-center text-gray-400 py-8">
            No recent users
          </div>
          <div v-else class="space-y-3">
            <div v-for="user in recentUsers" :key="user.$id" class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {{ user.user?.name?.charAt(0) || 'U' }}
                  </span>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-100">{{ user.user?.name || 'Unknown' }}</p>
                  <p class="text-xs text-gray-400">{{ user.user?.email || 'No email' }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-300">{{ user.credits }} credits</p>
                <p class="text-xs text-gray-400">{{ formatDate(user.createdAt) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Transactions -->
      <div class="bg-gray-800 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-700">
          <h3 class="text-lg font-medium text-gray-100">Recent Transactions</h3>
        </div>
        <div class="p-6">
          <div v-if="recentTransactions.length === 0" class="text-center text-gray-400 py-8">
            No recent transactions
          </div>
          <div v-else class="space-y-3">
            <div v-for="transaction in recentTransactions" :key="transaction.$id" class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-100">{{ transaction.planName }}</p>
                <p class="text-xs text-gray-400">{{ transaction.storageAmountMB }}MB storage</p>
              </div>
              <div class="text-right">
                <p class="text-sm text-green-400">{{ transaction.creditCost }} credits</p>
                <p class="text-xs text-gray-400">{{ formatDate(transaction.createdAt) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- System Health -->
    <div class="bg-gray-800 p-6 rounded-lg">
      <h3 class="text-lg font-medium text-gray-100 mb-4">System Health</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
          <div>
            <p class="text-sm font-medium text-gray-100">Database</p>
            <p class="text-xs text-gray-400">Connected</p>
          </div>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
          <div>
            <p class="text-sm font-medium text-gray-100">Storage</p>
            <p class="text-xs text-gray-400">Available</p>
          </div>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
          <div>
            <p class="text-sm font-medium text-gray-100">API</p>
            <p class="text-xs text-gray-400">Operational</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { 
  Users, Image, HardDrive, Coins, 
  BarChart3, PieChart 
} from 'lucide-vue-next'
import api from '../../config/api.js'

const stats = ref({})
const recentUsers = ref([])
const recentTransactions = ref([])
const isLoading = ref(false)

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

const loadOverviewData = async () => {
  try {
    isLoading.value = true
    
    // Load admin overview stats
    const overviewResponse = await api.get('/admin/overview')
    if (overviewResponse.data.success) {
      stats.value = overviewResponse.data.data
      recentUsers.value = overviewResponse.data.data.recentUsers || []
    }

    // Load recent transactions
    try {
      const transactionsResponse = await api.get('/admin/storage/stats')
      if (transactionsResponse.data.success) {
        recentTransactions.value = transactionsResponse.data.data.recentTransactions || []
      }
    } catch (error) {
      console.warn('Could not load transactions:', error)
    }
  } catch (error) {
    console.error('Failed to load overview data:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadOverviewData()
})
</script>
