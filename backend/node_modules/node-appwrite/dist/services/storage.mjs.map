{"version": 3, "sources": ["../../src/services/storage.ts"], "names": [], "mappings": ";AAAA,SAAS,yBAA+D;AAMjE,IAAM,UAAN,MAAc;AAAA,EAGjB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,SAAoB,QAA6C;AAC/E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,aAAa,UAAkB,MAAc,aAAwB,cAAwB,SAAmB,iBAA0B,uBAAkC,aAA2B,YAAsB,WAA6C;AAC5Q,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,oBAAoB,aAAa;AACxC,cAAQ,iBAAiB,IAAI;AAAA,IACjC;AACA,QAAI,OAAO,0BAA0B,aAAa;AAC9C,cAAQ,uBAAuB,IAAI;AAAA,IACvC;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,UAAU,UAA0C;AACtD,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,8BAA8B,QAAQ,cAAc,QAAQ;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,aAAa,UAAkB,MAAc,aAAwB,cAAwB,SAAmB,iBAA0B,uBAAkC,aAA2B,YAAsB,WAA6C;AAC5Q,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,8BAA8B,QAAQ,cAAc,QAAQ;AAC5E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,oBAAoB,aAAa;AACxC,cAAQ,iBAAiB,IAAI;AAAA,IACjC;AACA,QAAI,OAAO,0BAA0B,aAAa;AAC9C,cAAQ,uBAAuB,IAAI;AAAA,IACvC;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,aAAa,UAA+B;AAC9C,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,8BAA8B,QAAQ,cAAc,QAAQ;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,UAAU,UAAkB,SAAoB,QAA2C;AAC7F,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,MAAM,WAAW,UAAkB,QAAgB,MAAY,aAAwB,aAAa,CAAC,aAA6B;AAAA,EAAC,GAAyB;AACxJ,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,QAAQ,UAAkB,QAAsC;AAClE,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,WAAW,UAAkB,QAAgB,MAAe,aAA8C;AAC5G,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW,UAAkB,QAA6B;AAC5D,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB,UAAkB,QAAsC;AAC1E,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,sDAAsD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAChI,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,MAAM,eAAe,UAAkB,QAAgB,OAAgB,QAAiB,SAAwB,SAAkB,aAAsB,aAAsB,cAAuB,SAAkB,UAAmB,YAAqB,QAA4C;AACvS,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,qDAAqD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC/H,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY,UAAkB,QAAsC;AACtE,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,kDAAkD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC5H,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { Compression } from '../enums/compression';\nimport { ImageGravity } from '../enums/image-gravity';\nimport { ImageFormat } from '../enums/image-format';\n\nexport class Storage {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List buckets\n     *\n     * Get a list of all the storage buckets. You can use the query params to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.BucketList>}\n     */\n    async listBuckets(queries?: string[], search?: string): Promise<Models.BucketList> {\n        const apiPath = '/storage/buckets';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create bucket\n     *\n     * Create a new storage bucket.\n     *\n     * @param {string} bucketId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @param {boolean} fileSecurity\n     * @param {boolean} enabled\n     * @param {number} maximumFileSize\n     * @param {string[]} allowedFileExtensions\n     * @param {Compression} compression\n     * @param {boolean} encryption\n     * @param {boolean} antivirus\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Bucket>}\n     */\n    async createBucket(bucketId: string, name: string, permissions?: string[], fileSecurity?: boolean, enabled?: boolean, maximumFileSize?: number, allowedFileExtensions?: string[], compression?: Compression, encryption?: boolean, antivirus?: boolean): Promise<Models.Bucket> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/storage/buckets';\n        const payload: Payload = {};\n        if (typeof bucketId !== 'undefined') {\n            payload['bucketId'] = bucketId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        if (typeof fileSecurity !== 'undefined') {\n            payload['fileSecurity'] = fileSecurity;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof maximumFileSize !== 'undefined') {\n            payload['maximumFileSize'] = maximumFileSize;\n        }\n        if (typeof allowedFileExtensions !== 'undefined') {\n            payload['allowedFileExtensions'] = allowedFileExtensions;\n        }\n        if (typeof compression !== 'undefined') {\n            payload['compression'] = compression;\n        }\n        if (typeof encryption !== 'undefined') {\n            payload['encryption'] = encryption;\n        }\n        if (typeof antivirus !== 'undefined') {\n            payload['antivirus'] = antivirus;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get bucket\n     *\n     * Get a storage bucket by its unique ID. This endpoint response returns a JSON object with the storage bucket metadata.\n     *\n     * @param {string} bucketId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Bucket>}\n     */\n    async getBucket(bucketId: string): Promise<Models.Bucket> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update bucket\n     *\n     * Update a storage bucket by its unique ID.\n     *\n     * @param {string} bucketId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @param {boolean} fileSecurity\n     * @param {boolean} enabled\n     * @param {number} maximumFileSize\n     * @param {string[]} allowedFileExtensions\n     * @param {Compression} compression\n     * @param {boolean} encryption\n     * @param {boolean} antivirus\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Bucket>}\n     */\n    async updateBucket(bucketId: string, name: string, permissions?: string[], fileSecurity?: boolean, enabled?: boolean, maximumFileSize?: number, allowedFileExtensions?: string[], compression?: Compression, encryption?: boolean, antivirus?: boolean): Promise<Models.Bucket> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        if (typeof fileSecurity !== 'undefined') {\n            payload['fileSecurity'] = fileSecurity;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof maximumFileSize !== 'undefined') {\n            payload['maximumFileSize'] = maximumFileSize;\n        }\n        if (typeof allowedFileExtensions !== 'undefined') {\n            payload['allowedFileExtensions'] = allowedFileExtensions;\n        }\n        if (typeof compression !== 'undefined') {\n            payload['compression'] = compression;\n        }\n        if (typeof encryption !== 'undefined') {\n            payload['encryption'] = encryption;\n        }\n        if (typeof antivirus !== 'undefined') {\n            payload['antivirus'] = antivirus;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete bucket\n     *\n     * Delete a storage bucket by its unique ID.\n     *\n     * @param {string} bucketId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteBucket(bucketId: string): Promise<{}> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List files\n     *\n     * Get a list of all the user files. You can use the query params to filter your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FileList>}\n     */\n    async listFiles(bucketId: string, queries?: string[], search?: string): Promise<Models.FileList> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create file\n     *\n     * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.\n\nLarger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.\n\nWhen the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.\n\nIf you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.\n\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async createFile(bucketId: string, fileId: string, file: File, permissions?: string[], onProgress = (progress: UploadProgress) => {}): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'multipart/form-data',\n        }\n\n        return await this.client.chunkedUpload(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n            onProgress\n        );\n    }\n    /**\n     * Get file\n     *\n     * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async getFile(bucketId: string, fileId: string): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update file\n     *\n     * Update a file by its unique ID. Only users with write permissions have access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.File>}\n     */\n    async updateFile(bucketId: string, fileId: string, name?: string, permissions?: string[]): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete File\n     *\n     * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteFile(bucketId: string, fileId: string): Promise<{}> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get file for download\n     *\n     * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getFileDownload(bucketId: string, fileId: string): Promise<ArrayBuffer> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get file preview\n     *\n     * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getFilePreview(bucketId: string, fileId: string, width?: number, height?: number, gravity?: ImageGravity, quality?: number, borderWidth?: number, borderColor?: string, borderRadius?: number, opacity?: number, rotation?: number, background?: string, output?: ImageFormat): Promise<ArrayBuffer> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get file for view\n     *\n     * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getFileView(bucketId: string, fileId: string): Promise<ArrayBuffer> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n}\n"]}