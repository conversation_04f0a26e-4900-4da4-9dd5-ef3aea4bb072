{"version": 3, "sources": ["../../src/enums/browser.ts"], "names": ["Browser"], "mappings": ";AAAO,IAAK,UAAL,kBAAKA,aAAL;AACH,EAAAA,SAAA,kBAAe;AACf,EAAAA,SAAA,wBAAqB;AACrB,EAAAA,SAAA,kBAAe;AACf,EAAAA,SAAA,qBAAkB;AAClB,EAAAA,SAAA,wBAAqB;AACrB,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,oBAAiB;AACjB,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,kBAAe;AACf,EAAAA,SAAA,mBAAgB;AAChB,EAAAA,SAAA,sBAAmB;AACnB,EAAAA,SAAA,eAAY;AACZ,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,eAAY;AAdJ,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum Browser {\n    AvantBrowser = 'aa',\n    AndroidWebViewBeta = 'an',\n    GoogleChrome = 'ch',\n    GoogleChromeIOS = 'ci',\n    GoogleChromeMobile = 'cm',\n    Chromium = 'cr',\n    MozillaFirefox = 'ff',\n    Safari = 'sf',\n    MobileSafari = 'mf',\n    MicrosoftEdge = 'ps',\n    MicrosoftEdgeIOS = 'oi',\n    OperaMini = 'om',\n    Opera = 'op',\n    OperaNext = 'on',\n}"]}