{"version": 3, "names": ["_enum", "require", "transpileConstEnum", "path", "t", "name", "node", "id", "parentIsExport", "parentPath", "isExportNamedDeclaration", "isExported", "isProgram", "parent", "body", "some", "stmt", "exportKind", "source", "specifiers", "spec", "isExportSpecifier", "local", "enum<PERSON><PERSON><PERSON>", "entries", "translateEnumValues", "obj", "objectExpression", "map", "value", "objectProperty", "isValidIdentifier", "identifier", "stringLiteral", "scope", "hasOwnBinding", "replaceWith", "expressionStatement", "callExpression", "memberExpression", "variableDeclaration", "variableDeclarator", "registerDeclaration", "entriesMap", "Map", "traverse", "<PERSON><PERSON>", "skip", "MemberExpression", "isIdentifier", "object", "key", "computed", "isStringLiteral", "property", "has", "cloneNode", "get", "remove"], "sources": ["../src/const-enum.ts"], "sourcesContent": ["import type { NodePath, types as t } from \"@babel/core\";\n\nimport { translateEnumValues } from \"./enum.ts\";\n\nexport type NodePathConstEnum = NodePath<t.TSEnumDeclaration & { const: true }>;\nexport default function transpileConstEnum(\n  path: NodePathConstEnum,\n  t: typeof import(\"@babel/types\"),\n) {\n  const { name } = path.node.id;\n\n  const parentIsExport = path.parentPath.isExportNamedDeclaration();\n  let isExported = parentIsExport;\n  if (!isExported && t.isProgram(path.parent)) {\n    isExported = path.parent.body.some(\n      stmt =>\n        t.isExportNamedDeclaration(stmt) &&\n        stmt.exportKind !== \"type\" &&\n        !stmt.source &&\n        stmt.specifiers.some(\n          spec =>\n            t.isExportSpecifier(spec) &&\n            spec.exportKind !== \"type\" &&\n            spec.local.name === name,\n        ),\n    );\n  }\n\n  const { enumValues: entries } = translateEnumValues(path, t);\n\n  if (isExported) {\n    const obj = t.objectExpression(\n      entries.map(([name, value]) =>\n        t.objectProperty(\n          t.isValidIdentifier(name)\n            ? t.identifier(name)\n            : t.stringLiteral(name),\n          value,\n        ),\n      ),\n    );\n\n    if (path.scope.hasOwnBinding(name)) {\n      (parentIsExport ? path.parentPath : path).replaceWith(\n        t.expressionStatement(\n          t.callExpression(\n            t.memberExpression(t.identifier(\"Object\"), t.identifier(\"assign\")),\n            [path.node.id, obj],\n          ),\n        ),\n      );\n    } else {\n      path.replaceWith(\n        t.variableDeclaration(process.env.BABEL_8_BREAKING ? \"const\" : \"var\", [\n          t.variableDeclarator(path.node.id, obj),\n        ]),\n      );\n      path.scope.registerDeclaration(path);\n    }\n\n    return;\n  }\n\n  const entriesMap = new Map(entries);\n\n  // TODO: After fixing https://github.com/babel/babel/pull/11065, we can\n  // use path.scope.getBinding(name).referencePaths rather than doing\n  // a full traversal.\n  path.scope.path.traverse({\n    Scope(path) {\n      if (path.scope.hasOwnBinding(name)) path.skip();\n    },\n    MemberExpression(path) {\n      if (!t.isIdentifier(path.node.object, { name })) return;\n\n      let key: string;\n      if (path.node.computed) {\n        if (t.isStringLiteral(path.node.property)) {\n          key = path.node.property.value;\n        } else {\n          return;\n        }\n      } else if (t.isIdentifier(path.node.property)) {\n        key = path.node.property.name;\n      } else {\n        return;\n      }\n      if (!entriesMap.has(key)) return;\n\n      path.replaceWith(t.cloneNode(entriesMap.get(key)));\n    },\n  });\n\n  path.remove();\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAGe,SAASC,kBAAkBA,CACxCC,IAAuB,EACvBC,CAAgC,EAChC;EACA,MAAM;IAAEC;EAAK,CAAC,GAAGF,IAAI,CAACG,IAAI,CAACC,EAAE;EAE7B,MAAMC,cAAc,GAAGL,IAAI,CAACM,UAAU,CAACC,wBAAwB,CAAC,CAAC;EACjE,IAAIC,UAAU,GAAGH,cAAc;EAC/B,IAAI,CAACG,UAAU,IAAIP,CAAC,CAACQ,SAAS,CAACT,IAAI,CAACU,MAAM,CAAC,EAAE;IAC3CF,UAAU,GAAGR,IAAI,CAACU,MAAM,CAACC,IAAI,CAACC,IAAI,CAChCC,IAAI,IACFZ,CAAC,CAACM,wBAAwB,CAACM,IAAI,CAAC,IAChCA,IAAI,CAACC,UAAU,KAAK,MAAM,IAC1B,CAACD,IAAI,CAACE,MAAM,IACZF,IAAI,CAACG,UAAU,CAACJ,IAAI,CAClBK,IAAI,IACFhB,CAAC,CAACiB,iBAAiB,CAACD,IAAI,CAAC,IACzBA,IAAI,CAACH,UAAU,KAAK,MAAM,IAC1BG,IAAI,CAACE,KAAK,CAACjB,IAAI,KAAKA,IACxB,CACJ,CAAC;EACH;EAEA,MAAM;IAAEkB,UAAU,EAAEC;EAAQ,CAAC,GAAG,IAAAC,yBAAmB,EAACtB,IAAI,EAAEC,CAAC,CAAC;EAE5D,IAAIO,UAAU,EAAE;IACd,MAAMe,GAAG,GAAGtB,CAAC,CAACuB,gBAAgB,CAC5BH,OAAO,CAACI,GAAG,CAAC,CAAC,CAACvB,IAAI,EAAEwB,KAAK,CAAC,KACxBzB,CAAC,CAAC0B,cAAc,CACd1B,CAAC,CAAC2B,iBAAiB,CAAC1B,IAAI,CAAC,GACrBD,CAAC,CAAC4B,UAAU,CAAC3B,IAAI,CAAC,GAClBD,CAAC,CAAC6B,aAAa,CAAC5B,IAAI,CAAC,EACzBwB,KACF,CACF,CACF,CAAC;IAED,IAAI1B,IAAI,CAAC+B,KAAK,CAACC,aAAa,CAAC9B,IAAI,CAAC,EAAE;MAClC,CAACG,cAAc,GAAGL,IAAI,CAACM,UAAU,GAAGN,IAAI,EAAEiC,WAAW,CACnDhC,CAAC,CAACiC,mBAAmB,CACnBjC,CAAC,CAACkC,cAAc,CACdlC,CAAC,CAACmC,gBAAgB,CAACnC,CAAC,CAAC4B,UAAU,CAAC,QAAQ,CAAC,EAAE5B,CAAC,CAAC4B,UAAU,CAAC,QAAQ,CAAC,CAAC,EAClE,CAAC7B,IAAI,CAACG,IAAI,CAACC,EAAE,EAAEmB,GAAG,CACpB,CACF,CACF,CAAC;IACH,CAAC,MAAM;MACLvB,IAAI,CAACiC,WAAW,CACdhC,CAAC,CAACoC,mBAAmB,CAA0C,KAAK,EAAE,CACpEpC,CAAC,CAACqC,kBAAkB,CAACtC,IAAI,CAACG,IAAI,CAACC,EAAE,EAAEmB,GAAG,CAAC,CACxC,CACH,CAAC;MACDvB,IAAI,CAAC+B,KAAK,CAACQ,mBAAmB,CAACvC,IAAI,CAAC;IACtC;IAEA;EACF;EAEA,MAAMwC,UAAU,GAAG,IAAIC,GAAG,CAACpB,OAAO,CAAC;EAKnCrB,IAAI,CAAC+B,KAAK,CAAC/B,IAAI,CAAC0C,QAAQ,CAAC;IACvBC,KAAKA,CAAC3C,IAAI,EAAE;MACV,IAAIA,IAAI,CAAC+B,KAAK,CAACC,aAAa,CAAC9B,IAAI,CAAC,EAAEF,IAAI,CAAC4C,IAAI,CAAC,CAAC;IACjD,CAAC;IACDC,gBAAgBA,CAAC7C,IAAI,EAAE;MACrB,IAAI,CAACC,CAAC,CAAC6C,YAAY,CAAC9C,IAAI,CAACG,IAAI,CAAC4C,MAAM,EAAE;QAAE7C;MAAK,CAAC,CAAC,EAAE;MAEjD,IAAI8C,GAAW;MACf,IAAIhD,IAAI,CAACG,IAAI,CAAC8C,QAAQ,EAAE;QACtB,IAAIhD,CAAC,CAACiD,eAAe,CAAClD,IAAI,CAACG,IAAI,CAACgD,QAAQ,CAAC,EAAE;UACzCH,GAAG,GAAGhD,IAAI,CAACG,IAAI,CAACgD,QAAQ,CAACzB,KAAK;QAChC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAIzB,CAAC,CAAC6C,YAAY,CAAC9C,IAAI,CAACG,IAAI,CAACgD,QAAQ,CAAC,EAAE;QAC7CH,GAAG,GAAGhD,IAAI,CAACG,IAAI,CAACgD,QAAQ,CAACjD,IAAI;MAC/B,CAAC,MAAM;QACL;MACF;MACA,IAAI,CAACsC,UAAU,CAACY,GAAG,CAACJ,GAAG,CAAC,EAAE;MAE1BhD,IAAI,CAACiC,WAAW,CAAChC,CAAC,CAACoD,SAAS,CAACb,UAAU,CAACc,GAAG,CAACN,GAAG,CAAC,CAAC,CAAC;IACpD;EACF,CAAC,CAAC;EAEFhD,IAAI,CAACuD,MAAM,CAAC,CAAC;AACf", "ignoreList": []}