'use strict';

var client = require('../client');

class Messaging {
  constructor(client) {
    this.client = client;
  }
  /**
   * List messages
   *
   * Get a list of all messages from the current Appwrite project.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.MessageList>}
   */
  async listMessages(queries, search) {
    const apiPath = "/messaging/messages";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create email
   *
   * Create a new email message.
   *
   * @param {string} messageId
   * @param {string} subject
   * @param {string} content
   * @param {string[]} topics
   * @param {string[]} users
   * @param {string[]} targets
   * @param {string[]} cc
   * @param {string[]} bcc
   * @param {string[]} attachments
   * @param {boolean} draft
   * @param {boolean} html
   * @param {string} scheduledAt
   * @throws {AppwriteException}
   * @returns {Promise<Models.Message>}
   */
  async createEmail(messageId, subject, content, topics, users, targets, cc, bcc, attachments, draft, html, scheduledAt) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    if (typeof subject === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "subject"');
    }
    if (typeof content === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "content"');
    }
    const apiPath = "/messaging/messages/email";
    const payload = {};
    if (typeof messageId !== "undefined") {
      payload["messageId"] = messageId;
    }
    if (typeof subject !== "undefined") {
      payload["subject"] = subject;
    }
    if (typeof content !== "undefined") {
      payload["content"] = content;
    }
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof cc !== "undefined") {
      payload["cc"] = cc;
    }
    if (typeof bcc !== "undefined") {
      payload["bcc"] = bcc;
    }
    if (typeof attachments !== "undefined") {
      payload["attachments"] = attachments;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof html !== "undefined") {
      payload["html"] = html;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update email
       *
       * Update an email message by its unique ID.
  
       *
       * @param {string} messageId
       * @param {string[]} topics
       * @param {string[]} users
       * @param {string[]} targets
       * @param {string} subject
       * @param {string} content
       * @param {boolean} draft
       * @param {boolean} html
       * @param {string[]} cc
       * @param {string[]} bcc
       * @param {string} scheduledAt
       * @param {string[]} attachments
       * @throws {AppwriteException}
       * @returns {Promise<Models.Message>}
       */
  async updateEmail(messageId, topics, users, targets, subject, content, draft, html, cc, bcc, scheduledAt, attachments) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/email/{messageId}".replace("{messageId}", messageId);
    const payload = {};
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof subject !== "undefined") {
      payload["subject"] = subject;
    }
    if (typeof content !== "undefined") {
      payload["content"] = content;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof html !== "undefined") {
      payload["html"] = html;
    }
    if (typeof cc !== "undefined") {
      payload["cc"] = cc;
    }
    if (typeof bcc !== "undefined") {
      payload["bcc"] = bcc;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    if (typeof attachments !== "undefined") {
      payload["attachments"] = attachments;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create push notification
   *
   * Create a new push notification.
   *
   * @param {string} messageId
   * @param {string} title
   * @param {string} body
   * @param {string[]} topics
   * @param {string[]} users
   * @param {string[]} targets
   * @param {object} data
   * @param {string} action
   * @param {string} image
   * @param {string} icon
   * @param {string} sound
   * @param {string} color
   * @param {string} tag
   * @param {string} badge
   * @param {boolean} draft
   * @param {string} scheduledAt
   * @throws {AppwriteException}
   * @returns {Promise<Models.Message>}
   */
  async createPush(messageId, title, body, topics, users, targets, data, action, image, icon, sound, color, tag, badge, draft, scheduledAt) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    if (typeof title === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "title"');
    }
    if (typeof body === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "body"');
    }
    const apiPath = "/messaging/messages/push";
    const payload = {};
    if (typeof messageId !== "undefined") {
      payload["messageId"] = messageId;
    }
    if (typeof title !== "undefined") {
      payload["title"] = title;
    }
    if (typeof body !== "undefined") {
      payload["body"] = body;
    }
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof data !== "undefined") {
      payload["data"] = data;
    }
    if (typeof action !== "undefined") {
      payload["action"] = action;
    }
    if (typeof image !== "undefined") {
      payload["image"] = image;
    }
    if (typeof icon !== "undefined") {
      payload["icon"] = icon;
    }
    if (typeof sound !== "undefined") {
      payload["sound"] = sound;
    }
    if (typeof color !== "undefined") {
      payload["color"] = color;
    }
    if (typeof tag !== "undefined") {
      payload["tag"] = tag;
    }
    if (typeof badge !== "undefined") {
      payload["badge"] = badge;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update push notification
       *
       * Update a push notification by its unique ID.
  
       *
       * @param {string} messageId
       * @param {string[]} topics
       * @param {string[]} users
       * @param {string[]} targets
       * @param {string} title
       * @param {string} body
       * @param {object} data
       * @param {string} action
       * @param {string} image
       * @param {string} icon
       * @param {string} sound
       * @param {string} color
       * @param {string} tag
       * @param {number} badge
       * @param {boolean} draft
       * @param {string} scheduledAt
       * @throws {AppwriteException}
       * @returns {Promise<Models.Message>}
       */
  async updatePush(messageId, topics, users, targets, title, body, data, action, image, icon, sound, color, tag, badge, draft, scheduledAt) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/push/{messageId}".replace("{messageId}", messageId);
    const payload = {};
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof title !== "undefined") {
      payload["title"] = title;
    }
    if (typeof body !== "undefined") {
      payload["body"] = body;
    }
    if (typeof data !== "undefined") {
      payload["data"] = data;
    }
    if (typeof action !== "undefined") {
      payload["action"] = action;
    }
    if (typeof image !== "undefined") {
      payload["image"] = image;
    }
    if (typeof icon !== "undefined") {
      payload["icon"] = icon;
    }
    if (typeof sound !== "undefined") {
      payload["sound"] = sound;
    }
    if (typeof color !== "undefined") {
      payload["color"] = color;
    }
    if (typeof tag !== "undefined") {
      payload["tag"] = tag;
    }
    if (typeof badge !== "undefined") {
      payload["badge"] = badge;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create SMS
   *
   * Create a new SMS message.
   *
   * @param {string} messageId
   * @param {string} content
   * @param {string[]} topics
   * @param {string[]} users
   * @param {string[]} targets
   * @param {boolean} draft
   * @param {string} scheduledAt
   * @throws {AppwriteException}
   * @returns {Promise<Models.Message>}
   */
  async createSms(messageId, content, topics, users, targets, draft, scheduledAt) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    if (typeof content === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "content"');
    }
    const apiPath = "/messaging/messages/sms";
    const payload = {};
    if (typeof messageId !== "undefined") {
      payload["messageId"] = messageId;
    }
    if (typeof content !== "undefined") {
      payload["content"] = content;
    }
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update SMS
       *
       * Update an email message by its unique ID.
  
       *
       * @param {string} messageId
       * @param {string[]} topics
       * @param {string[]} users
       * @param {string[]} targets
       * @param {string} content
       * @param {boolean} draft
       * @param {string} scheduledAt
       * @throws {AppwriteException}
       * @returns {Promise<Models.Message>}
       */
  async updateSms(messageId, topics, users, targets, content, draft, scheduledAt) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/sms/{messageId}".replace("{messageId}", messageId);
    const payload = {};
    if (typeof topics !== "undefined") {
      payload["topics"] = topics;
    }
    if (typeof users !== "undefined") {
      payload["users"] = users;
    }
    if (typeof targets !== "undefined") {
      payload["targets"] = targets;
    }
    if (typeof content !== "undefined") {
      payload["content"] = content;
    }
    if (typeof draft !== "undefined") {
      payload["draft"] = draft;
    }
    if (typeof scheduledAt !== "undefined") {
      payload["scheduledAt"] = scheduledAt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Get message
       *
       * Get a message by its unique ID.
  
       *
       * @param {string} messageId
       * @throws {AppwriteException}
       * @returns {Promise<Models.Message>}
       */
  async getMessage(messageId) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/{messageId}".replace("{messageId}", messageId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete message
   *
   * Delete a message. If the message is not a draft or scheduled, but has been sent, this will not recall the message.
   *
   * @param {string} messageId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async delete(messageId) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/{messageId}".replace("{messageId}", messageId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List message logs
   *
   * Get the message activity logs listed by its unique ID.
   *
   * @param {string} messageId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.LogList>}
   */
  async listMessageLogs(messageId, queries) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/{messageId}/logs".replace("{messageId}", messageId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List message targets
   *
   * Get a list of the targets associated with a message.
   *
   * @param {string} messageId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.TargetList>}
   */
  async listTargets(messageId, queries) {
    if (typeof messageId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "messageId"');
    }
    const apiPath = "/messaging/messages/{messageId}/targets".replace("{messageId}", messageId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List providers
   *
   * Get a list of all providers from the current Appwrite project.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.ProviderList>}
   */
  async listProviders(queries, search) {
    const apiPath = "/messaging/providers";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create APNS provider
   *
   * Create a new Apple Push Notification service provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} authKey
   * @param {string} authKeyId
   * @param {string} teamId
   * @param {string} bundleId
   * @param {boolean} sandbox
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createApnsProvider(providerId, name, authKey, authKeyId, teamId, bundleId, sandbox, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/apns";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof authKey !== "undefined") {
      payload["authKey"] = authKey;
    }
    if (typeof authKeyId !== "undefined") {
      payload["authKeyId"] = authKeyId;
    }
    if (typeof teamId !== "undefined") {
      payload["teamId"] = teamId;
    }
    if (typeof bundleId !== "undefined") {
      payload["bundleId"] = bundleId;
    }
    if (typeof sandbox !== "undefined") {
      payload["sandbox"] = sandbox;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update APNS provider
   *
   * Update a Apple Push Notification service provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} authKey
   * @param {string} authKeyId
   * @param {string} teamId
   * @param {string} bundleId
   * @param {boolean} sandbox
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateApnsProvider(providerId, name, enabled, authKey, authKeyId, teamId, bundleId, sandbox) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/apns/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof authKey !== "undefined") {
      payload["authKey"] = authKey;
    }
    if (typeof authKeyId !== "undefined") {
      payload["authKeyId"] = authKeyId;
    }
    if (typeof teamId !== "undefined") {
      payload["teamId"] = teamId;
    }
    if (typeof bundleId !== "undefined") {
      payload["bundleId"] = bundleId;
    }
    if (typeof sandbox !== "undefined") {
      payload["sandbox"] = sandbox;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create FCM provider
   *
   * Create a new Firebase Cloud Messaging provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {object} serviceAccountJSON
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createFcmProvider(providerId, name, serviceAccountJSON, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/fcm";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof serviceAccountJSON !== "undefined") {
      payload["serviceAccountJSON"] = serviceAccountJSON;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update FCM provider
   *
   * Update a Firebase Cloud Messaging provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {object} serviceAccountJSON
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateFcmProvider(providerId, name, enabled, serviceAccountJSON) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/fcm/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof serviceAccountJSON !== "undefined") {
      payload["serviceAccountJSON"] = serviceAccountJSON;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Mailgun provider
   *
   * Create a new Mailgun provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} apiKey
   * @param {string} domain
   * @param {boolean} isEuRegion
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createMailgunProvider(providerId, name, apiKey, domain, isEuRegion, fromName, fromEmail, replyToName, replyToEmail, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/mailgun";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof domain !== "undefined") {
      payload["domain"] = domain;
    }
    if (typeof isEuRegion !== "undefined") {
      payload["isEuRegion"] = isEuRegion;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Mailgun provider
   *
   * Update a Mailgun provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} apiKey
   * @param {string} domain
   * @param {boolean} isEuRegion
   * @param {boolean} enabled
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateMailgunProvider(providerId, name, apiKey, domain, isEuRegion, enabled, fromName, fromEmail, replyToName, replyToEmail) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/mailgun/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof domain !== "undefined") {
      payload["domain"] = domain;
    }
    if (typeof isEuRegion !== "undefined") {
      payload["isEuRegion"] = isEuRegion;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Msg91 provider
   *
   * Create a new MSG91 provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} templateId
   * @param {string} senderId
   * @param {string} authKey
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createMsg91Provider(providerId, name, templateId, senderId, authKey, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/msg91";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof templateId !== "undefined") {
      payload["templateId"] = templateId;
    }
    if (typeof senderId !== "undefined") {
      payload["senderId"] = senderId;
    }
    if (typeof authKey !== "undefined") {
      payload["authKey"] = authKey;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Msg91 provider
   *
   * Update a MSG91 provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} templateId
   * @param {string} senderId
   * @param {string} authKey
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateMsg91Provider(providerId, name, enabled, templateId, senderId, authKey) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/msg91/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof templateId !== "undefined") {
      payload["templateId"] = templateId;
    }
    if (typeof senderId !== "undefined") {
      payload["senderId"] = senderId;
    }
    if (typeof authKey !== "undefined") {
      payload["authKey"] = authKey;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Sendgrid provider
   *
   * Create a new Sendgrid provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} apiKey
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createSendgridProvider(providerId, name, apiKey, fromName, fromEmail, replyToName, replyToEmail, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/sendgrid";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Sendgrid provider
   *
   * Update a Sendgrid provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} apiKey
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateSendgridProvider(providerId, name, enabled, apiKey, fromName, fromEmail, replyToName, replyToEmail) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/sendgrid/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create SMTP provider
   *
   * Create a new SMTP provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} host
   * @param {number} port
   * @param {string} username
   * @param {string} password
   * @param {SmtpEncryption} encryption
   * @param {boolean} autoTLS
   * @param {string} mailer
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createSmtpProvider(providerId, name, host, port, username, password, encryption, autoTLS, mailer, fromName, fromEmail, replyToName, replyToEmail, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    if (typeof host === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "host"');
    }
    const apiPath = "/messaging/providers/smtp";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof host !== "undefined") {
      payload["host"] = host;
    }
    if (typeof port !== "undefined") {
      payload["port"] = port;
    }
    if (typeof username !== "undefined") {
      payload["username"] = username;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof encryption !== "undefined") {
      payload["encryption"] = encryption;
    }
    if (typeof autoTLS !== "undefined") {
      payload["autoTLS"] = autoTLS;
    }
    if (typeof mailer !== "undefined") {
      payload["mailer"] = mailer;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update SMTP provider
   *
   * Update a SMTP provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} host
   * @param {number} port
   * @param {string} username
   * @param {string} password
   * @param {SmtpEncryption} encryption
   * @param {boolean} autoTLS
   * @param {string} mailer
   * @param {string} fromName
   * @param {string} fromEmail
   * @param {string} replyToName
   * @param {string} replyToEmail
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateSmtpProvider(providerId, name, host, port, username, password, encryption, autoTLS, mailer, fromName, fromEmail, replyToName, replyToEmail, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/smtp/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof host !== "undefined") {
      payload["host"] = host;
    }
    if (typeof port !== "undefined") {
      payload["port"] = port;
    }
    if (typeof username !== "undefined") {
      payload["username"] = username;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof encryption !== "undefined") {
      payload["encryption"] = encryption;
    }
    if (typeof autoTLS !== "undefined") {
      payload["autoTLS"] = autoTLS;
    }
    if (typeof mailer !== "undefined") {
      payload["mailer"] = mailer;
    }
    if (typeof fromName !== "undefined") {
      payload["fromName"] = fromName;
    }
    if (typeof fromEmail !== "undefined") {
      payload["fromEmail"] = fromEmail;
    }
    if (typeof replyToName !== "undefined") {
      payload["replyToName"] = replyToName;
    }
    if (typeof replyToEmail !== "undefined") {
      payload["replyToEmail"] = replyToEmail;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Telesign provider
   *
   * Create a new Telesign provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} from
   * @param {string} customerId
   * @param {string} apiKey
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createTelesignProvider(providerId, name, from, customerId, apiKey, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/telesign";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    if (typeof customerId !== "undefined") {
      payload["customerId"] = customerId;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Telesign provider
   *
   * Update a Telesign provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} customerId
   * @param {string} apiKey
   * @param {string} from
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateTelesignProvider(providerId, name, enabled, customerId, apiKey, from) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/telesign/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof customerId !== "undefined") {
      payload["customerId"] = customerId;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Textmagic provider
   *
   * Create a new Textmagic provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} from
   * @param {string} username
   * @param {string} apiKey
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createTextmagicProvider(providerId, name, from, username, apiKey, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/textmagic";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    if (typeof username !== "undefined") {
      payload["username"] = username;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Textmagic provider
   *
   * Update a Textmagic provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} username
   * @param {string} apiKey
   * @param {string} from
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateTextmagicProvider(providerId, name, enabled, username, apiKey, from) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/textmagic/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof username !== "undefined") {
      payload["username"] = username;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Twilio provider
   *
   * Create a new Twilio provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} from
   * @param {string} accountSid
   * @param {string} authToken
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createTwilioProvider(providerId, name, from, accountSid, authToken, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/twilio";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    if (typeof accountSid !== "undefined") {
      payload["accountSid"] = accountSid;
    }
    if (typeof authToken !== "undefined") {
      payload["authToken"] = authToken;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Twilio provider
   *
   * Update a Twilio provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} accountSid
   * @param {string} authToken
   * @param {string} from
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateTwilioProvider(providerId, name, enabled, accountSid, authToken, from) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/twilio/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof accountSid !== "undefined") {
      payload["accountSid"] = accountSid;
    }
    if (typeof authToken !== "undefined") {
      payload["authToken"] = authToken;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create Vonage provider
   *
   * Create a new Vonage provider.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {string} from
   * @param {string} apiKey
   * @param {string} apiSecret
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async createVonageProvider(providerId, name, from, apiKey, apiSecret, enabled) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/providers/vonage";
    const payload = {};
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof apiSecret !== "undefined") {
      payload["apiSecret"] = apiSecret;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update Vonage provider
   *
   * Update a Vonage provider by its unique ID.
   *
   * @param {string} providerId
   * @param {string} name
   * @param {boolean} enabled
   * @param {string} apiKey
   * @param {string} apiSecret
   * @param {string} from
   * @throws {AppwriteException}
   * @returns {Promise<Models.Provider>}
   */
  async updateVonageProvider(providerId, name, enabled, apiKey, apiSecret, from) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/vonage/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof apiKey !== "undefined") {
      payload["apiKey"] = apiKey;
    }
    if (typeof apiSecret !== "undefined") {
      payload["apiSecret"] = apiSecret;
    }
    if (typeof from !== "undefined") {
      payload["from"] = from;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Get provider
       *
       * Get a provider by its unique ID.
  
       *
       * @param {string} providerId
       * @throws {AppwriteException}
       * @returns {Promise<Models.Provider>}
       */
  async getProvider(providerId) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete provider
   *
   * Delete a provider by its unique ID.
   *
   * @param {string} providerId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteProvider(providerId) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/{providerId}".replace("{providerId}", providerId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List provider logs
   *
   * Get the provider activity logs listed by its unique ID.
   *
   * @param {string} providerId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.LogList>}
   */
  async listProviderLogs(providerId, queries) {
    if (typeof providerId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "providerId"');
    }
    const apiPath = "/messaging/providers/{providerId}/logs".replace("{providerId}", providerId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List subscriber logs
   *
   * Get the subscriber activity logs listed by its unique ID.
   *
   * @param {string} subscriberId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.LogList>}
   */
  async listSubscriberLogs(subscriberId, queries) {
    if (typeof subscriberId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "subscriberId"');
    }
    const apiPath = "/messaging/subscribers/{subscriberId}/logs".replace("{subscriberId}", subscriberId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List topics
   *
   * Get a list of all topics from the current Appwrite project.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.TopicList>}
   */
  async listTopics(queries, search) {
    const apiPath = "/messaging/topics";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create topic
   *
   * Create a new topic.
   *
   * @param {string} topicId
   * @param {string} name
   * @param {string[]} subscribe
   * @throws {AppwriteException}
   * @returns {Promise<Models.Topic>}
   */
  async createTopic(topicId, name, subscribe) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/messaging/topics";
    const payload = {};
    if (typeof topicId !== "undefined") {
      payload["topicId"] = topicId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof subscribe !== "undefined") {
      payload["subscribe"] = subscribe;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Get topic
       *
       * Get a topic by its unique ID.
  
       *
       * @param {string} topicId
       * @throws {AppwriteException}
       * @returns {Promise<Models.Topic>}
       */
  async getTopic(topicId) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    const apiPath = "/messaging/topics/{topicId}".replace("{topicId}", topicId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update topic
       *
       * Update a topic by its unique ID.
  
       *
       * @param {string} topicId
       * @param {string} name
       * @param {string[]} subscribe
       * @throws {AppwriteException}
       * @returns {Promise<Models.Topic>}
       */
  async updateTopic(topicId, name, subscribe) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    const apiPath = "/messaging/topics/{topicId}".replace("{topicId}", topicId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof subscribe !== "undefined") {
      payload["subscribe"] = subscribe;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete topic
   *
   * Delete a topic by its unique ID.
   *
   * @param {string} topicId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteTopic(topicId) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    const apiPath = "/messaging/topics/{topicId}".replace("{topicId}", topicId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List topic logs
   *
   * Get the topic activity logs listed by its unique ID.
   *
   * @param {string} topicId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.LogList>}
   */
  async listTopicLogs(topicId, queries) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    const apiPath = "/messaging/topics/{topicId}/logs".replace("{topicId}", topicId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List subscribers
   *
   * Get a list of all subscribers from the current Appwrite project.
   *
   * @param {string} topicId
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.SubscriberList>}
   */
  async listSubscribers(topicId, queries, search) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    const apiPath = "/messaging/topics/{topicId}/subscribers".replace("{topicId}", topicId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create subscriber
   *
   * Create a new subscriber.
   *
   * @param {string} topicId
   * @param {string} subscriberId
   * @param {string} targetId
   * @throws {AppwriteException}
   * @returns {Promise<Models.Subscriber>}
   */
  async createSubscriber(topicId, subscriberId, targetId) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    if (typeof subscriberId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "subscriberId"');
    }
    if (typeof targetId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "targetId"');
    }
    const apiPath = "/messaging/topics/{topicId}/subscribers".replace("{topicId}", topicId);
    const payload = {};
    if (typeof subscriberId !== "undefined") {
      payload["subscriberId"] = subscriberId;
    }
    if (typeof targetId !== "undefined") {
      payload["targetId"] = targetId;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Get subscriber
       *
       * Get a subscriber by its unique ID.
  
       *
       * @param {string} topicId
       * @param {string} subscriberId
       * @throws {AppwriteException}
       * @returns {Promise<Models.Subscriber>}
       */
  async getSubscriber(topicId, subscriberId) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    if (typeof subscriberId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "subscriberId"');
    }
    const apiPath = "/messaging/topics/{topicId}/subscribers/{subscriberId}".replace("{topicId}", topicId).replace("{subscriberId}", subscriberId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete subscriber
   *
   * Delete a subscriber by its unique ID.
   *
   * @param {string} topicId
   * @param {string} subscriberId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteSubscriber(topicId, subscriberId) {
    if (typeof topicId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "topicId"');
    }
    if (typeof subscriberId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "subscriberId"');
    }
    const apiPath = "/messaging/topics/{topicId}/subscribers/{subscriberId}".replace("{topicId}", topicId).replace("{subscriberId}", subscriberId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
}

exports.Messaging = Messaging;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=messaging.js.map