// src/query.ts
var _Query = class _Query {
  constructor(method, attribute, values) {
    this.method = method;
    this.attribute = attribute;
    if (values !== void 0) {
      if (Array.isArray(values)) {
        this.values = values;
      } else {
        this.values = [values];
      }
    }
  }
  toString() {
    return JSON.stringify({
      method: this.method,
      attribute: this.attribute,
      values: this.values
    });
  }
};
_Query.equal = (attribute, value) => new _Query("equal", attribute, value).toString();
_Query.notEqual = (attribute, value) => new _Query("notEqual", attribute, value).toString();
_Query.lessThan = (attribute, value) => new _Query("lessThan", attribute, value).toString();
_Query.lessThanEqual = (attribute, value) => new _Query("lessThanEqual", attribute, value).toString();
_Query.greaterThan = (attribute, value) => new _Query("greaterThan", attribute, value).toString();
_Query.greaterThanEqual = (attribute, value) => new _Query("greaterThanEqual", attribute, value).toString();
_Query.isNull = (attribute) => new _Query("isNull", attribute).toString();
_Query.isNotNull = (attribute) => new _Query("isNotNull", attribute).toString();
_Query.between = (attribute, start, end) => new _Query("between", attribute, [start, end]).toString();
_Query.startsWith = (attribute, value) => new _Query("startsWith", attribute, value).toString();
_Query.endsWith = (attribute, value) => new _Query("endsWith", attribute, value).toString();
_Query.select = (attributes) => new _Query("select", void 0, attributes).toString();
_Query.search = (attribute, value) => new _Query("search", attribute, value).toString();
_Query.orderDesc = (attribute) => new _Query("orderDesc", attribute).toString();
_Query.orderAsc = (attribute) => new _Query("orderAsc", attribute).toString();
_Query.cursorAfter = (documentId) => new _Query("cursorAfter", void 0, documentId).toString();
_Query.cursorBefore = (documentId) => new _Query("cursorBefore", void 0, documentId).toString();
_Query.limit = (limit) => new _Query("limit", void 0, limit).toString();
_Query.offset = (offset) => new _Query("offset", void 0, offset).toString();
_Query.contains = (attribute, value) => new _Query("contains", attribute, value).toString();
_Query.or = (queries) => new _Query("or", void 0, queries.map((query) => JSON.parse(query))).toString();
_Query.and = (queries) => new _Query("and", void 0, queries.map((query) => JSON.parse(query))).toString();
var Query = _Query;

export { Query };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=query.mjs.map