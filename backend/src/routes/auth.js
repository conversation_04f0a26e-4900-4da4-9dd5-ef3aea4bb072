import express from 'express';
import {
  register,
  login,
  logout,
  getCurrentUser,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  createOAuthSession,
  handleOAuthCallback,
  handleOAuthFailure
} from '../controllers/authController.js';
import { authenticateUser } from '../middleware/auth.js';
import { authLimiter } from '../middleware/rateLimiting.js';

const router = express.Router();

// Public routes
router.post('/register', authLimiter, register);
router.post('/login', authLimiter, login);
router.post('/logout', logout);
router.post('/forgot-password', authLimiter, forgotPassword);
router.post('/reset-password', authLimiter, resetPassword);

// OAuth routes
router.get('/oauth/:provider', createOAuthSession);
router.get('/oauth/callback', handleOAuthCallback);
router.get('/oauth/failure', handleOAuthFailure);

// Protected routes
router.get('/user', authenticateUser, getCurrentUser);
router.put('/profile', authenticateUser, updateProfile);
router.put('/password', authenticateUser, changePassword);

export default router;
