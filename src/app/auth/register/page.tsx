'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AuthLayout from '@/components/layouts/AuthLayout';
import FormCard from '@/components/auth/FormCard';
import FormInput from '@/components/auth/FormInput';
import { useAuthStore } from '@/stores/auth';
import Swal from 'sweetalert2';

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading } = useAuthStore();

  const [form, setForm] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form
      if (!form.name || !form.email || !form.password || !form.confirmPassword) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'All fields are required'
        });
        return;
      }

      if (form.password !== form.confirmPassword) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Passwords do not match'
        });
        return;
      }

      if (form.password.length < 8) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Password must be at least 8 characters long'
        });
        return;
      }

      const result = await register({
        name: form.name,
        email: form.email,
        password: form.password
      });

      if (result.success) {
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Registration successful! Please login with your credentials.',
          timer: 2000,
          showConfirmButton: false
        });
        
        // Redirect to login page
        router.push('/auth/login');
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Registration Failed',
          text: result.error
        });
      }
    } catch (error) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Registration failed. Please try again.'
      });
    }
  };

  return (
    <AuthLayout>
      <FormCard title="Create Account" onSubmit={handleSubmit}>
        <FormInput
          id="name"
          label="Full Name"
          value={form.name}
          onChange={(value) => setForm({ ...form, name: value })}
          placeholder="Enter your full name"
          required
        />
        
        <FormInput
          id="email"
          label="Email"
          value={form.email}
          onChange={(value) => setForm({ ...form, email: value })}
          placeholder="Enter your email"
          required
        />

        <FormInput
          id="password"
          label="Password"
          type="password"
          value={form.password}
          onChange={(value) => setForm({ ...form, password: value })}
          placeholder="Enter your password"
          required
        />

        <FormInput
          id="confirmPassword"
          label="Confirm Password"
          type="password"
          value={form.confirmPassword}
          onChange={(value) => setForm({ ...form, confirmPassword: value })}
          placeholder="Confirm your password"
          required
        />

        <button
          type="submit"
          className="w-full mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
          disabled={isLoading}
        >
          {isLoading ? 'Creating Account...' : 'Create Account'}
        </button>

        <p className="mt-4 text-center text-sm text-gray-400">
          Already have an account?{' '}
          <Link href="/auth/login" className="text-purple-400 hover:text-purple-300">
            Sign in
          </Link>
        </p>
      </FormCard>
    </AuthLayout>
  );
}
