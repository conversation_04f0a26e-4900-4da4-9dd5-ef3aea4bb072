{"version": 3, "sources": ["../../src/services/health.ts"], "names": [], "mappings": ";AAAA,SAAS,yBAA+D;AAIjE,IAAM,SAAN,MAAa;AAAA,EAGhB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAoC;AACtC,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAAgD;AAClD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,WAAyC;AAC3C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,QAAoD;AACrE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,QAAsC;AACxC,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAA0C;AAC5C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,WAAyC;AAC3C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,WAAiD;AAClE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,qBAAqB,WAAiD;AACxE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,kBAAkB,MAAe,WAAiD;AACpF,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,gBAAgB,WAAiD;AACnE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,MAAY,WAAiD;AAC7E,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,8BAA8B,QAAQ,UAAU,IAAI;AACpE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,kBAAkB,WAAiD;AACrE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,aAAa,WAAiD;AAChE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,WAAiD;AACjE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,kBAAkB,WAAiD;AACrE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,mBAAmB,WAAiD;AACtE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,WAAiD;AACjE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,kBAAkB,WAAiD;AACrE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,iBAAiB,WAAiD;AACpE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,aAA2C;AAC7C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,kBAAgD;AAClD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,UAAsC;AACxC,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { Name } from '../enums/name';\n\nexport class Health {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get HTTP\n     *\n     * Check the Appwrite HTTP server is up and responsive.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async get(): Promise<Models.HealthStatus> {\n        const apiPath = '/health';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get antivirus\n     *\n     * Check the Appwrite Antivirus server is up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthAntivirus>}\n     */\n    async getAntivirus(): Promise<Models.HealthAntivirus> {\n        const apiPath = '/health/anti-virus';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get cache\n     *\n     * Check the Appwrite in-memory cache servers are up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getCache(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/cache';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get the SSL certificate for a domain\n     *\n     * Get the SSL certificate for a domain\n     *\n     * @param {string} domain\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthCertificate>}\n     */\n    async getCertificate(domain?: string): Promise<Models.HealthCertificate> {\n        const apiPath = '/health/certificate';\n        const payload: Payload = {};\n        if (typeof domain !== 'undefined') {\n            payload['domain'] = domain;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get DB\n     *\n     * Check the Appwrite database servers are up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getDB(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/db';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get pubsub\n     *\n     * Check the Appwrite pub-sub servers are up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getPubSub(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/pubsub';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get queue\n     *\n     * Check the Appwrite queue messaging servers are up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getQueue(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/queue';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get builds queue\n     *\n     * Get the number of builds that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueBuilds(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/builds';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get certificates queue\n     *\n     * Get the number of certificates that are waiting to be issued against [Letsencrypt](https://letsencrypt.org/) in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueCertificates(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/certificates';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get databases queue\n     *\n     * Get the number of database changes that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {string} name\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueDatabases(name?: string, threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/databases';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get deletes queue\n     *\n     * Get the number of background destructive changes that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueDeletes(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/deletes';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get number of failed queue jobs\n     *\n     * Returns the amount of failed jobs in a given queue.\n\n     *\n     * @param {Name} name\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getFailedJobs(name: Name, threshold?: number): Promise<Models.HealthQueue> {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/health/queue/failed/{name}'.replace('{name}', name);\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get functions queue\n     *\n     * Get the number of function executions that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueFunctions(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/functions';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get logs queue\n     *\n     * Get the number of logs that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueLogs(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/logs';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get mails queue\n     *\n     * Get the number of mails that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueMails(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/mails';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get messaging queue\n     *\n     * Get the number of messages that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueMessaging(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/messaging';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get migrations queue\n     *\n     * Get the number of migrations that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueMigrations(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/migrations';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get usage queue\n     *\n     * Get the number of metrics that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueUsage(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/usage';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get usage dump queue\n     *\n     * Get the number of projects containing metrics that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueUsageDump(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/usage-dump';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get webhooks queue\n     *\n     * Get the number of webhooks that are waiting to be processed in the Appwrite internal queue server.\n     *\n     * @param {number} threshold\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthQueue>}\n     */\n    async getQueueWebhooks(threshold?: number): Promise<Models.HealthQueue> {\n        const apiPath = '/health/queue/webhooks';\n        const payload: Payload = {};\n        if (typeof threshold !== 'undefined') {\n            payload['threshold'] = threshold;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get storage\n     *\n     * Check the Appwrite storage device is up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getStorage(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/storage';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get local storage\n     *\n     * Check the Appwrite local storage device is up and connection is successful.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthStatus>}\n     */\n    async getStorageLocal(): Promise<Models.HealthStatus> {\n        const apiPath = '/health/storage/local';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get time\n     *\n     * Check the Appwrite server time is synced with Google remote NTP server. We use this technology to smoothly handle leap seconds with no disruptive events. The [Network Time Protocol](https://en.wikipedia.org/wiki/Network_Time_Protocol) (NTP) is used by hundreds of millions of computers and devices to synchronize their clocks over the Internet. If your computer sets its own clock, it likely uses NTP.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.HealthTime>}\n     */\n    async getTime(): Promise<Models.HealthTime> {\n        const apiPath = '/health/time';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}