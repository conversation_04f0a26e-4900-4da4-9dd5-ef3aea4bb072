"use strict";var n=Object.defineProperty;var t=(r,a)=>n(r,"name",{value:a,configurable:!0});var c=Object.defineProperty,e=t((r,a)=>c(r,"name",{value:a,configurable:!0}),"e");function createAgent(){return{agent:void 0,dispatcher:void 0}}t(createAgent,"createAgent"),e(createAgent,"createAgent");function createFetch(){return globalThis.fetch}t(createFetch,"createFetch"),e(createFetch,"createFetch");const fetch=globalThis.fetch;exports.createAgent=createAgent,exports.createFetch=createFetch,exports.fetch=fetch;
