{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\n\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n *\n * @example\n * // returns true\n * const nodePathAQDotB = NodePath(\"if (a?.#b) {}\").get(\"test\"); // a?.#b\n * willPathCastToBoolean(nodePathAQDotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a?.#b\"))\n * @todo Respect transparent expression wrappers\n * @see {@link packages/babel-plugin-transform-optional-chaining/src/util.js}\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = path;\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON>, Visitor } from \"@babel/traverse\";\nimport {\n  LOGICAL_OPERATORS,\n  arrowFunctionExpression,\n  assignmentExpression,\n  binaryExpression,\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  identifier,\n  isMemberExpression,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isUpdateExpression,\n  logicalExpression,\n  memberExpression,\n  nullLiteral,\n  optionalCallExpression,\n  optionalMemberExpression,\n  sequenceExpression,\n  updateExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { willPathCastToBoolean } from \"./util.ts\";\n\nclass AssignmentMemoiser {\n  private _map: WeakMap<t.Expression, { count: number; value: t.Identifier }>;\n  constructor() {\n    this._map = new WeakMap();\n  }\n\n  has(key: t.Expression) {\n    return this._map.has(key);\n  }\n\n  get(key: t.Expression) {\n    if (!this.has(key)) return;\n\n    const record = this._map.get(key);\n    const { value } = record;\n\n    record.count--;\n    if (record.count === 0) {\n      // The `count` access is the outermost function call (hopefully), so it\n      // does the assignment.\n      return assignmentExpression(\"=\", value, key);\n    }\n    return value;\n  }\n\n  set(key: t.Expression, value: t.Identifier, count: number) {\n    return this._map.set(key, { count, value });\n  }\n}\n\nfunction toNonOptional(\n  path: NodePath<t.Expression>,\n  base: t.Expression,\n): t.Expression {\n  const { node } = path;\n  if (isOptionalMemberExpression(node)) {\n    return memberExpression(base, node.property, node.computed);\n  }\n\n  if (path.isOptionalCallExpression()) {\n    const callee = path.get(\"callee\");\n    if (path.node.optional && callee.isOptionalMemberExpression()) {\n      // object must be a conditional expression because the optional private access in object has been transformed\n      const object = callee.node.object as t.ConditionalExpression;\n      const context = path.scope.maybeGenerateMemoised(object);\n      callee\n        .get(\"object\")\n        .replaceWith(assignmentExpression(\"=\", context, object));\n\n      return callExpression(memberExpression(base, identifier(\"call\")), [\n        context,\n        ...path.node.arguments,\n      ]);\n    }\n\n    return callExpression(base, path.node.arguments);\n  }\n\n  return path.node;\n}\n\n// Determines if the current path is in a detached tree. This can happen when\n// we are iterating on a path, and replace an ancestor with a new node. Babel\n// doesn't always stop traversing the old node tree, and that can cause\n// inconsistencies.\nfunction isInDetachedTree(path: NodePath) {\n  while (path) {\n    if (path.isProgram()) break;\n\n    const { parentPath, container, listKey } = path;\n    const parentNode = parentPath.node;\n    if (listKey) {\n      if (\n        container !==\n        // @ts-expect-error listKey must be a valid parent node key\n        parentNode[listKey]\n      ) {\n        return true;\n      }\n    } else {\n      if (container !== parentNode) return true;\n    }\n\n    path = parentPath;\n  }\n\n  return false;\n}\n\ntype Member = NodePath<t.OptionalMemberExpression | t.MemberExpression>;\n\nconst handle = {\n  memoise() {\n    // noop.\n  },\n\n  handle(this: HandlerState, member: Member, noDocumentAll: boolean) {\n    const { node, parent, parentPath, scope } = member;\n\n    if (member.isOptionalMemberExpression()) {\n      // Transforming optional chaining requires we replace ancestors.\n      if (isInDetachedTree(member)) return;\n\n      // We're looking for the end of _this_ optional chain, which is actually\n      // the \"rightmost\" property access of the chain. This is because\n      // everything up to that property access is \"optional\".\n      //\n      // Let's take the case of `FOO?.BAR.baz?.qux`, with `FOO?.BAR` being our\n      // member. The \"end\" to most users would be `qux` property access.\n      // Everything up to it could be skipped if it `FOO` were nullish. But\n      // actually, we can consider the `baz` access to be the end. So we're\n      // looking for the nearest optional chain that is `optional: true`.\n      const endPath = member.find(({ node, parent }) => {\n        if (isOptionalMemberExpression(parent)) {\n          // We need to check `parent.object` since we could be inside the\n          // computed expression of a `bad?.[FOO?.BAR]`. In this case, the\n          // endPath is the `FOO?.BAR` member itself.\n          return parent.optional || parent.object !== node;\n        }\n        if (isOptionalCallExpression(parent)) {\n          // Checking `parent.callee` since we could be in the arguments, eg\n          // `bad?.(FOO?.BAR)`.\n          // Also skip `FOO?.BAR` in `FOO?.BAR?.()` since we need to transform the optional call to ensure proper this\n          return (\n            // In FOO?.#BAR?.(), endPath points the optional call expression so we skip FOO?.#BAR\n            (node !== member.node && parent.optional) || parent.callee !== node\n          );\n        }\n        return true;\n      }) as NodePath<t.OptionalMemberExpression>;\n\n      // Replace `function (a, x = a.b?.#c) {}` to `function (a, x = (() => a.b?.#c)() ){}`\n      // so the temporary variable can be injected in correct scope\n      // This can be further optimized to avoid unnecessary IIFE\n      if (scope.path.isPattern()) {\n        endPath.replaceWith(\n          // The injected member will be queued and eventually transformed when visited\n          callExpression(arrowFunctionExpression([], endPath.node), []),\n        );\n        return;\n      }\n\n      const willEndPathCastToBoolean = willPathCastToBoolean(endPath);\n\n      const rootParentPath = endPath.parentPath;\n      if (rootParentPath.isUpdateExpression({ argument: node })) {\n        throw member.buildCodeFrameError(`can't handle update expression`);\n      }\n      const isAssignment = rootParentPath.isAssignmentExpression({\n        left: endPath.node,\n      });\n      const isDeleteOperation = rootParentPath.isUnaryExpression({\n        operator: \"delete\",\n      });\n      if (\n        isDeleteOperation &&\n        endPath.isOptionalMemberExpression() &&\n        endPath.get(\"property\").isPrivateName()\n      ) {\n        // @babel/parser will throw error on `delete obj?.#x`.\n        // This error serves as fallback when `delete obj?.#x` is constructed from babel types\n        throw member.buildCodeFrameError(\n          `can't delete a private class element`,\n        );\n      }\n\n      // Now, we're looking for the start of this optional chain, which is\n      // optional to the left of this member.\n      //\n      // Let's take the case of `foo?.bar?.baz.QUX?.BAM`, with `QUX?.BAM` being\n      // our member. The \"start\" to most users would be `foo` object access.\n      // But actually, we can consider the `bar` access to be the start. So\n      // we're looking for the nearest optional chain that is `optional: true`,\n      // which is guaranteed to be somewhere in the object/callee tree.\n      let startingOptional: NodePath<t.Expression> = member;\n      for (;;) {\n        if (startingOptional.isOptionalMemberExpression()) {\n          if (startingOptional.node.optional) break;\n          startingOptional = startingOptional.get(\"object\");\n          continue;\n        } else if (startingOptional.isOptionalCallExpression()) {\n          if (startingOptional.node.optional) break;\n          startingOptional = startingOptional.get(\"callee\");\n          continue;\n        }\n        // prevent infinite loop: unreachable if the AST is well-formed\n        throw new Error(\n          `Internal error: unexpected ${startingOptional.node.type}`,\n        );\n      }\n\n      const startingNode = startingOptional.isOptionalMemberExpression()\n        ? startingOptional.node.object\n        : startingOptional.node.callee;\n      const baseNeedsMemoised = scope.maybeGenerateMemoised(startingNode);\n      const baseRef = baseNeedsMemoised ?? startingNode;\n\n      // Compute parentIsOptionalCall before `startingOptional` is replaced\n      // as `node` may refer to `startingOptional.node` before replaced.\n      const parentIsOptionalCall = parentPath.isOptionalCallExpression({\n        callee: node,\n      });\n      // here we use a function to wrap `parentIsOptionalCall` to get type\n      // for parent, do not use it anywhere else\n      // See https://github.com/microsoft/TypeScript/issues/10421\n      const isOptionalCall = (\n        parent: t.Node,\n      ): parent is t.OptionalCallExpression => parentIsOptionalCall;\n      // if parentIsCall is true, it implies that node.extra.parenthesized is always true\n      const parentIsCall = parentPath.isCallExpression({ callee: node });\n      startingOptional.replaceWith(toNonOptional(startingOptional, baseRef));\n      if (isOptionalCall(parent)) {\n        if (parent.optional) {\n          parentPath.replaceWith(this.optionalCall(member, parent.arguments));\n        } else {\n          parentPath.replaceWith(this.call(member, parent.arguments));\n        }\n      } else if (parentIsCall) {\n        // `(a?.#b)()` to `(a == null ? void 0 : a.#b.bind(a))()`\n        member.replaceWith(this.boundGet(member));\n      } else if (\n        (process.env.BABEL_8_BREAKING || this.delete) &&\n        parentPath.isUnaryExpression({ operator: \"delete\" })\n      ) {\n        parentPath.replaceWith(this.delete(member));\n      } else if (parentPath.isAssignmentExpression()) {\n        // `a?.#b = c` to `(a == null ? void 0 : a.#b = c)`\n        handleAssignment(this, member, parentPath);\n      } else {\n        member.replaceWith(this.get(member));\n      }\n\n      let regular: t.Expression = member.node;\n      for (let current: NodePath = member; current !== endPath; ) {\n        const parentPath = current.parentPath as NodePath<t.Expression>;\n        // skip transforming `Foo.#BAR?.call(FOO)`\n        if (\n          parentPath === endPath &&\n          isOptionalCall(parent) &&\n          parent.optional\n        ) {\n          regular = parentPath.node;\n          break;\n        }\n        regular = toNonOptional(parentPath, regular);\n        current = parentPath;\n      }\n\n      let context: t.Identifier;\n      const endParentPath = endPath.parentPath as NodePath<t.Expression>;\n      if (\n        isMemberExpression(regular) &&\n        endParentPath.isOptionalCallExpression({\n          callee: endPath.node,\n          optional: true,\n        })\n      ) {\n        const { object } = regular;\n        context = member.scope.maybeGenerateMemoised(object);\n        if (context) {\n          regular.object = assignmentExpression(\n            \"=\",\n            context,\n            // object must not be Super when `context` is an identifier\n            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n            object as t.Expression,\n          );\n        }\n      }\n\n      let replacementPath: NodePath = endPath;\n      if (isDeleteOperation || isAssignment) {\n        replacementPath = endParentPath;\n        regular = endParentPath.node;\n      }\n\n      const baseMemoised = baseNeedsMemoised\n        ? assignmentExpression(\n            \"=\",\n            // When base needs memoised, the baseRef must be an identifier\n            cloneNode(baseRef as t.Identifier),\n            cloneNode(startingNode),\n          )\n        : cloneNode(baseRef);\n\n      if (willEndPathCastToBoolean) {\n        let nonNullishCheck;\n        if (noDocumentAll) {\n          nonNullishCheck = binaryExpression(\"!=\", baseMemoised, nullLiteral());\n        } else {\n          nonNullishCheck = logicalExpression(\n            \"&&\",\n            binaryExpression(\"!==\", baseMemoised, nullLiteral()),\n            binaryExpression(\n              \"!==\",\n              cloneNode(baseRef),\n              scope.buildUndefinedNode(),\n            ),\n          );\n        }\n        replacementPath.replaceWith(\n          logicalExpression(\"&&\", nonNullishCheck, regular),\n        );\n      } else {\n        let nullishCheck;\n        if (noDocumentAll) {\n          nullishCheck = binaryExpression(\"==\", baseMemoised, nullLiteral());\n        } else {\n          nullishCheck = logicalExpression(\n            \"||\",\n            binaryExpression(\"===\", baseMemoised, nullLiteral()),\n            binaryExpression(\n              \"===\",\n              cloneNode(baseRef),\n              scope.buildUndefinedNode(),\n            ),\n          );\n        }\n\n        replacementPath.replaceWith(\n          conditionalExpression(\n            nullishCheck,\n            isDeleteOperation\n              ? booleanLiteral(true)\n              : scope.buildUndefinedNode(),\n            regular,\n          ),\n        );\n      }\n\n      // context and isDeleteOperation can not be both truthy\n      if (context) {\n        const endParent = endParentPath.node as t.OptionalCallExpression;\n        endParentPath.replaceWith(\n          optionalCallExpression(\n            optionalMemberExpression(\n              endParent.callee,\n              identifier(\"call\"),\n              false,\n              true,\n            ),\n            [cloneNode(context), ...endParent.arguments],\n            false,\n          ),\n        );\n      }\n\n      return;\n    }\n\n    // MEMBER++   ->   _set(MEMBER, (ref = _get(MEMBER), ref2 = ref++, ref)), ref2\n    // ++MEMBER   ->   _set(MEMBER, (ref = _get(MEMBER), ++ref))\n    if (isUpdateExpression(parent, { argument: node })) {\n      if (this.simpleSet) {\n        member.replaceWith(this.simpleSet(member));\n        return;\n      }\n\n      const { operator, prefix } = parent;\n\n      // Give the state handler a chance to memoise the member, since we'll\n      // reference it twice. The second access (the set) should do the memo\n      // assignment.\n      this.memoise(member, 2);\n\n      const ref = scope.generateUidIdentifierBasedOnNode(node);\n      scope.push({ id: ref });\n\n      const seq: t.Expression[] = [\n        // ref = _get(MEMBER)\n        assignmentExpression(\"=\", cloneNode(ref), this.get(member)),\n      ];\n\n      if (prefix) {\n        seq.push(updateExpression(operator, cloneNode(ref), prefix));\n\n        // (ref = _get(MEMBER), ++ref)\n        const value = sequenceExpression(seq);\n        parentPath.replaceWith(this.set(member, value));\n\n        return;\n      } else {\n        const ref2 = scope.generateUidIdentifierBasedOnNode(node);\n        scope.push({ id: ref2 });\n\n        seq.push(\n          assignmentExpression(\n            \"=\",\n            cloneNode(ref2),\n            updateExpression(operator, cloneNode(ref), prefix),\n          ),\n          cloneNode(ref),\n        );\n\n        // (ref = _get(MEMBER), ref2 = ref++, ref)\n        const value = sequenceExpression(seq);\n        parentPath.replaceWith(\n          sequenceExpression([this.set(member, value), cloneNode(ref2)]),\n        );\n\n        return;\n      }\n    }\n\n    // MEMBER = VALUE   ->   _set(MEMBER, VALUE)\n    // MEMBER += VALUE   ->   _set(MEMBER, _get(MEMBER) + VALUE)\n    // MEMBER ??= VALUE   ->   _get(MEMBER) ?? _set(MEMBER, VALUE)\n    if (parentPath.isAssignmentExpression({ left: node })) {\n      handleAssignment(this, member, parentPath);\n      return;\n    }\n\n    // MEMBER(ARGS) -> _call(MEMBER, ARGS)\n    if (parentPath.isCallExpression({ callee: node })) {\n      parentPath.replaceWith(this.call(member, parentPath.node.arguments));\n      return;\n    }\n\n    // MEMBER?.(ARGS) -> _optionalCall(MEMBER, ARGS)\n    if (parentPath.isOptionalCallExpression({ callee: node })) {\n      // Replace `function (a, x = a.b.#c?.()) {}` to `function (a, x = (() => a.b.#c?.())() ){}`\n      // so the temporary variable can be injected in correct scope\n      // This can be further optimized to avoid unnecessary IIFE\n      if (scope.path.isPattern()) {\n        parentPath.replaceWith(\n          // The injected member will be queued and eventually transformed when visited\n          callExpression(arrowFunctionExpression([], parentPath.node), []),\n        );\n        return;\n      }\n      parentPath.replaceWith(\n        this.optionalCall(member, parentPath.node.arguments),\n      );\n      return;\n    }\n\n    // delete MEMBER -> _delete(MEMBER)\n    if (\n      (process.env.BABEL_8_BREAKING || this.delete) &&\n      parentPath.isUnaryExpression({ operator: \"delete\" })\n    ) {\n      parentPath.replaceWith(this.delete(member));\n      return;\n    }\n\n    // for (MEMBER of ARR)\n    // for (MEMBER in ARR)\n    // { KEY: MEMBER } = OBJ -> { KEY: _destructureSet(MEMBER) } = OBJ\n    // { KEY: MEMBER = _VALUE } = OBJ -> { KEY: _destructureSet(MEMBER) = _VALUE } = OBJ\n    // {...MEMBER} -> {..._destructureSet(MEMBER)}\n    //\n    // [MEMBER] = ARR -> [_destructureSet(MEMBER)] = ARR\n    // [MEMBER = _VALUE] = ARR -> [_destructureSet(MEMBER) = _VALUE] = ARR\n    // [...MEMBER] -> [..._destructureSet(MEMBER)]\n    if (\n      // for (MEMBER of ARR)\n      // for (MEMBER in ARR)\n      parentPath.isForXStatement({ left: node }) ||\n      // { KEY: MEMBER } = OBJ\n      (parentPath.isObjectProperty({ value: node }) &&\n        parentPath.parentPath.isObjectPattern()) ||\n      // { KEY: MEMBER = _VALUE } = OBJ\n      (parentPath.isAssignmentPattern({ left: node }) &&\n        parentPath.parentPath.isObjectProperty({ value: parent }) &&\n        parentPath.parentPath.parentPath.isObjectPattern()) ||\n      // [MEMBER] = ARR\n      parentPath.isArrayPattern() ||\n      // [MEMBER = _VALUE] = ARR\n      (parentPath.isAssignmentPattern({ left: node }) &&\n        parentPath.parentPath.isArrayPattern()) ||\n      // {...MEMBER}\n      // [...MEMBER]\n      parentPath.isRestElement()\n    ) {\n      member.replaceWith(this.destructureSet(member));\n      return;\n    }\n\n    if (parentPath.isTaggedTemplateExpression()) {\n      // MEMBER   ->   _get(MEMBER).bind(this)\n      member.replaceWith(this.boundGet(member));\n    } else {\n      // MEMBER   ->   _get(MEMBER)\n      member.replaceWith(this.get(member));\n    }\n  },\n};\n\nfunction handleAssignment(\n  state: HandlerState,\n  member: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  parentPath: NodePath<t.AssignmentExpression>,\n) {\n  if (state.simpleSet) {\n    member.replaceWith(state.simpleSet(member));\n    return;\n  }\n\n  const { operator, right: value } = parentPath.node;\n\n  if (operator === \"=\") {\n    parentPath.replaceWith(state.set(member, value));\n  } else {\n    const operatorTrunc = operator.slice(0, -1);\n    if (LOGICAL_OPERATORS.includes(operatorTrunc)) {\n      // Give the state handler a chance to memoise the member, since we'll\n      // reference it twice. The first access (the get) should do the memo\n      // assignment.\n      state.memoise(member, 1);\n      parentPath.replaceWith(\n        logicalExpression(\n          operatorTrunc as t.LogicalExpression[\"operator\"],\n          state.get(member),\n          state.set(member, value),\n        ),\n      );\n    } else {\n      // Here, the second access (the set) is evaluated first.\n      state.memoise(member, 2);\n      parentPath.replaceWith(\n        state.set(\n          member,\n          binaryExpression(\n            operatorTrunc as t.BinaryExpression[\"operator\"],\n            state.get(member),\n            value,\n          ),\n        ),\n      );\n    }\n  }\n}\n\nexport interface Handler<State> {\n  memoise?(\n    this: HandlerState<State> & State,\n    member: Member,\n    count: number,\n  ): void;\n  destructureSet(\n    this: HandlerState<State> & State,\n    member: Member,\n  ): t.Expression;\n  boundGet(this: HandlerState<State> & State, member: Member): t.Expression;\n  simpleSet?(this: HandlerState<State> & State, member: Member): t.Expression;\n  get(this: HandlerState<State> & State, member: Member): t.Expression;\n  set(\n    this: HandlerState<State> & State,\n    member: Member,\n    value: t.Expression,\n  ): t.Expression;\n  call(\n    this: HandlerState<State> & State,\n    member: Member,\n    args: t.CallExpression[\"arguments\"],\n  ): t.Expression;\n  optionalCall(\n    this: HandlerState<State> & State,\n    member: Member,\n    args: t.OptionalCallExpression[\"arguments\"],\n  ): t.Expression;\n  delete(this: HandlerState<State> & State, member: Member): t.Expression;\n}\n\nexport interface HandlerState<State = object> extends Handler<State> {\n  handle(\n    this: HandlerState<State> & State,\n    member: Member,\n    noDocumentAll?: boolean,\n  ): void;\n  memoiser: AssignmentMemoiser;\n}\n\n// We do not provide a default traversal visitor\n// Instead, caller passes one, and must call `state.handle` on the members\n// it wishes to be transformed.\n// Additionally, the caller must pass in a state object with at least\n// get, set, and call methods.\n// Optionally, a memoise method may be defined on the state, which will be\n// called when the member is a self-referential update.\nexport default function memberExpressionToFunctions<CustomState extends object>(\n  path: NodePath,\n  visitor: Visitor<HandlerState<CustomState>>,\n  state: Handler<CustomState> & CustomState,\n) {\n  path.traverse(visitor, {\n    ...handle,\n    ...state,\n    memoiser: new AssignmentMemoiser(),\n  });\n}\n"], "names": ["willPathCastToBoolean", "path", "maybeWrapped", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop", "LOGICAL_OPERATORS", "arrowFunctionExpression", "assignmentExpression", "binaryExpression", "booleanLiteral", "callExpression", "cloneNode", "conditionalExpression", "identifier", "isMemberExpression", "isOptionalCallExpression", "isOptionalMemberExpression", "isUpdateExpression", "logicalExpression", "memberExpression", "nullLiteral", "optionalCallExpression", "optionalMemberExpression", "sequenceExpression", "updateExpression", "_t", "AssignmentMemoiser", "constructor", "_map", "WeakMap", "has", "key", "get", "record", "value", "count", "set", "toNonOptional", "base", "property", "computed", "callee", "optional", "object", "context", "scope", "maybeGenerateMemoised", "replaceWith", "arguments", "isInDetachedTree", "isProgram", "container", "<PERSON><PERSON><PERSON>", "parentNode", "handle", "memoise", "member", "noDocumentAll", "parent", "endPath", "find", "isPattern", "willEndPathCastToBoolean", "rootParentPath", "argument", "buildCodeFrameError", "isAssignment", "isAssignmentExpression", "left", "isDeleteOperation", "isPrivateName", "startingOptional", "Error", "type", "startingNode", "baseNeedsMemoised", "baseRef", "parentIsOptionalCall", "isOptionalCall", "parentIsCall", "isCallExpression", "optionalCall", "call", "boundGet", "delete", "handleAssignment", "regular", "current", "endParentPath", "<PERSON><PERSON><PERSON>", "baseMemoised", "nonNullish<PERSON><PERSON>ck", "buildUndefinedNode", "nullish<PERSON><PERSON><PERSON>", "endParent", "simpleSet", "prefix", "ref", "generateUidIdentifierBasedOnNode", "push", "id", "seq", "ref2", "isForXStatement", "isObjectProperty", "isObjectPattern", "isAssignmentPattern", "isArrayPattern", "isRestElement", "destructureSet", "isTaggedTemplateExpression", "state", "operatorTrunc", "slice", "includes", "memberExpressionToFunctions", "visitor", "traverse", "Object", "assign", "memoiser"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAiBO,SAASA,qBAAqBA,CAACC,IAAc,EAAW;EAC7D,MAAMC,YAAY,GAAGD,IAAI,CAAA;EACzB,MAAM;IAAEE,IAAI;AAAEC,IAAAA,UAAAA;AAAW,GAAC,GAAGF,YAAY,CAAA;AACzC,EAAA,IAAIE,UAAU,CAACC,mBAAmB,EAAE,EAAE;IACpC,MAAM;MAAEC,QAAQ;AAAEC,MAAAA,KAAAA;KAAO,GAAGH,UAAU,CAACD,IAAI,CAAA;AAC3C,IAAA,IACEG,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,IAAI,IAChBA,QAAQ,KAAK,IAAI,IAAIH,IAAI,KAAKI,KAAM,EACrC;MACA,OAAOP,qBAAqB,CAACI,UAAU,CAAC,CAAA;AAC1C,KAAA;AACF,GAAA;AACA,EAAA,IAAIA,UAAU,CAACI,oBAAoB,EAAE,EAAE;IACrC,MAAM;AAAEC,MAAAA,WAAAA;KAAa,GAAGL,UAAU,CAACD,IAAI,CAAA;IACvC,IAAIM,WAAW,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKP,IAAI,EAAE;MAChD,OAAOH,qBAAqB,CAACI,UAAU,CAAC,CAAA;AAC1C,KAAC,MAAM;AAIL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;EACA,OACEA,UAAU,CAACO,aAAa,CAAC;AAAEC,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,IACxCC,UAAU,CAACS,iBAAiB,CAAC;AAAEP,IAAAA,QAAQ,EAAE,GAAA;AAAI,GAAC,CAAC,IAC/CF,UAAU,CAACU,MAAM,CAAC;AAAEF,IAAAA,IAAI,EAAET,IAAAA;AAAK,GAAC,CAAC,CAAA;AAErC;;ACxBsB,MAAA;EApBpBY,iBAAiB;EACjBC,uBAAuB;EACvBC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,kBAAkB;EAClBC,wBAAwB;EACxBC,0BAA0B;EAC1BC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,WAAW;EACXC,sBAAsB;EACtBC,wBAAwB;EACxBC,kBAAkB;AAClBC,EAAAA,gBAAAA;AAAgB,CAAA,GAAAC,aAAA,CAAA;AAKlB,MAAMC,kBAAkB,CAAC;AAEvBC,EAAAA,WAAWA,GAAG;AAAA,IAAA,IAAA,CADNC,IAAI,GAAA,KAAA,CAAA,CAAA;AAEV,IAAA,IAAI,CAACA,IAAI,GAAG,IAAIC,OAAO,EAAE,CAAA;AAC3B,GAAA;EAEAC,GAAGA,CAACC,GAAiB,EAAE;AACrB,IAAA,OAAO,IAAI,CAACH,IAAI,CAACE,GAAG,CAACC,GAAG,CAAC,CAAA;AAC3B,GAAA;EAEAC,GAAGA,CAACD,GAAiB,EAAE;AACrB,IAAA,IAAI,CAAC,IAAI,CAACD,GAAG,CAACC,GAAG,CAAC,EAAE,OAAA;IAEpB,MAAME,MAAM,GAAG,IAAI,CAACL,IAAI,CAACI,GAAG,CAACD,GAAG,CAAC,CAAA;IACjC,MAAM;AAAEG,MAAAA,KAAAA;AAAM,KAAC,GAAGD,MAAM,CAAA;IAExBA,MAAM,CAACE,KAAK,EAAE,CAAA;AACd,IAAA,IAAIF,MAAM,CAACE,KAAK,KAAK,CAAC,EAAE;AAGtB,MAAA,OAAO5B,oBAAoB,CAAC,GAAG,EAAE2B,KAAK,EAAEH,GAAG,CAAC,CAAA;AAC9C,KAAA;AACA,IAAA,OAAOG,KAAK,CAAA;AACd,GAAA;AAEAE,EAAAA,GAAGA,CAACL,GAAiB,EAAEG,KAAmB,EAAEC,KAAa,EAAE;AACzD,IAAA,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAACL,GAAG,EAAE;MAAEI,KAAK;AAAED,MAAAA,KAAAA;AAAM,KAAC,CAAC,CAAA;AAC7C,GAAA;AACF,CAAA;AAEA,SAASG,aAAaA,CACpB9C,IAA4B,EAC5B+C,IAAkB,EACJ;EACd,MAAM;AAAE7C,IAAAA,IAAAA;AAAK,GAAC,GAAGF,IAAI,CAAA;AACrB,EAAA,IAAIyB,0BAA0B,CAACvB,IAAI,CAAC,EAAE;IACpC,OAAO0B,gBAAgB,CAACmB,IAAI,EAAE7C,IAAI,CAAC8C,QAAQ,EAAE9C,IAAI,CAAC+C,QAAQ,CAAC,CAAA;AAC7D,GAAA;AAEA,EAAA,IAAIjD,IAAI,CAACwB,wBAAwB,EAAE,EAAE;AACnC,IAAA,MAAM0B,MAAM,GAAGlD,IAAI,CAACyC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACjC,IAAIzC,IAAI,CAACE,IAAI,CAACiD,QAAQ,IAAID,MAAM,CAACzB,0BAA0B,EAAE,EAAE;AAE7D,MAAA,MAAM2B,MAAM,GAAGF,MAAM,CAAChD,IAAI,CAACkD,MAAiC,CAAA;MAC5D,MAAMC,OAAO,GAAGrD,IAAI,CAACsD,KAAK,CAACC,qBAAqB,CAACH,MAAM,CAAC,CAAA;AACxDF,MAAAA,MAAM,CACHT,GAAG,CAAC,QAAQ,CAAC,CACbe,WAAW,CAACxC,oBAAoB,CAAC,GAAG,EAAEqC,OAAO,EAAED,MAAM,CAAC,CAAC,CAAA;MAE1D,OAAOjC,cAAc,CAACS,gBAAgB,CAACmB,IAAI,EAAEzB,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAChE+B,OAAO,EACP,GAAGrD,IAAI,CAACE,IAAI,CAACuD,SAAS,CACvB,CAAC,CAAA;AACJ,KAAA;IAEA,OAAOtC,cAAc,CAAC4B,IAAI,EAAE/C,IAAI,CAACE,IAAI,CAACuD,SAAS,CAAC,CAAA;AAClD,GAAA;EAEA,OAAOzD,IAAI,CAACE,IAAI,CAAA;AAClB,CAAA;AAMA,SAASwD,gBAAgBA,CAAC1D,IAAc,EAAE;AACxC,EAAA,OAAOA,IAAI,EAAE;AACX,IAAA,IAAIA,IAAI,CAAC2D,SAAS,EAAE,EAAE,MAAA;IAEtB,MAAM;MAAExD,UAAU;MAAEyD,SAAS;AAAEC,MAAAA,OAAAA;AAAQ,KAAC,GAAG7D,IAAI,CAAA;AAC/C,IAAA,MAAM8D,UAAU,GAAG3D,UAAU,CAACD,IAAI,CAAA;AAClC,IAAA,IAAI2D,OAAO,EAAE;AACX,MAAA,IACED,SAAS,KAETE,UAAU,CAACD,OAAO,CAAC,EACnB;AACA,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAC,MAAM;AACL,MAAA,IAAID,SAAS,KAAKE,UAAU,EAAE,OAAO,IAAI,CAAA;AAC3C,KAAA;AAEA9D,IAAAA,IAAI,GAAGG,UAAU,CAAA;AACnB,GAAA;AAEA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAIA,MAAM4D,MAAM,GAAG;EACbC,OAAOA,GAAG,EAET;AAEDD,EAAAA,MAAMA,CAAqBE,MAAc,EAAEC,aAAsB,EAAE;IACjE,MAAM;MAAEhE,IAAI;MAAEiE,MAAM;MAAEhE,UAAU;AAAEmD,MAAAA,KAAAA;AAAM,KAAC,GAAGW,MAAM,CAAA;AAElD,IAAA,IAAIA,MAAM,CAACxC,0BAA0B,EAAE,EAAE;AAEvC,MAAA,IAAIiC,gBAAgB,CAACO,MAAM,CAAC,EAAE,OAAA;AAW9B,MAAA,MAAMG,OAAO,GAAGH,MAAM,CAACI,IAAI,CAAC,CAAC;QAAEnE,IAAI;AAAEiE,QAAAA,MAAAA;AAAO,OAAC,KAAK;AAChD,QAAA,IAAI1C,0BAA0B,CAAC0C,MAAM,CAAC,EAAE;UAItC,OAAOA,MAAM,CAAChB,QAAQ,IAAIgB,MAAM,CAACf,MAAM,KAAKlD,IAAI,CAAA;AAClD,SAAA;AACA,QAAA,IAAIsB,wBAAwB,CAAC2C,MAAM,CAAC,EAAE;AAIpC,UAAA,OAEGjE,IAAI,KAAK+D,MAAM,CAAC/D,IAAI,IAAIiE,MAAM,CAAChB,QAAQ,IAAKgB,MAAM,CAACjB,MAAM,KAAKhD,IAAI,CAAA;AAEvE,SAAA;AACA,QAAA,OAAO,IAAI,CAAA;AACb,OAAC,CAAyC,CAAA;AAK1C,MAAA,IAAIoD,KAAK,CAACtD,IAAI,CAACsE,SAAS,EAAE,EAAE;AAC1BF,QAAAA,OAAO,CAACZ,WAAW,CAEjBrC,cAAc,CAACJ,uBAAuB,CAAC,EAAE,EAAEqD,OAAO,CAAClE,IAAI,CAAC,EAAE,EAAE,CAC9D,CAAC,CAAA;AACD,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,MAAMqE,wBAAwB,GAAGxE,qBAAqB,CAACqE,OAAO,CAAC,CAAA;AAE/D,MAAA,MAAMI,cAAc,GAAGJ,OAAO,CAACjE,UAAU,CAAA;MACzC,IAAIqE,cAAc,CAAC9C,kBAAkB,CAAC;AAAE+C,QAAAA,QAAQ,EAAEvE,IAAAA;AAAK,OAAC,CAAC,EAAE;AACzD,QAAA,MAAM+D,MAAM,CAACS,mBAAmB,CAAC,gCAAgC,CAAC,CAAA;AACpE,OAAA;AACA,MAAA,MAAMC,YAAY,GAAGH,cAAc,CAACI,sBAAsB,CAAC;QACzDC,IAAI,EAAET,OAAO,CAAClE,IAAAA;AAChB,OAAC,CAAC,CAAA;AACF,MAAA,MAAM4E,iBAAiB,GAAGN,cAAc,CAAC5D,iBAAiB,CAAC;AACzDP,QAAAA,QAAQ,EAAE,QAAA;AACZ,OAAC,CAAC,CAAA;AACF,MAAA,IACEyE,iBAAiB,IACjBV,OAAO,CAAC3C,0BAA0B,EAAE,IACpC2C,OAAO,CAAC3B,GAAG,CAAC,UAAU,CAAC,CAACsC,aAAa,EAAE,EACvC;AAGA,QAAA,MAAMd,MAAM,CAACS,mBAAmB,CAC9B,sCACF,CAAC,CAAA;AACH,OAAA;MAUA,IAAIM,gBAAwC,GAAGf,MAAM,CAAA;MACrD,SAAS;AACP,QAAA,IAAIe,gBAAgB,CAACvD,0BAA0B,EAAE,EAAE;AACjD,UAAA,IAAIuD,gBAAgB,CAAC9E,IAAI,CAACiD,QAAQ,EAAE,MAAA;AACpC6B,UAAAA,gBAAgB,GAAGA,gBAAgB,CAACvC,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjD,UAAA,SAAA;AACF,SAAC,MAAM,IAAIuC,gBAAgB,CAACxD,wBAAwB,EAAE,EAAE;AACtD,UAAA,IAAIwD,gBAAgB,CAAC9E,IAAI,CAACiD,QAAQ,EAAE,MAAA;AACpC6B,UAAAA,gBAAgB,GAAGA,gBAAgB,CAACvC,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjD,UAAA,SAAA;AACF,SAAA;QAEA,MAAM,IAAIwC,KAAK,CACb,CAA8BD,2BAAAA,EAAAA,gBAAgB,CAAC9E,IAAI,CAACgF,IAAI,CAAA,CAC1D,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,MAAMC,YAAY,GAAGH,gBAAgB,CAACvD,0BAA0B,EAAE,GAC9DuD,gBAAgB,CAAC9E,IAAI,CAACkD,MAAM,GAC5B4B,gBAAgB,CAAC9E,IAAI,CAACgD,MAAM,CAAA;AAChC,MAAA,MAAMkC,iBAAiB,GAAG9B,KAAK,CAACC,qBAAqB,CAAC4B,YAAY,CAAC,CAAA;AACnE,MAAA,MAAME,OAAO,GAAGD,iBAAiB,IAAjBA,IAAAA,GAAAA,iBAAiB,GAAID,YAAY,CAAA;AAIjD,MAAA,MAAMG,oBAAoB,GAAGnF,UAAU,CAACqB,wBAAwB,CAAC;AAC/D0B,QAAAA,MAAM,EAAEhD,IAAAA;AACV,OAAC,CAAC,CAAA;AAIF,MAAA,MAAMqF,cAAc,GAClBpB,MAAc,IACyBmB,oBAAoB,CAAA;AAE7D,MAAA,MAAME,YAAY,GAAGrF,UAAU,CAACsF,gBAAgB,CAAC;AAAEvC,QAAAA,MAAM,EAAEhD,IAAAA;AAAK,OAAC,CAAC,CAAA;MAClE8E,gBAAgB,CAACxB,WAAW,CAACV,aAAa,CAACkC,gBAAgB,EAAEK,OAAO,CAAC,CAAC,CAAA;AACtE,MAAA,IAAIE,cAAc,CAAO,CAAC,EAAE;QAC1B,IAAIpB,MAAM,CAAChB,QAAQ,EAAE;AACnBhD,UAAAA,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACkC,YAAY,CAACzB,MAAM,EAAEE,MAAM,CAACV,SAAS,CAAC,CAAC,CAAA;AACrE,SAAC,MAAM;AACLtD,UAAAA,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACmC,IAAI,CAAC1B,MAAM,EAAEE,MAAM,CAACV,SAAS,CAAC,CAAC,CAAA;AAC7D,SAAA;OACD,MAAM,IAAI+B,YAAY,EAAE;QAEvBvB,MAAM,CAACT,WAAW,CAAC,IAAI,CAACoC,QAAQ,CAAC3B,MAAM,CAAC,CAAC,CAAA;OAC1C,MAAM,IAC4B,IAAI,CAAC4B,MAAM,IAC5C1F,UAAU,CAACS,iBAAiB,CAAC;AAAEP,QAAAA,QAAQ,EAAE,QAAA;AAAS,OAAC,CAAC,EACpD;QACAF,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACqC,MAAM,CAAC5B,MAAM,CAAC,CAAC,CAAA;AAC7C,OAAC,MAAM,IAAI9D,UAAU,CAACyE,sBAAsB,EAAE,EAAE;AAE9CkB,QAAAA,gBAAgB,CAAC,IAAI,EAAE7B,MAAM,EAAE9D,UAAU,CAAC,CAAA;AAC5C,OAAC,MAAM;QACL8D,MAAM,CAACT,WAAW,CAAC,IAAI,CAACf,GAAG,CAACwB,MAAM,CAAC,CAAC,CAAA;AACtC,OAAA;AAEA,MAAA,IAAI8B,OAAqB,GAAG9B,MAAM,CAAC/D,IAAI,CAAA;MACvC,KAAK,IAAI8F,OAAiB,GAAG/B,MAAM,EAAE+B,OAAO,KAAK5B,OAAO,GAAI;AAC1D,QAAA,MAAMjE,UAAU,GAAG6F,OAAO,CAAC7F,UAAoC,CAAA;AAE/D,QAAA,IACEA,UAAU,KAAKiE,OAAO,IACtBmB,cAAc,CAAO,CAAC,IACtBpB,MAAM,CAAChB,QAAQ,EACf;UACA4C,OAAO,GAAG5F,UAAU,CAACD,IAAI,CAAA;AACzB,UAAA,MAAA;AACF,SAAA;AACA6F,QAAAA,OAAO,GAAGjD,aAAa,CAAC3C,UAAU,EAAE4F,OAAO,CAAC,CAAA;AAC5CC,QAAAA,OAAO,GAAG7F,UAAU,CAAA;AACtB,OAAA;AAEA,MAAA,IAAIkD,OAAqB,CAAA;AACzB,MAAA,MAAM4C,aAAa,GAAG7B,OAAO,CAACjE,UAAoC,CAAA;MAClE,IACEoB,kBAAkB,CAACwE,OAAO,CAAC,IAC3BE,aAAa,CAACzE,wBAAwB,CAAC;QACrC0B,MAAM,EAAEkB,OAAO,CAAClE,IAAI;AACpBiD,QAAAA,QAAQ,EAAE,IAAA;AACZ,OAAC,CAAC,EACF;QACA,MAAM;AAAEC,UAAAA,MAAAA;AAAO,SAAC,GAAG2C,OAAO,CAAA;QAC1B1C,OAAO,GAAGY,MAAM,CAACX,KAAK,CAACC,qBAAqB,CAACH,MAAM,CAAC,CAAA;AACpD,QAAA,IAAIC,OAAO,EAAE;UACX0C,OAAO,CAAC3C,MAAM,GAAGpC,oBAAoB,CACnC,GAAG,EACHqC,OAAO,EAGPD,MACF,CAAC,CAAA;AACH,SAAA;AACF,OAAA;MAEA,IAAI8C,eAAyB,GAAG9B,OAAO,CAAA;MACvC,IAAIU,iBAAiB,IAAIH,YAAY,EAAE;AACrCuB,QAAAA,eAAe,GAAGD,aAAa,CAAA;QAC/BF,OAAO,GAAGE,aAAa,CAAC/F,IAAI,CAAA;AAC9B,OAAA;MAEA,MAAMiG,YAAY,GAAGf,iBAAiB,GAClCpE,oBAAoB,CAClB,GAAG,EAEHI,SAAS,CAACiE,OAAuB,CAAC,EAClCjE,SAAS,CAAC+D,YAAY,CACxB,CAAC,GACD/D,SAAS,CAACiE,OAAO,CAAC,CAAA;AAEtB,MAAA,IAAId,wBAAwB,EAAE;AAC5B,QAAA,IAAI6B,eAAe,CAAA;AACnB,QAAA,IAAIlC,aAAa,EAAE;UACjBkC,eAAe,GAAGnF,gBAAgB,CAAC,IAAI,EAAEkF,YAAY,EAAEtE,WAAW,EAAE,CAAC,CAAA;AACvE,SAAC,MAAM;AACLuE,UAAAA,eAAe,GAAGzE,iBAAiB,CACjC,IAAI,EACJV,gBAAgB,CAAC,KAAK,EAAEkF,YAAY,EAAEtE,WAAW,EAAE,CAAC,EACpDZ,gBAAgB,CACd,KAAK,EACLG,SAAS,CAACiE,OAAO,CAAC,EAClB/B,KAAK,CAAC+C,kBAAkB,EAC1B,CACF,CAAC,CAAA;AACH,SAAA;QACAH,eAAe,CAAC1C,WAAW,CACzB7B,iBAAiB,CAAC,IAAI,EAAEyE,eAAe,EAAEL,OAAO,CAClD,CAAC,CAAA;AACH,OAAC,MAAM;AACL,QAAA,IAAIO,YAAY,CAAA;AAChB,QAAA,IAAIpC,aAAa,EAAE;UACjBoC,YAAY,GAAGrF,gBAAgB,CAAC,IAAI,EAAEkF,YAAY,EAAEtE,WAAW,EAAE,CAAC,CAAA;AACpE,SAAC,MAAM;AACLyE,UAAAA,YAAY,GAAG3E,iBAAiB,CAC9B,IAAI,EACJV,gBAAgB,CAAC,KAAK,EAAEkF,YAAY,EAAEtE,WAAW,EAAE,CAAC,EACpDZ,gBAAgB,CACd,KAAK,EACLG,SAAS,CAACiE,OAAO,CAAC,EAClB/B,KAAK,CAAC+C,kBAAkB,EAC1B,CACF,CAAC,CAAA;AACH,SAAA;QAEAH,eAAe,CAAC1C,WAAW,CACzBnC,qBAAqB,CACnBiF,YAAY,EACZxB,iBAAiB,GACb5D,cAAc,CAAC,IAAI,CAAC,GACpBoC,KAAK,CAAC+C,kBAAkB,EAAE,EAC9BN,OACF,CACF,CAAC,CAAA;AACH,OAAA;AAGA,MAAA,IAAI1C,OAAO,EAAE;AACX,QAAA,MAAMkD,SAAS,GAAGN,aAAa,CAAC/F,IAAgC,CAAA;AAChE+F,QAAAA,aAAa,CAACzC,WAAW,CACvB1B,sBAAsB,CACpBC,wBAAwB,CACtBwE,SAAS,CAACrD,MAAM,EAChB5B,UAAU,CAAC,MAAM,CAAC,EAClB,KAAK,EACL,IACF,CAAC,EACD,CAACF,SAAS,CAACiC,OAAO,CAAC,EAAE,GAAGkD,SAAS,CAAC9C,SAAS,CAAC,EAC5C,KACF,CACF,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,OAAA;AACF,KAAA;IAIA,IAAI/B,kBAAkB,CAACyC,MAAM,EAAE;AAAEM,MAAAA,QAAQ,EAAEvE,IAAAA;AAAK,KAAC,CAAC,EAAE;MAClD,IAAI,IAAI,CAACsG,SAAS,EAAE;QAClBvC,MAAM,CAACT,WAAW,CAAC,IAAI,CAACgD,SAAS,CAACvC,MAAM,CAAC,CAAC,CAAA;AAC1C,QAAA,OAAA;AACF,OAAA;MAEA,MAAM;QAAE5D,QAAQ;AAAEoG,QAAAA,MAAAA;AAAO,OAAC,GAAGtC,MAAM,CAAA;AAKnC,MAAA,IAAI,CAACH,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC,CAAA;AAEvB,MAAA,MAAMyC,GAAG,GAAGpD,KAAK,CAACqD,gCAAgC,CAACzG,IAAI,CAAC,CAAA;MACxDoD,KAAK,CAACsD,IAAI,CAAC;AAAEC,QAAAA,EAAE,EAAEH,GAAAA;AAAI,OAAC,CAAC,CAAA;AAEvB,MAAA,MAAMI,GAAmB,GAAG,CAE1B9F,oBAAoB,CAAC,GAAG,EAAEI,SAAS,CAACsF,GAAG,CAAC,EAAE,IAAI,CAACjE,GAAG,CAACwB,MAAM,CAAC,CAAC,CAC5D,CAAA;AAED,MAAA,IAAIwC,MAAM,EAAE;AACVK,QAAAA,GAAG,CAACF,IAAI,CAAC3E,gBAAgB,CAAC5B,QAAQ,EAAEe,SAAS,CAACsF,GAAG,CAAC,EAAED,MAAM,CAAC,CAAC,CAAA;AAG5D,QAAA,MAAM9D,KAAK,GAAGX,kBAAkB,CAAC8E,GAAG,CAAC,CAAA;QACrC3G,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACX,GAAG,CAACoB,MAAM,EAAEtB,KAAK,CAAC,CAAC,CAAA;AAE/C,QAAA,OAAA;AACF,OAAC,MAAM;AACL,QAAA,MAAMoE,IAAI,GAAGzD,KAAK,CAACqD,gCAAgC,CAACzG,IAAI,CAAC,CAAA;QACzDoD,KAAK,CAACsD,IAAI,CAAC;AAAEC,UAAAA,EAAE,EAAEE,IAAAA;AAAK,SAAC,CAAC,CAAA;AAExBD,QAAAA,GAAG,CAACF,IAAI,CACN5F,oBAAoB,CAClB,GAAG,EACHI,SAAS,CAAC2F,IAAI,CAAC,EACf9E,gBAAgB,CAAC5B,QAAQ,EAAEe,SAAS,CAACsF,GAAG,CAAC,EAAED,MAAM,CACnD,CAAC,EACDrF,SAAS,CAACsF,GAAG,CACf,CAAC,CAAA;AAGD,QAAA,MAAM/D,KAAK,GAAGX,kBAAkB,CAAC8E,GAAG,CAAC,CAAA;QACrC3G,UAAU,CAACqD,WAAW,CACpBxB,kBAAkB,CAAC,CAAC,IAAI,CAACa,GAAG,CAACoB,MAAM,EAAEtB,KAAK,CAAC,EAAEvB,SAAS,CAAC2F,IAAI,CAAC,CAAC,CAC/D,CAAC,CAAA;AAED,QAAA,OAAA;AACF,OAAA;AACF,KAAA;IAKA,IAAI5G,UAAU,CAACyE,sBAAsB,CAAC;AAAEC,MAAAA,IAAI,EAAE3E,IAAAA;AAAK,KAAC,CAAC,EAAE;AACrD4F,MAAAA,gBAAgB,CAAC,IAAI,EAAE7B,MAAM,EAAE9D,UAAU,CAAC,CAAA;AAC1C,MAAA,OAAA;AACF,KAAA;IAGA,IAAIA,UAAU,CAACsF,gBAAgB,CAAC;AAAEvC,MAAAA,MAAM,EAAEhD,IAAAA;AAAK,KAAC,CAAC,EAAE;AACjDC,MAAAA,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACmC,IAAI,CAAC1B,MAAM,EAAE9D,UAAU,CAACD,IAAI,CAACuD,SAAS,CAAC,CAAC,CAAA;AACpE,MAAA,OAAA;AACF,KAAA;IAGA,IAAItD,UAAU,CAACqB,wBAAwB,CAAC;AAAE0B,MAAAA,MAAM,EAAEhD,IAAAA;AAAK,KAAC,CAAC,EAAE;AAIzD,MAAA,IAAIoD,KAAK,CAACtD,IAAI,CAACsE,SAAS,EAAE,EAAE;AAC1BnE,QAAAA,UAAU,CAACqD,WAAW,CAEpBrC,cAAc,CAACJ,uBAAuB,CAAC,EAAE,EAAEZ,UAAU,CAACD,IAAI,CAAC,EAAE,EAAE,CACjE,CAAC,CAAA;AACD,QAAA,OAAA;AACF,OAAA;AACAC,MAAAA,UAAU,CAACqD,WAAW,CACpB,IAAI,CAACkC,YAAY,CAACzB,MAAM,EAAE9D,UAAU,CAACD,IAAI,CAACuD,SAAS,CACrD,CAAC,CAAA;AACD,MAAA,OAAA;AACF,KAAA;AAGA,IAAA,IACmC,IAAI,CAACoC,MAAM,IAC5C1F,UAAU,CAACS,iBAAiB,CAAC;AAAEP,MAAAA,QAAQ,EAAE,QAAA;AAAS,KAAC,CAAC,EACpD;MACAF,UAAU,CAACqD,WAAW,CAAC,IAAI,CAACqC,MAAM,CAAC5B,MAAM,CAAC,CAAC,CAAA;AAC3C,MAAA,OAAA;AACF,KAAA;IAWA,IAGE9D,UAAU,CAAC6G,eAAe,CAAC;AAAEnC,MAAAA,IAAI,EAAE3E,IAAAA;AAAK,KAAC,CAAC,IAEzCC,UAAU,CAAC8G,gBAAgB,CAAC;AAAEtE,MAAAA,KAAK,EAAEzC,IAAAA;AAAK,KAAC,CAAC,IAC3CC,UAAU,CAACA,UAAU,CAAC+G,eAAe,EAAG,IAEzC/G,UAAU,CAACgH,mBAAmB,CAAC;AAAEtC,MAAAA,IAAI,EAAE3E,IAAAA;AAAK,KAAC,CAAC,IAC7CC,UAAU,CAACA,UAAU,CAAC8G,gBAAgB,CAAC;AAAEtE,MAAAA,KAAK,EAAEwB,MAAAA;KAAQ,CAAC,IACzDhE,UAAU,CAACA,UAAU,CAACA,UAAU,CAAC+G,eAAe,EAAG,IAErD/G,UAAU,CAACiH,cAAc,EAAE,IAE1BjH,UAAU,CAACgH,mBAAmB,CAAC;AAAEtC,MAAAA,IAAI,EAAE3E,IAAAA;AAAK,KAAC,CAAC,IAC7CC,UAAU,CAACA,UAAU,CAACiH,cAAc,EAAG,IAGzCjH,UAAU,CAACkH,aAAa,EAAE,EAC1B;MACApD,MAAM,CAACT,WAAW,CAAC,IAAI,CAAC8D,cAAc,CAACrD,MAAM,CAAC,CAAC,CAAA;AAC/C,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAI9D,UAAU,CAACoH,0BAA0B,EAAE,EAAE;MAE3CtD,MAAM,CAACT,WAAW,CAAC,IAAI,CAACoC,QAAQ,CAAC3B,MAAM,CAAC,CAAC,CAAA;AAC3C,KAAC,MAAM;MAELA,MAAM,CAACT,WAAW,CAAC,IAAI,CAACf,GAAG,CAACwB,MAAM,CAAC,CAAC,CAAA;AACtC,KAAA;AACF,GAAA;AACF,CAAC,CAAA;AAED,SAAS6B,gBAAgBA,CACvB0B,KAAmB,EACnBvD,MAAiE,EACjE9D,UAA4C,EAC5C;EACA,IAAIqH,KAAK,CAAChB,SAAS,EAAE;IACnBvC,MAAM,CAACT,WAAW,CAACgE,KAAK,CAAChB,SAAS,CAACvC,MAAM,CAAC,CAAC,CAAA;AAC3C,IAAA,OAAA;AACF,GAAA;EAEA,MAAM;IAAE5D,QAAQ;AAAEC,IAAAA,KAAK,EAAEqC,KAAAA;GAAO,GAAGxC,UAAU,CAACD,IAAI,CAAA;EAElD,IAAIG,QAAQ,KAAK,GAAG,EAAE;IACpBF,UAAU,CAACqD,WAAW,CAACgE,KAAK,CAAC3E,GAAG,CAACoB,MAAM,EAAEtB,KAAK,CAAC,CAAC,CAAA;AAClD,GAAC,MAAM;IACL,MAAM8E,aAAa,GAAGpH,QAAQ,CAACqH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAC3C,IAAA,IAAI5G,iBAAiB,CAAC6G,QAAQ,CAACF,aAAa,CAAC,EAAE;AAI7CD,MAAAA,KAAK,CAACxD,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC,CAAA;MACxB9D,UAAU,CAACqD,WAAW,CACpB7B,iBAAiB,CACf8F,aAAa,EACbD,KAAK,CAAC/E,GAAG,CAACwB,MAAM,CAAC,EACjBuD,KAAK,CAAC3E,GAAG,CAACoB,MAAM,EAAEtB,KAAK,CACzB,CACF,CAAC,CAAA;AACH,KAAC,MAAM;AAEL6E,MAAAA,KAAK,CAACxD,OAAO,CAACC,MAAM,EAAE,CAAC,CAAC,CAAA;MACxB9D,UAAU,CAACqD,WAAW,CACpBgE,KAAK,CAAC3E,GAAG,CACPoB,MAAM,EACNhD,gBAAgB,CACdwG,aAAa,EACbD,KAAK,CAAC/E,GAAG,CAACwB,MAAM,CAAC,EACjBtB,KACF,CACF,CACF,CAAC,CAAA;AACH,KAAA;AACF,GAAA;AACF,CAAA;AAiDe,SAASiF,2BAA2BA,CACjD5H,IAAc,EACd6H,OAA2C,EAC3CL,KAAyC,EACzC;EACAxH,IAAI,CAAC8H,QAAQ,CAACD,OAAO,EAAAE,MAAA,CAAAC,MAAA,CAAA,EAAA,EAChBjE,MAAM,EACNyD,KAAK,EAAA;IACRS,QAAQ,EAAE,IAAI9F,kBAAkB,EAAC;AAAC,GAAA,CACnC,CAAC,CAAA;AACJ;;;;"}