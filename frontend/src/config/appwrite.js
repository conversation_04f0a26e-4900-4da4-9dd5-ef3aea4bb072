import { Client, Account, Storage, Databases } from 'appwrite';

console.log('Appwrite config: Environment variables:', {
  VITE_APPWRITE_ENDPOINT: import.meta.env.VITE_APPWRITE_ENDPOINT,
  VITE_APPWRITE_PROJECT_ID: import.meta.env.VITE_APPWRITE_PROJECT_ID,
  VITE_APPWRITE_BUCKET_ID: import.meta.env.VITE_APPWRITE_BUCKET_ID,
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL
});

// Appwrite configuration
const client = new Client()
  .setEndpoint(import.meta.env.VITE_APPWRITE_ENDPOINT || 'https://api.avehubs.com/v1')
  .setProject(import.meta.env.VITE_APPWRITE_PROJECT_ID);

console.log('Appwrite config: Client configured with:', {
  endpoint: import.meta.env.VITE_APPWRITE_ENDPOINT || 'https://api.avehubs.com/v1',
  projectId: import.meta.env.VITE_APPWRITE_PROJECT_ID
});

// Initialize services
export const account = new Account(client);
export const storage = new Storage(client);
export const databases = new Databases(client);

// Configuration
export const config = {
  appwrite: {
    endpoint: import.meta.env.VITE_APPWRITE_ENDPOINT || 'https://api.avehubs.com/v1',
    projectId: import.meta.env.VITE_APPWRITE_PROJECT_ID,
    bucketId: import.meta.env.VITE_APPWRITE_BUCKET_ID,
    databaseId: import.meta.env.VITE_APPWRITE_DATABASE_ID,
    collections: {
      images: import.meta.env.VITE_APPWRITE_IMAGES_COLLECTION_ID || 'images',
      users: import.meta.env.VITE_APPWRITE_USERS_COLLECTION_ID || 'users'
    }
  },
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api'
  }
};

export default client;
