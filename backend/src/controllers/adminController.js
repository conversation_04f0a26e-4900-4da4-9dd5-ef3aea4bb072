import { UserService } from '../services/userService.js';
import { StorageService } from '../services/storageService.js';
import { users } from '../config/appwrite.js';

// Get admin dashboard overview
export const getAdminOverview = async (req, res) => {
  try {
    const stats = await UserService.getSystemStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get admin overview error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get admin overview'
    });
  }
};

// Get all users for admin panel
export const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 25, search = '' } = req.query;
    const offset = (page - 1) * limit;

    const result = await UserService.getAllUsersWithProfiles(
      parseInt(limit),
      parseInt(offset)
    );

    // Filter by search if provided
    let filteredUsers = result.users;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = result.users.filter(userWithProfile => 
        userWithProfile.user?.name?.toLowerCase().includes(searchLower) ||
        userWithProfile.user?.email?.toLowerCase().includes(searchLower)
      );
    }

    res.json({
      success: true,
      data: {
        users: filteredUsers,
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(result.total / limit)
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get users'
    });
  }
};

// Update user by admin
export const updateUserByAdmin = async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    // Validate userId
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Update user profile
    const allowedUpdates = ['credits', 'storageLimit', 'isAdmin', 'embedFooter', 'embedColor'];
    const profileUpdates = {};

    for (const key of allowedUpdates) {
      if (updates[key] !== undefined) {
        profileUpdates[key] = updates[key];
      }
    }

    let updatedProfile = null;
    if (Object.keys(profileUpdates).length > 0) {
      updatedProfile = await UserService.updateUserProfile(userId, profileUpdates);
    }

    // Update user account info if provided
    let updatedUser = null;
    if (updates.name !== undefined) {
      // Note: We need to use a session-based approach for this
      // For now, we'll skip updating the name via admin
    }

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        profile: updatedProfile
      }
    });
  } catch (error) {
    console.error('Update user by admin error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user'
    });
  }
};

// Delete user by admin
export const deleteUserByAdmin = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Delete user from Appwrite
    await users.delete(userId);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user by admin error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete user'
    });
  }
};

// Grant/revoke admin status
export const toggleAdminStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const { isAdmin } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    if (typeof isAdmin !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'isAdmin must be a boolean value'
      });
    }

    const updatedProfile = await UserService.updateUserProfile(userId, {
      isAdmin
    });

    res.json({
      success: true,
      message: `User ${isAdmin ? 'granted' : 'revoked'} admin status`,
      data: updatedProfile
    });
  } catch (error) {
    console.error('Toggle admin status error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update admin status'
    });
  }
};

// Add credits to user
export const addCreditsToUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, reason = 'Admin credit adjustment' } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    if (!amount || typeof amount !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'Valid amount is required'
      });
    }

    const updatedProfile = await UserService.updateCredits(userId, amount);

    res.json({
      success: true,
      message: `${amount > 0 ? 'Added' : 'Removed'} ${Math.abs(amount)} credits`,
      data: {
        credits: updatedProfile.credits,
        reason
      }
    });
  } catch (error) {
    console.error('Add credits to user error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update user credits'
    });
  }
};

// Update user storage limit
export const updateUserStorageLimit = async (req, res) => {
  try {
    const { userId } = req.params;
    const { storageLimitMB } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    if (!storageLimitMB || storageLimitMB <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Valid storage limit is required'
      });
    }

    const storageLimitBytes = storageLimitMB * 1024 * 1024;
    const updatedProfile = await UserService.updateUserProfile(userId, {
      storageLimit: storageLimitBytes
    });

    res.json({
      success: true,
      message: `Updated storage limit to ${storageLimitMB}MB`,
      data: {
        storageLimit: updatedProfile.storageLimit,
        storageLimitMB
      }
    });
  } catch (error) {
    console.error('Update user storage limit error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update storage limit'
    });
  }
};

// Storage Plans Management

// Get all storage plans
export const getAllStoragePlans = async (req, res) => {
  try {
    const { activeOnly = false } = req.query;
    const plans = await StorageService.getAllStoragePlans(activeOnly === 'true');

    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Get storage plans error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get storage plans'
    });
  }
};

// Create storage plan
export const createStoragePlan = async (req, res) => {
  try {
    const { name, description, storageAmountMB, creditCost, includesFooterCustomization } = req.body;

    if (!name || !storageAmountMB || !creditCost) {
      return res.status(400).json({
        success: false,
        error: 'Name, storage amount, and credit cost are required'
      });
    }

    const plan = await StorageService.createStoragePlan({
      name,
      description,
      storageAmountMB: parseInt(storageAmountMB),
      creditCost: parseInt(creditCost),
      includesFooterCustomization: includesFooterCustomization === true
    });

    res.json({
      success: true,
      message: 'Storage plan created successfully',
      data: plan
    });
  } catch (error) {
    console.error('Create storage plan error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create storage plan'
    });
  }
};

// Update storage plan
export const updateStoragePlan = async (req, res) => {
  try {
    const { planId } = req.params;
    const updates = req.body;

    if (!planId) {
      return res.status(400).json({
        success: false,
        error: 'Plan ID is required'
      });
    }

    const updatedPlan = await StorageService.updateStoragePlan(planId, updates);

    res.json({
      success: true,
      message: 'Storage plan updated successfully',
      data: updatedPlan
    });
  } catch (error) {
    console.error('Update storage plan error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update storage plan'
    });
  }
};

// Delete storage plan
export const deleteStoragePlan = async (req, res) => {
  try {
    const { planId } = req.params;

    if (!planId) {
      return res.status(400).json({
        success: false,
        error: 'Plan ID is required'
      });
    }

    await StorageService.deleteStoragePlan(planId);

    res.json({
      success: true,
      message: 'Storage plan deleted successfully'
    });
  } catch (error) {
    console.error('Delete storage plan error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to delete storage plan'
    });
  }
};

// Get storage statistics
export const getStorageStats = async (req, res) => {
  try {
    const stats = await StorageService.getStorageStats();

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get storage stats error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get storage statistics'
    });
  }
};

// Get all transactions
export const getAllTransactions = async (req, res) => {
  try {
    const { page = 1, limit = 25 } = req.query;
    const offset = (page - 1) * limit;

    const result = await StorageService.getAllTransactions(parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: {
        transactions: result.transactions,
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(result.total / limit)
      }
    });
  } catch (error) {
    console.error('Get all transactions error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get transactions'
    });
  }
};
