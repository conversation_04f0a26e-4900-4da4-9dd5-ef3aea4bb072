'use strict';

var client = require('../client');

class Health {
  constructor(client) {
    this.client = client;
  }
  /**
   * Get HTTP
   *
   * Check the Appwrite HTTP server is up and responsive.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async get() {
    const apiPath = "/health";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get antivirus
   *
   * Check the Appwrite Antivirus server is up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthAntivirus>}
   */
  async getAntivirus() {
    const apiPath = "/health/anti-virus";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get cache
   *
   * Check the Appwrite in-memory cache servers are up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getCache() {
    const apiPath = "/health/cache";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get the SSL certificate for a domain
   *
   * Get the SSL certificate for a domain
   *
   * @param {string} domain
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthCertificate>}
   */
  async getCertificate(domain) {
    const apiPath = "/health/certificate";
    const payload = {};
    if (typeof domain !== "undefined") {
      payload["domain"] = domain;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get DB
   *
   * Check the Appwrite database servers are up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getDB() {
    const apiPath = "/health/db";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get pubsub
   *
   * Check the Appwrite pub-sub servers are up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getPubSub() {
    const apiPath = "/health/pubsub";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get queue
   *
   * Check the Appwrite queue messaging servers are up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getQueue() {
    const apiPath = "/health/queue";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get builds queue
   *
   * Get the number of builds that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueBuilds(threshold) {
    const apiPath = "/health/queue/builds";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get certificates queue
   *
   * Get the number of certificates that are waiting to be issued against [Letsencrypt](https://letsencrypt.org/) in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueCertificates(threshold) {
    const apiPath = "/health/queue/certificates";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get databases queue
   *
   * Get the number of database changes that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {string} name
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueDatabases(name, threshold) {
    const apiPath = "/health/queue/databases";
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get deletes queue
   *
   * Get the number of background destructive changes that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueDeletes(threshold) {
    const apiPath = "/health/queue/deletes";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Get number of failed queue jobs
       *
       * Returns the amount of failed jobs in a given queue.
  
       *
       * @param {Name} name
       * @param {number} threshold
       * @throws {AppwriteException}
       * @returns {Promise<Models.HealthQueue>}
       */
  async getFailedJobs(name, threshold) {
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/health/queue/failed/{name}".replace("{name}", name);
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get functions queue
   *
   * Get the number of function executions that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueFunctions(threshold) {
    const apiPath = "/health/queue/functions";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get logs queue
   *
   * Get the number of logs that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueLogs(threshold) {
    const apiPath = "/health/queue/logs";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get mails queue
   *
   * Get the number of mails that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueMails(threshold) {
    const apiPath = "/health/queue/mails";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get messaging queue
   *
   * Get the number of messages that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueMessaging(threshold) {
    const apiPath = "/health/queue/messaging";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get migrations queue
   *
   * Get the number of migrations that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueMigrations(threshold) {
    const apiPath = "/health/queue/migrations";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get usage queue
   *
   * Get the number of metrics that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueUsage(threshold) {
    const apiPath = "/health/queue/usage";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get usage dump queue
   *
   * Get the number of projects containing metrics that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueUsageDump(threshold) {
    const apiPath = "/health/queue/usage-dump";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get webhooks queue
   *
   * Get the number of webhooks that are waiting to be processed in the Appwrite internal queue server.
   *
   * @param {number} threshold
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthQueue>}
   */
  async getQueueWebhooks(threshold) {
    const apiPath = "/health/queue/webhooks";
    const payload = {};
    if (typeof threshold !== "undefined") {
      payload["threshold"] = threshold;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get storage
   *
   * Check the Appwrite storage device is up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getStorage() {
    const apiPath = "/health/storage";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get local storage
   *
   * Check the Appwrite local storage device is up and connection is successful.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthStatus>}
   */
  async getStorageLocal() {
    const apiPath = "/health/storage/local";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get time
   *
   * Check the Appwrite server time is synced with Google remote NTP server. We use this technology to smoothly handle leap seconds with no disruptive events. The [Network Time Protocol](https://en.wikipedia.org/wiki/Network_Time_Protocol) (NTP) is used by hundreds of millions of computers and devices to synchronize their clocks over the Internet. If your computer sets its own clock, it likely uses NTP.
   *
   * @throws {AppwriteException}
   * @returns {Promise<Models.HealthTime>}
   */
  async getTime() {
    const apiPath = "/health/time";
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
}

exports.Health = Health;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=health.js.map