import { databases, storage } from '../config/appwrite.js';
import { ID } from 'node-appwrite';
import { v4 as uuidv4 } from 'uuid';

class ImageService {
    constructor() {
        this.databaseId = process.env.APPWRITE_DATABASE_ID;
        this.imagesCollectionId = process.env.APPWRITE_IMAGES_COLLECTION_ID;
        this.bucketId = process.env.APPWRITE_BUCKET_ID;
    }

    async uploadImage(file, userId, customSettings = {}) {
        try {
            // Check user's storage quota first
            const user = await databases.getDocument(
                this.databaseId,
                process.env.APPWRITE_USERS_COLLECTION_ID,
                userId
            );

            const fileSizeInMB = file.size / (1024 * 1024);
            
            if (user.storage_used + fileSizeInMB > user.storage_quota) {
                throw new Error('Storage quota exceeded');
            }

            // Upload file to Appwrite storage
            const uploadedFile = await storage.createFile(
                this.bucketId,
                ID.unique(),
                file
            );

            // Generate UUID for image URL
            const imageUuid = uuidv4();
            const imageUrl = `/i/${imageUuid}`;

            // Create image record in database
            const imageRecord = await databases.createDocument(
                this.databaseId,
                this.imagesCollectionId,
                ID.unique(),
                {
                    file_id: uploadedFile.$id,
                    user_id: userId,
                    uuid: imageUuid,
                    image_url: imageUrl,
                    filename: file.name,
                    size: file.size,
                    mimetype: file.type,
                    embed_title: customSettings.title || file.name,
                    embed_description: customSettings.description || '',
                    embed_color: customSettings.color || '#7c3aed',
                    embed_footer: customSettings.footer || '',
                    sharex_enabled: customSettings.sharex_enabled || false,
                    views: 0,
                    uploaded_at: new Date().toISOString()
                }
            );

            // Update user's storage usage
            await databases.updateDocument(
                this.databaseId,
                process.env.APPWRITE_USERS_COLLECTION_ID,
                userId,
                {
                    storage_used: user.storage_used + fileSizeInMB
                }
            );

            return {
                success: true,
                message: 'Image uploaded successfully',
                image: {
                    ...imageRecord,
                    file_url: `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${uploadedFile.$id}/view?project=${process.env.APPWRITE_PROJECT_ID}`,
                    embed_url: `${process.env.FRONTEND_URL}${imageUrl}`,
                    sharex_url: imageUrl
                }
            };

        } catch (error) {
            console.error('Image upload error:', error);
            throw error;
        }
    }

    async getUserImages(userId, limit = 20, offset = 0) {
        try {
            const response = await databases.listDocuments(
                this.databaseId,
                this.imagesCollectionId,
                [
                    `equal("user_id", ["${userId}"])`,
                    `orderDesc("uploaded_at")`,
                    `limit(${limit})`,
                    `offset(${offset})`
                ]
            );

            return {
                success: true,
                images: response.documents.map(doc => ({
                    ...doc,
                    file_url: `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${doc.file_id}/view?project=${process.env.APPWRITE_PROJECT_ID}`,
                    embed_url: `${process.env.FRONTEND_URL}${doc.image_url}`,
                    sharex_url: doc.image_url
                })),
                total: response.total
            };
        } catch (error) {
            console.error('Get user images error:', error);
            throw error;
        }
    }

    // Get image by UUID for embed display
    async getImageByUuid(uuid) {
        try {
            const response = await databases.listDocuments(
                this.databaseId,
                this.imagesCollectionId,
                [`equal("uuid", ["${uuid}"])`]
            );

            if (response.documents.length === 0) {
                return null;
            }

            const imageDoc = response.documents[0];

            // Increment view count
            await databases.updateDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageDoc.$id,
                { views: imageDoc.views + 1 }
            );

            return {
                ...imageDoc,
                file_url: `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${imageDoc.file_id}/view?project=${process.env.APPWRITE_PROJECT_ID}`,
                views: imageDoc.views + 1
            };
        } catch (error) {
            console.error('Get image by UUID error:', error);
            throw error;
        }
    }

    // Get image with URL parameters for manipulation
    async getImageWithParams(uuid, params = {}) {
        try {
            const image = await this.getImageByUuid(uuid);
            if (!image) return null;

            let fileUrl = `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${image.file_id}/view?project=${process.env.APPWRITE_PROJECT_ID}`;

            // Add URL parameters for image manipulation
            const urlParams = new URLSearchParams();
            if (params.width) urlParams.append('width', params.width);
            if (params.height) urlParams.append('height', params.height);
            if (params.quality) urlParams.append('quality', params.quality);
            if (params.format) urlParams.append('output', params.format);

            if (urlParams.toString()) {
                fileUrl += '&' + urlParams.toString();
            }

            return {
                ...image,
                file_url: fileUrl
            };
        } catch (error) {
            console.error('Get image with params error:', error);
            throw error;
        }
    }

    async getImageById(imageId) {
        try {
            const image = await databases.getDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId
            );

            // Increment view count
            await databases.updateDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId,
                {
                    views: image.views + 1
                }
            );

            return {
                success: true,
                image: {
                    ...image,
                    file_url: `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${image.file_id}/view?project=${process.env.APPWRITE_PROJECT_ID}`,
                    embed_url: `${process.env.FRONTEND_URL}/embed/${image.$id}`
                }
            };
        } catch (error) {
            console.error('Get image error:', error);
            throw error;
        }
    }

    async deleteImage(imageId, userId) {
        try {
            const image = await databases.getDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId
            );

            // Check if user owns the image
            if (image.user_id !== userId) {
                throw new Error('Unauthorized');
            }

            // Delete from storage
            await storage.deleteFile(this.bucketId, image.file_id);

            // Delete from database
            await databases.deleteDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId
            );

            // Update user's storage usage
            const user = await databases.getDocument(
                this.databaseId,
                process.env.APPWRITE_USERS_COLLECTION_ID,
                userId
            );

            const fileSizeInMB = image.size / (1024 * 1024);
            await databases.updateDocument(
                this.databaseId,
                process.env.APPWRITE_USERS_COLLECTION_ID,
                userId,
                {
                    storage_used: Math.max(0, user.storage_used - fileSizeInMB)
                }
            );

            return { success: true };
        } catch (error) {
            console.error('Delete image error:', error);
            throw error;
        }
    }

    async updateImageSettings(imageId, userId, settings) {
        try {
            const image = await databases.getDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId
            );

            // Check if user owns the image
            if (image.user_id !== userId) {
                throw new Error('Unauthorized');
            }

            const updatedImage = await databases.updateDocument(
                this.databaseId,
                this.imagesCollectionId,
                imageId,
                {
                    embed_title: settings.title || image.embed_title,
                    embed_description: settings.description || image.embed_description,
                    embed_color: settings.color || image.embed_color,
                    embed_footer: settings.footer || image.embed_footer,
                    sharex_enabled: settings.sharex_enabled !== undefined ? settings.sharex_enabled : image.sharex_enabled
                }
            );

            return {
                success: true,
                image: {
                    ...updatedImage,
                    file_url: `${process.env.APPWRITE_ENDPOINT}/storage/buckets/${this.bucketId}/files/${updatedImage.file_id}/view?project=${process.env.APPWRITE_PROJECT_ID}`,
                    embed_url: `${process.env.FRONTEND_URL}/embed/${updatedImage.$id}`
                }
            };
        } catch (error) {
            console.error('Update image settings error:', error);
            throw error;
        }
    }
}

export default new ImageService();
