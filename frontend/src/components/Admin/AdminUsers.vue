<template>
  <div class="space-y-6">
    <!-- Search and Filters -->
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <Search class="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search users by name or email..."
            class="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
            @input="debouncedSearch"
          />
        </div>
      </div>
      <button @click="loadUsers" :disabled="isLoading"
              class="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors">
        <RefreshCw class="w-4 h-4 inline mr-2" :class="{ 'animate-spin': isLoading }" />
        Refresh
      </button>
    </div>

    <!-- Users Table -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Credits</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Storage</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr v-for="userWithProfile in paginatedUsers" :key="userWithProfile.$id" 
                class="hover:bg-gray-700/50 transition-colors">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {{ getUserInitials(userWithProfile.user?.name) }}
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-white">
                      {{ userWithProfile.user?.name || 'N/A' }}
                      <Crown v-if="userWithProfile.isAdmin" class="w-3 h-3 inline ml-1 text-purple-400" />
                    </div>
                    <div class="text-sm text-gray-400">{{ userWithProfile.user?.email || 'N/A' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-purple-400">{{ userWithProfile.credits }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-300">
                  {{ Math.round(userWithProfile.storageUsed / (1024 * 1024)) }}MB / 
                  {{ Math.round(userWithProfile.storageLimit / (1024 * 1024)) }}MB
                </div>
                <div class="w-20 bg-gray-700 rounded-full h-1 mt-1">
                  <div class="bg-blue-500 h-1 rounded-full" 
                       :style="{ width: `${Math.min(100, (userWithProfile.storageUsed / userWithProfile.storageLimit) * 100)}%` }"></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span v-if="userWithProfile.user" 
                      :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        userWithProfile.user.status ? 'bg-green-900 text-green-200' : 'bg-red-900 text-red-200'
                      ]">
                  {{ userWithProfile.user.status ? 'Active' : 'Inactive' }}
                </span>
                <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-900 text-gray-200">
                  Unknown
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button @click="editUser(userWithProfile)"
                        class="text-purple-400 hover:text-purple-300 transition-colors">
                  <Edit class="w-4 h-4" />
                </button>
                <button @click="toggleAdminStatus(userWithProfile)"
                        class="text-blue-400 hover:text-blue-300 transition-colors"
                        :title="userWithProfile.isAdmin ? 'Remove Admin' : 'Make Admin'">
                  <Shield class="w-4 h-4" />
                </button>
                <button @click="deleteUser(userWithProfile)"
                        class="text-red-400 hover:text-red-300 transition-colors">
                  <Trash2 class="w-4 h-4" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="bg-gray-700 px-6 py-3 flex items-center justify-between">
        <div class="text-sm text-gray-300">
          Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, totalUsers) }} of {{ totalUsers }} users
        </div>
        <div class="flex space-x-2">
          <button @click="currentPage--" :disabled="currentPage <= 1"
                  class="px-3 py-1 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 text-white rounded transition-colors">
            Previous
          </button>
          <span class="px-3 py-1 bg-purple-600 text-white rounded">{{ currentPage }}</span>
          <button @click="currentPage++" :disabled="currentPage >= totalPages"
                  class="px-3 py-1 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 text-white rounded transition-colors">
            Next
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
      <p class="text-gray-400 mt-2">Loading users...</p>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && filteredUsers.length === 0" class="text-center py-12">
      <Users class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-xl font-semibold text-white mb-2">No users found</h3>
      <p class="text-gray-400">{{ searchQuery ? 'Try adjusting your search terms.' : 'No users have been created yet.' }}</p>
    </div>
  </div>

  <!-- Edit User Modal -->
  <Modal v-if="editingUser" @close="editingUser = null">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
      <h3 class="text-xl font-semibold text-white mb-4">Edit User</h3>
      <form @submit.prevent="saveUser" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-400 mb-2">Credits</label>
          <input v-model.number="editForm.credits" type="number" min="0"
                 class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-400 mb-2">Storage Limit (MB)</label>
          <input v-model.number="editForm.storageLimitMB" type="number" min="1"
                 class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white">
        </div>
        <div class="flex justify-end space-x-3">
          <button type="button" @click="editingUser = null"
                  class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
            Cancel
          </button>
          <button type="submit" :disabled="isSaving"
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors">
            {{ isSaving ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </form>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import Modal from '../ui/Modal.vue'
import { 
  Search, RefreshCw, Edit, Shield, Trash2, 
  Crown, Users 
} from 'lucide-vue-next'
import api from '../../config/api.js'
import Swal from 'sweetalert2'

const users = ref([])
const isLoading = ref(false)
const isSaving = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalUsers = ref(0)
const editingUser = ref(null)
const editForm = ref({
  credits: 0,
  storageLimitMB: 50
})

const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  const query = searchQuery.value.toLowerCase()
  return users.value.filter(user => 
    user.user?.name?.toLowerCase().includes(query) ||
    user.user?.email?.toLowerCase().includes(query)
  )
})

const totalPages = computed(() => Math.ceil(filteredUsers.value.length / pageSize.value))

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUsers.value.slice(start, end)
})

const getUserInitials = (name) => {
  if (!name) return 'U'
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
}

const loadUsers = async () => {
  isLoading.value = true
  try {
    const response = await api.get('/admin/users')
    if (response.data.success) {
      users.value = response.data.data.users
      totalUsers.value = response.data.data.total
    }
  } catch (error) {
    console.error('Failed to load users:', error)
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to load users'
    })
  } finally {
    isLoading.value = false
  }
}

const editUser = (user) => {
  editingUser.value = user
  editForm.value = {
    credits: user.credits,
    storageLimitMB: Math.round(user.storageLimit / (1024 * 1024))
  }
}

const saveUser = async () => {
  if (!editingUser.value) return
  
  isSaving.value = true
  try {
    const response = await api.put(`/admin/users/${editingUser.value.userId}`, {
      credits: editForm.value.credits,
      storageLimit: editForm.value.storageLimitMB * 1024 * 1024
    })
    
    if (response.data.success) {
      // Update local data
      const userIndex = users.value.findIndex(u => u.userId === editingUser.value.userId)
      if (userIndex !== -1) {
        users.value[userIndex] = { ...users.value[userIndex], ...response.data.data.profile }
      }
      
      editingUser.value = null
      Swal.fire({
        icon: 'success',
        title: 'Success',
        text: 'User updated successfully',
        timer: 2000,
        showConfirmButton: false
      })
    }
  } catch (error) {
    console.error('Failed to update user:', error)
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to update user'
    })
  } finally {
    isSaving.value = false
  }
}

const toggleAdminStatus = async (user) => {
  const newStatus = !user.isAdmin
  
  try {
    const response = await api.put(`/admin/users/${user.userId}/admin`, {
      isAdmin: newStatus
    })
    
    if (response.data.success) {
      // Update local data
      const userIndex = users.value.findIndex(u => u.userId === user.userId)
      if (userIndex !== -1) {
        users.value[userIndex].isAdmin = newStatus
      }
      
      Swal.fire({
        icon: 'success',
        title: 'Success',
        text: `User ${newStatus ? 'granted' : 'revoked'} admin status`,
        timer: 2000,
        showConfirmButton: false
      })
    }
  } catch (error) {
    console.error('Failed to toggle admin status:', error)
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to update admin status'
    })
  }
}

const deleteUser = async (user) => {
  const result = await Swal.fire({
    icon: 'warning',
    title: 'Delete User',
    text: `Are you sure you want to delete ${user.user?.name || 'this user'}? This action cannot be undone.`,
    showCancelButton: true,
    confirmButtonColor: '#dc2626',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel'
  })
  
  if (!result.isConfirmed) return
  
  try {
    const response = await api.delete(`/admin/users/${user.userId}`)
    
    if (response.data.success) {
      // Remove from local data
      users.value = users.value.filter(u => u.userId !== user.userId)
      totalUsers.value--
      
      Swal.fire({
        icon: 'success',
        title: 'Success',
        text: 'User deleted successfully',
        timer: 2000,
        showConfirmButton: false
      })
    }
  } catch (error) {
    console.error('Failed to delete user:', error)
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: 'Failed to delete user'
    })
  }
}

// Debounced search
let searchTimeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 1 // Reset to first page when searching
  }, 300)
}

// Reset page when filter changes
watch(searchQuery, () => {
  currentPage.value = 1
})

onMounted(() => {
  loadUsers()
})
</script>
