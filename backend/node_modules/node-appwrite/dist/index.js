'use strict';

var client = require('./client');
var account = require('./services/account');
var avatars = require('./services/avatars');
var databases = require('./services/databases');
var functions = require('./services/functions');
var graphql = require('./services/graphql');
var health = require('./services/health');
var locale = require('./services/locale');
var messaging = require('./services/messaging');
var storage = require('./services/storage');
var teams = require('./services/teams');
var users = require('./services/users');
var permission = require('./permission');
var role = require('./role');
var id = require('./id');
var authenticatorType = require('./enums/authenticator-type');
var authenticationFactor = require('./enums/authentication-factor');
var oAuthProvider = require('./enums/o-auth-provider');
var browser = require('./enums/browser');
var creditCard = require('./enums/credit-card');
var flag = require('./enums/flag');
var relationshipType = require('./enums/relationship-type');
var relationMutate = require('./enums/relation-mutate');
var indexType = require('./enums/index-type');
var runtime = require('./enums/runtime');
var executionMethod = require('./enums/execution-method');
var name = require('./enums/name');
var smtpEncryption = require('./enums/smtp-encryption');
var compression = require('./enums/compression');
var imageGravity = require('./enums/image-gravity');
var imageFormat = require('./enums/image-format');
var passwordHash = require('./enums/password-hash');
var messagingProviderType = require('./enums/messaging-provider-type');



Object.defineProperty(exports, 'AppwriteException', {
  enumerable: true,
  get: function () { return client.AppwriteException; }
});
Object.defineProperty(exports, 'Client', {
  enumerable: true,
  get: function () { return client.Client; }
});
Object.defineProperty(exports, 'Query', {
  enumerable: true,
  get: function () { return client.Query; }
});
Object.defineProperty(exports, 'Account', {
  enumerable: true,
  get: function () { return account.Account; }
});
Object.defineProperty(exports, 'Avatars', {
  enumerable: true,
  get: function () { return avatars.Avatars; }
});
Object.defineProperty(exports, 'Databases', {
  enumerable: true,
  get: function () { return databases.Databases; }
});
Object.defineProperty(exports, 'Functions', {
  enumerable: true,
  get: function () { return functions.Functions; }
});
Object.defineProperty(exports, 'Graphql', {
  enumerable: true,
  get: function () { return graphql.Graphql; }
});
Object.defineProperty(exports, 'Health', {
  enumerable: true,
  get: function () { return health.Health; }
});
Object.defineProperty(exports, 'Locale', {
  enumerable: true,
  get: function () { return locale.Locale; }
});
Object.defineProperty(exports, 'Messaging', {
  enumerable: true,
  get: function () { return messaging.Messaging; }
});
Object.defineProperty(exports, 'Storage', {
  enumerable: true,
  get: function () { return storage.Storage; }
});
Object.defineProperty(exports, 'Teams', {
  enumerable: true,
  get: function () { return teams.Teams; }
});
Object.defineProperty(exports, 'Users', {
  enumerable: true,
  get: function () { return users.Users; }
});
Object.defineProperty(exports, 'Permission', {
  enumerable: true,
  get: function () { return permission.Permission; }
});
Object.defineProperty(exports, 'Role', {
  enumerable: true,
  get: function () { return role.Role; }
});
Object.defineProperty(exports, 'ID', {
  enumerable: true,
  get: function () { return id.ID; }
});
Object.defineProperty(exports, 'AuthenticatorType', {
  enumerable: true,
  get: function () { return authenticatorType.AuthenticatorType; }
});
Object.defineProperty(exports, 'AuthenticationFactor', {
  enumerable: true,
  get: function () { return authenticationFactor.AuthenticationFactor; }
});
Object.defineProperty(exports, 'OAuthProvider', {
  enumerable: true,
  get: function () { return oAuthProvider.OAuthProvider; }
});
Object.defineProperty(exports, 'Browser', {
  enumerable: true,
  get: function () { return browser.Browser; }
});
Object.defineProperty(exports, 'CreditCard', {
  enumerable: true,
  get: function () { return creditCard.CreditCard; }
});
Object.defineProperty(exports, 'Flag', {
  enumerable: true,
  get: function () { return flag.Flag; }
});
Object.defineProperty(exports, 'RelationshipType', {
  enumerable: true,
  get: function () { return relationshipType.RelationshipType; }
});
Object.defineProperty(exports, 'RelationMutate', {
  enumerable: true,
  get: function () { return relationMutate.RelationMutate; }
});
Object.defineProperty(exports, 'IndexType', {
  enumerable: true,
  get: function () { return indexType.IndexType; }
});
Object.defineProperty(exports, 'Runtime', {
  enumerable: true,
  get: function () { return runtime.Runtime; }
});
Object.defineProperty(exports, 'ExecutionMethod', {
  enumerable: true,
  get: function () { return executionMethod.ExecutionMethod; }
});
Object.defineProperty(exports, 'Name', {
  enumerable: true,
  get: function () { return name.Name; }
});
Object.defineProperty(exports, 'SmtpEncryption', {
  enumerable: true,
  get: function () { return smtpEncryption.SmtpEncryption; }
});
Object.defineProperty(exports, 'Compression', {
  enumerable: true,
  get: function () { return compression.Compression; }
});
Object.defineProperty(exports, 'ImageGravity', {
  enumerable: true,
  get: function () { return imageGravity.ImageGravity; }
});
Object.defineProperty(exports, 'ImageFormat', {
  enumerable: true,
  get: function () { return imageFormat.ImageFormat; }
});
Object.defineProperty(exports, 'PasswordHash', {
  enumerable: true,
  get: function () { return passwordHash.PasswordHash; }
});
Object.defineProperty(exports, 'MessagingProviderType', {
  enumerable: true,
  get: function () { return messagingProviderType.MessagingProviderType; }
});
//# sourceMappingURL=out.js.map
//# sourceMappingURL=index.js.map