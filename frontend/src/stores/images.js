import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useImagesStore = defineStore('images', () => {
  const images = ref([])
  const loading = ref(false)
  const error = ref(null)
  const uploadProgress = ref(0)
  const currentPage = ref(1)
  const totalImages = ref(0)

  // Computed
  const hasImages = computed(() => images.value.length > 0)
  const isLoading = computed(() => loading.value)
  const sortedImages = computed(() => {
    return [...images.value].sort((a, b) => new Date(b.uploaded_at) - new Date(a.uploaded_at))
  })
  const totalSize = computed(() => {
    return images.value.reduce((total, image) => total + (image.size || 0), 0)
  })

  // Actions
  async function uploadImage(file, customSettings = {}) {
    loading.value = true
    error.value = null
    uploadProgress.value = 0

    try {
      const formData = new FormData()
      formData.append('image', file)
      
      // Add custom settings
      if (customSettings.title) formData.append('title', customSettings.title)
      if (customSettings.description) formData.append('description', customSettings.description)
      if (customSettings.color) formData.append('color', customSettings.color)
      if (customSettings.footer) formData.append('footer', customSettings.footer)
      if (customSettings.sharex_enabled !== undefined) {
        formData.append('sharex_enabled', customSettings.sharex_enabled.toString())
      }

      const response = await fetch('/api/images/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('session')}`
        }
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Upload failed')
      }

      // Add to images array
      images.value.unshift(result.image)
      totalImages.value++

      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
      uploadProgress.value = 0
    }
  }

  async function fetchImages(page = 1, limit = 20) {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/images/my-images?page=${page}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('appwrite-token')}`
        }
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch images')
      }

      if (page === 1) {
        images.value = result.images
      } else {
        images.value.push(...result.images)
      }

      totalImages.value = result.total
      currentPage.value = page

      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteImage(imageId) {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(`/api/images/${imageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('appwrite-token')}`
        }
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete image')
      }

      // Remove from images array
      images.value = images.value.filter(img => img.$id !== imageId)
      totalImages.value--

      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  function clearError() {
    error.value = null
  }

  function clearImages() {
    images.value = []
    currentPage.value = 1
    totalImages.value = 0
  }

  async function copyImageUrl(url) {
    try {
      await navigator.clipboard.writeText(url)
      return { success: true, message: 'Image URL copied to clipboard!' }
    } catch (err) {
      console.error('Copy error:', err)
      return { success: false, error: 'Failed to copy URL to clipboard' }
    }
  }

  async function uploadMultipleImages(files, progressCallback) {
    const results = []

    for (let i = 0; i < files.length; i++) {
      try {
        const result = await uploadImage(files[i])
        results.push({ success: true, result })

        // Update progress
        if (progressCallback) {
          progressCallback(Math.round(((i + 1) / files.length) * 100))
        }
      } catch (error) {
        results.push({ success: false, error: error.message })
      }
    }

    return results
  }

  return {
    // State
    images,
    loading,
    error,
    uploadProgress,
    currentPage,
    totalImages,
    
    // Computed
    hasImages,
    isLoading,
    sortedImages,
    totalSize,
    
    // Actions
    uploadImage,
    fetchImages,
    deleteImage,
    clearError,
    clearImages,
    copyImageUrl,
    uploadMultipleImages
  }
})
