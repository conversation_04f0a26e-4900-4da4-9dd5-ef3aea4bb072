'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore, useAuthGetters } from '@/stores/auth';
import {
  Search,
  Bell,
  User,
  Menu,
  X,
  Image,
  Upload,
  Star,
  LayoutDashboard,
  FolderOpen,
  Share2,
  Settings,
  Trash2,
  LogOut,
  CheckCircle,
  AlertTriangle,
  Info,
  Crown
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const router = useRouter();
  const { logout } = useAuthStore();
  const { userName, userEmail, isAdmin } = useAuthGetters();

  const [loading, setLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Storage data - this would come from a storage API in a real app
  const [storageUsage] = useState(67);
  const [usedStorage] = useState('2.1 GB');
  const [totalStorage] = useState('5 GB');
  const [unreadNotifications] = useState(3);

  // Menu Sections
  const menuSections = [
    {
      title: 'Main',
      items: [
        { name: 'Dashboard', icon: LayoutDashboard, href: '/dashboard', active: true },
        { name: 'My Images', icon: Image, href: '/dashboard/images', active: false },
        { name: 'Upload', icon: Upload, href: '/dashboard/upload', active: false }
      ]
    },
    {
      title: 'Organize',
      items: [
        { name: 'Albums', icon: FolderOpen, href: '/albums', active: false },
        { name: 'Favorites', icon: Star, href: '/favorites', active: false },
        { name: 'Shared', icon: Share2, href: '/shared', active: false }
      ]
    },
    {
      title: 'Tools',
      items: [
        { name: 'ShareX Config', icon: Settings, href: '/sharex', active: false },
        { name: 'API Keys', icon: Info, href: '/api-keys', active: false },
        { name: 'Trash', icon: Trash2, href: '/trash', active: false }
      ]
    }
  ];

  // Add admin section if user is admin
  if (isAdmin) {
    menuSections.push({
      title: 'Administration',
      items: [
        { name: 'Admin Panel', icon: Crown, href: '/admin', active: false }
      ]
    });
  }

  // Profile Menu
  const profileMenu = [
    { name: 'Account Settings', icon: Settings, href: '/dashboard/account' },
    { name: 'API Keys', icon: Info, href: '/account/api-keys' },
    { name: 'Support', icon: Info, href: '/support' },
    { name: 'Sign Out', icon: LogOut, href: '#', action: handleLogout }
  ];

  // Notifications
  const notifications = [
    {
      id: 1,
      title: 'Image uploaded successfully',
      message: 'vacation-photo.jpg has been uploaded to Summer 2024 album',
      time: '2 minutes ago',
      icon: CheckCircle,
      type: 'upload',
      read: false
    },
    {
      id: 2,
      title: 'Storage warning',
      message: 'You are using 85% of your storage quota',
      time: '1 hour ago',
      icon: AlertTriangle,
      type: 'storage',
      read: false
    },
    {
      id: 3,
      title: 'Shared album viewed',
      message: 'Someone viewed your "Wedding Photos" album',
      time: '3 hours ago',
      icon: Share2,
      type: 'activity',
      read: false
    }
  ];

  async function handleLogout() {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
  const closeSidebar = () => setIsSidebarOpen(false);
  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
    if (!isSearchOpen) {
      setTimeout(() => searchInputRef.current?.focus(), 100);
    }
  };
  const closeSearch = () => setIsSearchOpen(false);
  const toggleNotifications = () => setIsNotificationsOpen(!isNotificationsOpen);
  const toggleProfile = () => setIsProfileOpen(!isProfileOpen);

  const closeDropdowns = () => {
    setIsNotificationsOpen(false);
    setIsProfileOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown') && !target.closest('button')) {
        closeDropdowns();
      }
    };

    const handleKeydown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === '/') {
        event.preventDefault();
        toggleSearch();
      }
      if (event.key === 'Escape') {
        closeSearch();
        closeDropdowns();
        closeSidebar();
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeydown);

    // Simulate loading
    setTimeout(() => setLoading(false), 1000);

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeydown);
    };
  }, []);

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient">
        <div className="text-center">
          <div className="w-16 h-16 mb-4 mx-auto">
            <svg className="animate-spin" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          </div>
          <div className="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
            Loading...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient text-gray-100">
      {/* Mobile Sidebar Overlay */}
      {isSidebarOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden" onClick={closeSidebar} />
      )}

      {/* Top Navigation Bar */}
      <nav className="fixed top-0 left-0 right-0 h-16 bg-gray-900/50 backdrop-blur-sm border-b border-gray-700/50 z-30">
        <div className="h-full px-4 flex items-center justify-between">
          {/* Left: Logo & Menu Button */}
          <div className="flex items-center gap-3">
            <button className="lg:hidden p-2 hover:bg-gray-800/50 rounded-lg" onClick={toggleSidebar}>
              {!isSidebarOpen ? <Menu className="w-5 h-5" /> : <X className="w-5 h-5" />}
            </button>
            
            <Link href="/dashboard" className="flex items-center gap-2">
              <img src="https://github.com/mythicalltd.png" alt="Logo" className="w-8 h-8" />
              <span className="font-bold text-lg hidden sm:block">MythicalUI</span>
            </Link>
          </div>

          {/* Right: Search, Notifications, Profile */}
          <div className="flex items-center gap-2">
            {/* Search */}
            <div className="relative">
              <button
                onClick={toggleSearch}
                className="p-2 hover:bg-gray-800/50 rounded-lg transition-colors"
              >
                <Search className="w-5 h-5" />
              </button>
              
              {isSearchOpen && (
                <div className="absolute right-0 top-12 w-80 bg-gray-900/90 backdrop-blur-sm border border-gray-700/50 rounded-lg p-4 shadow-xl">
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search images..."
                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg px-3 py-2 text-sm text-gray-100 placeholder-gray-500 focus:border-purple-500/50 focus:ring-1 focus:ring-purple-500/50 focus:outline-none"
                  />
                </div>
              )}
            </div>

            {/* Notifications */}
            <div className="relative dropdown">
              <button
                onClick={toggleNotifications}
                className="p-2 hover:bg-gray-800/50 rounded-lg transition-colors relative"
              >
                <Bell className="w-5 h-5" />
                {unreadNotifications > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadNotifications}
                  </span>
                )}
              </button>
              
              {isNotificationsOpen && (
                <div className="absolute right-0 top-12 w-80 bg-gray-900/90 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl max-h-96 overflow-y-auto">
                  <div className="p-4 border-b border-gray-700/50">
                    <h3 className="font-medium text-gray-100">Notifications</h3>
                  </div>
                  <div className="p-2">
                    {notifications.map((notification) => (
                      <div key={notification.id} className="p-3 hover:bg-gray-800/50 rounded-lg cursor-pointer">
                        <div className="flex items-start gap-3">
                          <notification.icon className="w-5 h-5 text-purple-400 mt-0.5" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-100">{notification.title}</p>
                            <p className="text-xs text-gray-400 mt-1">{notification.message}</p>
                            <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Profile */}
            <div className="relative dropdown">
              <button
                onClick={toggleProfile}
                className="flex items-center gap-2 p-2 hover:bg-gray-800/50 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4" />
                </div>
              </button>
              
              {isProfileOpen && (
                <div className="absolute right-0 top-12 w-64 bg-gray-900/90 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl">
                  <div className="p-4 border-b border-gray-700/50">
                    <p className="font-medium text-gray-100">{userName || 'Guest User'}</p>
                    <p className="text-sm text-gray-400">{userEmail || '<EMAIL>'}</p>
                  </div>
                  <div className="p-2">
                    {profileMenu.map((item) => (
                      item.action ? (
                        <button
                          key={item.name}
                          onClick={item.action}
                          className="flex items-center gap-3 px-3 py-2 hover:bg-gray-800/50 rounded-lg transition-colors w-full text-left"
                        >
                          <item.icon className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-300">{item.name}</span>
                        </button>
                      ) : (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="flex items-center gap-3 px-3 py-2 hover:bg-gray-800/50 rounded-lg transition-colors"
                        >
                          <item.icon className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-300">{item.name}</span>
                        </Link>
                      )
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Sidebar */}
      <aside className={`fixed top-0 left-0 h-full w-64 bg-gray-900/50 backdrop-blur-sm border-r border-gray-700/50 transform transition-transform duration-200 ease-in-out z-50 lg:translate-x-0 lg:z-20 ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="flex flex-col h-full pt-16">
          {/* Storage Usage */}
          <div className="p-4 border-b border-gray-700/50">
            <div className="text-sm text-gray-400 mb-2">Storage Usage</div>
            <div className="w-full bg-gray-700/50 rounded-full h-2 mb-2">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" style={{ width: `${storageUsage}%` }} />
            </div>
            <div className="text-xs text-gray-400">{usedStorage} / {totalStorage} used</div>
          </div>
          
          {/* Navigation */}
          <div className="flex-1 overflow-y-auto">
            <nav className="p-4">
              {menuSections.map((section, index) => (
                <div key={index} className="mb-6">
                  <div className="text-xs uppercase tracking-wider text-gray-500 font-medium px-4 mb-3">
                    {section.title}
                  </div>
                  <div className="space-y-1">
                    {section.items.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-800/50 transition-colors group ${item.active ? 'bg-purple-500/10 text-purple-400 border-r-2 border-purple-500' : ''}`}
                      >
                        <item.icon className={`w-5 h-5 transition-colors ${item.active ? 'text-purple-400' : 'text-gray-400 group-hover:text-purple-400'}`} />
                        <span className="font-medium">{item.name}</span>
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </nav>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main className="lg:ml-64 pt-16 min-h-screen">
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  );
}
