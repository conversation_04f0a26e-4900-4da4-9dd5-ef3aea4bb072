{"version": 3, "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,SAAS,QAAQ,OAAO,yBAAyB;AACjD,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAC1B,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,cAAc;AACvB,SAAS,cAAc;AACvB,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,aAAa;AACtB,SAAS,aAAa;AAGtB,SAAS,kBAAkB;AAC3B,SAAS,YAAY;AACrB,SAAS,UAAU;AACnB,SAAS,yBAAyB;AAClC,SAAS,4BAA4B;AACrC,SAAS,qBAAqB;AAC9B,SAAS,eAAe;AACxB,SAAS,kBAAkB;AAC3B,SAAS,YAAY;AACrB,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,uBAAuB;AAChC,SAAS,YAAY;AACrB,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAC7B,SAAS,mBAAmB;AAC5B,SAAS,oBAAoB;AAC7B,SAAS,6BAA6B", "sourcesContent": ["export { Client, Query, AppwriteException } from './client';\nexport { Account } from './services/account';\nexport { Avatars } from './services/avatars';\nexport { Databases } from './services/databases';\nexport { Functions } from './services/functions';\nexport { Graphql } from './services/graphql';\nexport { Health } from './services/health';\nexport { Locale } from './services/locale';\nexport { Messaging } from './services/messaging';\nexport { Storage } from './services/storage';\nexport { Teams } from './services/teams';\nexport { Users } from './services/users';\nexport type { Models, Payload, UploadProgress } from './client';\nexport type { QueryTypes, QueryTypesList } from './query';\nexport { Permission } from './permission';\nexport { Role } from './role';\nexport { ID } from './id';\nexport { AuthenticatorType } from './enums/authenticator-type';\nexport { AuthenticationFactor } from './enums/authentication-factor';\nexport { OAuthProvider } from './enums/o-auth-provider';\nexport { Browser } from './enums/browser';\nexport { CreditCard } from './enums/credit-card';\nexport { Flag } from './enums/flag';\nexport { RelationshipType } from './enums/relationship-type';\nexport { RelationMutate } from './enums/relation-mutate';\nexport { IndexType } from './enums/index-type';\nexport { Runtime } from './enums/runtime';\nexport { ExecutionMethod } from './enums/execution-method';\nexport { Name } from './enums/name';\nexport { SmtpEncryption } from './enums/smtp-encryption';\nexport { Compression } from './enums/compression';\nexport { ImageGravity } from './enums/image-gravity';\nexport { ImageFormat } from './enums/image-format';\nexport { PasswordHash } from './enums/password-hash';\nexport { MessagingProviderType } from './enums/messaging-provider-type';\n"]}