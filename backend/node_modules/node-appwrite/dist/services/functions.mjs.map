{"version": 3, "sources": ["../../src/services/functions.ts"], "names": [], "mappings": ";AAAA,SAAS,yBAA+D;AAKjE,IAAM,YAAN,MAAgB;AAAA,EAGnB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,KAAK,SAAoB,QAA+C;AAC1E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,MAAM,OAAO,YAAoB,MAAc,SAAkB,SAAoB,QAAmB,UAAmB,SAAkB,SAAmB,SAAmB,YAAqB,UAAmB,gBAAyB,sBAA+B,gBAAyB,oBAA8B,uBAAgC,oBAA6B,eAAwB,uBAAgC,gBAAmD;AAC9e,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,mBAAmB,aAAa;AACvC,cAAQ,gBAAgB,IAAI;AAAA,IAChC;AACA,QAAI,OAAO,yBAAyB,aAAa;AAC7C,cAAQ,sBAAsB,IAAI;AAAA,IACtC;AACA,QAAI,OAAO,mBAAmB,aAAa;AACvC,cAAQ,gBAAgB,IAAI;AAAA,IAChC;AACA,QAAI,OAAO,uBAAuB,aAAa;AAC3C,cAAQ,oBAAoB,IAAI;AAAA,IACpC;AACA,QAAI,OAAO,0BAA0B,aAAa;AAC9C,cAAQ,uBAAuB,IAAI;AAAA,IACvC;AACA,QAAI,OAAO,uBAAuB,aAAa;AAC3C,cAAQ,oBAAoB,IAAI;AAAA,IACpC;AACA,QAAI,OAAO,kBAAkB,aAAa;AACtC,cAAQ,eAAe,IAAI;AAAA,IAC/B;AACA,QAAI,OAAO,0BAA0B,aAAa;AAC9C,cAAQ,uBAAuB,IAAI;AAAA,IACvC;AACA,QAAI,OAAO,mBAAmB,aAAa;AACvC,cAAQ,gBAAgB,IAAI;AAAA,IAChC;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAA4C;AAC9C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,IAAI,YAA8C;AACpD,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,MAAM,OAAO,YAAoB,MAAc,SAAmB,SAAoB,QAAmB,UAAmB,SAAkB,SAAmB,SAAmB,YAAqB,UAAmB,gBAAyB,sBAA+B,gBAAyB,oBAA8B,uBAA0D;AACjY,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,mBAAmB,aAAa;AACvC,cAAQ,gBAAgB,IAAI;AAAA,IAChC;AACA,QAAI,OAAO,yBAAyB,aAAa;AAC7C,cAAQ,sBAAsB,IAAI;AAAA,IACtC;AACA,QAAI,OAAO,mBAAmB,aAAa;AACvC,cAAQ,gBAAgB,IAAI;AAAA,IAChC;AACA,QAAI,OAAO,uBAAuB,aAAa;AAC3C,cAAQ,oBAAoB,IAAI;AAAA,IACpC;AACA,QAAI,OAAO,0BAA0B,aAAa;AAC9C,cAAQ,uBAAuB,IAAI;AAAA,IACvC;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,YAAiC;AAC1C,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,gBAAgB,YAAoB,SAAoB,QAAiD;AAC3G,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,sCAAsC,QAAQ,gBAAgB,UAAU;AACxF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,iBAAiB,YAAoB,MAAY,UAAmB,YAAqB,UAAmB,aAAa,CAAC,aAA6B;AAAA,EAAC,GAA+B;AACzL,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,sCAAsC,QAAQ,gBAAgB,UAAU;AACxF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,YAAoB,cAAkD;AACtF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,YAAoB,cAAgD;AACvF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,YAAoB,cAAmC;AAC1E,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,YAAoB,cAAsB,SAA8B;AACtF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,sEAAsE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,aAAa,OAAO;AAC9L,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,YAAoB,cAA4C;AACrF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,8DAA8D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACxJ,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,YAAoB,SAAoB,QAAgD;AACzG,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,gBAAgB,YAAoB,MAAe,OAAiB,OAAgB,QAA0B,SAA6C;AAC7J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,aAAa,YAAoB,aAAgD;AACnF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,YAAM,IAAI,kBAAkB,2CAA2C;AAAA,IAC3E;AACA,UAAM,UAAU,mDAAmD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,iBAAiB,WAAW;AAC3I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,YAAkD;AAClE,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,oCAAoC,QAAQ,gBAAgB,UAAU;AACtF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,YAAoB,KAAa,OAAyC;AAC3F,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU,oCAAoC,QAAQ,gBAAgB,UAAU;AACtF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY,YAAoB,YAA8C;AAChF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,iDAAiD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,gBAAgB,UAAU;AACvI,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,eAAe,YAAoB,YAAoB,KAAa,OAA0C;AAChH,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,iDAAiD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,gBAAgB,UAAU;AACvI,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,eAAe,YAAoB,YAAiC;AACtE,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,iDAAiD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,gBAAgB,UAAU;AACvI,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { Runtime } from '../enums/runtime';\nimport { ExecutionMethod } from '../enums/execution-method';\n\nexport class Functions {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List functions\n     *\n     * Get a list of all the project&#039;s functions. You can use the query params to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.FunctionList>}\n     */\n    async list(queries?: string[], search?: string): Promise<Models.FunctionList> {\n        const apiPath = '/functions';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create function\n     *\n     * Create a new function. You can pass a list of [permissions](https://appwrite.io/docs/permissions) to allow different project users or team with access to execute the function using the client API.\n     *\n     * @param {string} functionId\n     * @param {string} name\n     * @param {Runtime} runtime\n     * @param {string[]} execute\n     * @param {string[]} events\n     * @param {string} schedule\n     * @param {number} timeout\n     * @param {boolean} enabled\n     * @param {boolean} logging\n     * @param {string} entrypoint\n     * @param {string} commands\n     * @param {string} installationId\n     * @param {string} providerRepositoryId\n     * @param {string} providerBranch\n     * @param {boolean} providerSilentMode\n     * @param {string} providerRootDirectory\n     * @param {string} templateRepository\n     * @param {string} templateOwner\n     * @param {string} templateRootDirectory\n     * @param {string} templateBranch\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Function>}\n     */\n    async create(functionId: string, name: string, runtime: Runtime, execute?: string[], events?: string[], schedule?: string, timeout?: number, enabled?: boolean, logging?: boolean, entrypoint?: string, commands?: string, installationId?: string, providerRepositoryId?: string, providerBranch?: string, providerSilentMode?: boolean, providerRootDirectory?: string, templateRepository?: string, templateOwner?: string, templateRootDirectory?: string, templateBranch?: string): Promise<Models.Function> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        if (typeof runtime === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"runtime\"');\n        }\n        const apiPath = '/functions';\n        const payload: Payload = {};\n        if (typeof functionId !== 'undefined') {\n            payload['functionId'] = functionId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof runtime !== 'undefined') {\n            payload['runtime'] = runtime;\n        }\n        if (typeof execute !== 'undefined') {\n            payload['execute'] = execute;\n        }\n        if (typeof events !== 'undefined') {\n            payload['events'] = events;\n        }\n        if (typeof schedule !== 'undefined') {\n            payload['schedule'] = schedule;\n        }\n        if (typeof timeout !== 'undefined') {\n            payload['timeout'] = timeout;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof logging !== 'undefined') {\n            payload['logging'] = logging;\n        }\n        if (typeof entrypoint !== 'undefined') {\n            payload['entrypoint'] = entrypoint;\n        }\n        if (typeof commands !== 'undefined') {\n            payload['commands'] = commands;\n        }\n        if (typeof installationId !== 'undefined') {\n            payload['installationId'] = installationId;\n        }\n        if (typeof providerRepositoryId !== 'undefined') {\n            payload['providerRepositoryId'] = providerRepositoryId;\n        }\n        if (typeof providerBranch !== 'undefined') {\n            payload['providerBranch'] = providerBranch;\n        }\n        if (typeof providerSilentMode !== 'undefined') {\n            payload['providerSilentMode'] = providerSilentMode;\n        }\n        if (typeof providerRootDirectory !== 'undefined') {\n            payload['providerRootDirectory'] = providerRootDirectory;\n        }\n        if (typeof templateRepository !== 'undefined') {\n            payload['templateRepository'] = templateRepository;\n        }\n        if (typeof templateOwner !== 'undefined') {\n            payload['templateOwner'] = templateOwner;\n        }\n        if (typeof templateRootDirectory !== 'undefined') {\n            payload['templateRootDirectory'] = templateRootDirectory;\n        }\n        if (typeof templateBranch !== 'undefined') {\n            payload['templateBranch'] = templateBranch;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List runtimes\n     *\n     * Get a list of all runtimes that are currently active on your instance.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.RuntimeList>}\n     */\n    async listRuntimes(): Promise<Models.RuntimeList> {\n        const apiPath = '/functions/runtimes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get function\n     *\n     * Get a function by its unique ID.\n     *\n     * @param {string} functionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Function>}\n     */\n    async get(functionId: string): Promise<Models.Function> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update function\n     *\n     * Update function by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} name\n     * @param {Runtime} runtime\n     * @param {string[]} execute\n     * @param {string[]} events\n     * @param {string} schedule\n     * @param {number} timeout\n     * @param {boolean} enabled\n     * @param {boolean} logging\n     * @param {string} entrypoint\n     * @param {string} commands\n     * @param {string} installationId\n     * @param {string} providerRepositoryId\n     * @param {string} providerBranch\n     * @param {boolean} providerSilentMode\n     * @param {string} providerRootDirectory\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Function>}\n     */\n    async update(functionId: string, name: string, runtime?: Runtime, execute?: string[], events?: string[], schedule?: string, timeout?: number, enabled?: boolean, logging?: boolean, entrypoint?: string, commands?: string, installationId?: string, providerRepositoryId?: string, providerBranch?: string, providerSilentMode?: boolean, providerRootDirectory?: string): Promise<Models.Function> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/functions/{functionId}'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof runtime !== 'undefined') {\n            payload['runtime'] = runtime;\n        }\n        if (typeof execute !== 'undefined') {\n            payload['execute'] = execute;\n        }\n        if (typeof events !== 'undefined') {\n            payload['events'] = events;\n        }\n        if (typeof schedule !== 'undefined') {\n            payload['schedule'] = schedule;\n        }\n        if (typeof timeout !== 'undefined') {\n            payload['timeout'] = timeout;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof logging !== 'undefined') {\n            payload['logging'] = logging;\n        }\n        if (typeof entrypoint !== 'undefined') {\n            payload['entrypoint'] = entrypoint;\n        }\n        if (typeof commands !== 'undefined') {\n            payload['commands'] = commands;\n        }\n        if (typeof installationId !== 'undefined') {\n            payload['installationId'] = installationId;\n        }\n        if (typeof providerRepositoryId !== 'undefined') {\n            payload['providerRepositoryId'] = providerRepositoryId;\n        }\n        if (typeof providerBranch !== 'undefined') {\n            payload['providerBranch'] = providerBranch;\n        }\n        if (typeof providerSilentMode !== 'undefined') {\n            payload['providerSilentMode'] = providerSilentMode;\n        }\n        if (typeof providerRootDirectory !== 'undefined') {\n            payload['providerRootDirectory'] = providerRootDirectory;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete function\n     *\n     * Delete a function by its unique ID.\n     *\n     * @param {string} functionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async delete(functionId: string): Promise<{}> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List deployments\n     *\n     * Get a list of all the project&#039;s code deployments. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DeploymentList>}\n     */\n    async listDeployments(functionId: string, queries?: string[], search?: string): Promise<Models.DeploymentList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create deployment\n     *\n     * Create a new function code deployment. Use this endpoint to upload a new version of your code function. To execute your newly uploaded code, you&#039;ll need to update the function&#039;s deployment to use your new deployment UID.\n\nThis endpoint accepts a tar.gz file compressed with your code. Make sure to include any dependencies your code has within the compressed file. You can learn more about code packaging in the [Appwrite Cloud Functions tutorial](https://appwrite.io/docs/functions).\n\nUse the &quot;command&quot; param to set the entrypoint used to execute your code.\n     *\n     * @param {string} functionId\n     * @param {File} code\n     * @param {boolean} activate\n     * @param {string} entrypoint\n     * @param {string} commands\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Deployment>}\n     */\n    async createDeployment(functionId: string, code: File, activate: boolean, entrypoint?: string, commands?: string, onProgress = (progress: UploadProgress) => {}): Promise<Models.Deployment> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        if (typeof activate === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"activate\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof entrypoint !== 'undefined') {\n            payload['entrypoint'] = entrypoint;\n        }\n        if (typeof commands !== 'undefined') {\n            payload['commands'] = commands;\n        }\n        if (typeof code !== 'undefined') {\n            payload['code'] = code;\n        }\n        if (typeof activate !== 'undefined') {\n            payload['activate'] = activate;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'multipart/form-data',\n        }\n\n        return await this.client.chunkedUpload(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n            onProgress\n        );\n    }\n    /**\n     * Get deployment\n     *\n     * Get a code deployment by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} deploymentId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Deployment>}\n     */\n    async getDeployment(functionId: string, deploymentId: string): Promise<Models.Deployment> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof deploymentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"deploymentId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments/{deploymentId}'.replace('{functionId}', functionId).replace('{deploymentId}', deploymentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update function deployment\n     *\n     * Update the function code deployment ID using the unique function ID. Use this endpoint to switch the code deployment that should be executed by the execution endpoint.\n     *\n     * @param {string} functionId\n     * @param {string} deploymentId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Function>}\n     */\n    async updateDeployment(functionId: string, deploymentId: string): Promise<Models.Function> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof deploymentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"deploymentId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments/{deploymentId}'.replace('{functionId}', functionId).replace('{deploymentId}', deploymentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete deployment\n     *\n     * Delete a code deployment by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} deploymentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteDeployment(functionId: string, deploymentId: string): Promise<{}> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof deploymentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"deploymentId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments/{deploymentId}'.replace('{functionId}', functionId).replace('{deploymentId}', deploymentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create build\n     *\n     * Create a new build for an Appwrite Function deployment. This endpoint can be used to retry a failed build.\n     *\n     * @param {string} functionId\n     * @param {string} deploymentId\n     * @param {string} buildId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async createBuild(functionId: string, deploymentId: string, buildId: string): Promise<{}> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof deploymentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"deploymentId\"');\n        }\n        if (typeof buildId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"buildId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments/{deploymentId}/builds/{buildId}'.replace('{functionId}', functionId).replace('{deploymentId}', deploymentId).replace('{buildId}', buildId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Download Deployment\n     *\n     * Get a Deployment&#039;s contents by its unique ID. This endpoint supports range requests for partial or streaming file download.\n     *\n     * @param {string} functionId\n     * @param {string} deploymentId\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async downloadDeployment(functionId: string, deploymentId: string): Promise<ArrayBuffer> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof deploymentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"deploymentId\"');\n        }\n        const apiPath = '/functions/{functionId}/deployments/{deploymentId}/download'.replace('{functionId}', functionId).replace('{deploymentId}', deploymentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * List executions\n     *\n     * Get a list of all the current user function execution logs. You can use the query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ExecutionList>}\n     */\n    async listExecutions(functionId: string, queries?: string[], search?: string): Promise<Models.ExecutionList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create execution\n     *\n     * Trigger a function execution. The returned object will return you the current execution status. You can ping the `Get Execution` endpoint to get updates on the current execution status. Once this endpoint is called, your function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    async createExecution(functionId: string, body?: string, async?: boolean, xpath?: string, method?: ExecutionMethod, headers?: object): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get execution\n     *\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Execution>}\n     */\n    async getExecution(functionId: string, executionId: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List variables\n     *\n     * Get a list of all variables of a specific function.\n     *\n     * @param {string} functionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.VariableList>}\n     */\n    async listVariables(functionId: string): Promise<Models.VariableList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        const apiPath = '/functions/{functionId}/variables'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create variable\n     *\n     * Create a new function environment variable. These variables can be accessed in the function at runtime as environment variables.\n     *\n     * @param {string} functionId\n     * @param {string} key\n     * @param {string} value\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Variable>}\n     */\n    async createVariable(functionId: string, key: string, value: string): Promise<Models.Variable> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof value === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"value\"');\n        }\n        const apiPath = '/functions/{functionId}/variables'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get variable\n     *\n     * Get a variable by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} variableId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Variable>}\n     */\n    async getVariable(functionId: string, variableId: string): Promise<Models.Variable> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof variableId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"variableId\"');\n        }\n        const apiPath = '/functions/{functionId}/variables/{variableId}'.replace('{functionId}', functionId).replace('{variableId}', variableId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update variable\n     *\n     * Update variable by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} variableId\n     * @param {string} key\n     * @param {string} value\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Variable>}\n     */\n    async updateVariable(functionId: string, variableId: string, key: string, value?: string): Promise<Models.Variable> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof variableId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"variableId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/functions/{functionId}/variables/{variableId}'.replace('{functionId}', functionId).replace('{variableId}', variableId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof value !== 'undefined') {\n            payload['value'] = value;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete variable\n     *\n     * Delete a variable by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} variableId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteVariable(functionId: string, variableId: string): Promise<{}> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n        if (typeof variableId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"variableId\"');\n        }\n        const apiPath = '/functions/{functionId}/variables/{variableId}'.replace('{functionId}', functionId).replace('{variableId}', variableId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}