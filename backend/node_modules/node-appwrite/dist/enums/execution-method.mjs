// src/enums/execution-method.ts
var ExecutionMethod = /* @__PURE__ */ ((ExecutionMethod2) => {
  ExecutionMethod2["GET"] = "GET";
  ExecutionMethod2["POST"] = "POST";
  ExecutionMethod2["PUT"] = "PUT";
  ExecutionMethod2["PATCH"] = "PATCH";
  ExecutionMethod2["DELETE"] = "DELETE";
  ExecutionMethod2["OPTIONS"] = "OPTIONS";
  return ExecutionMethod2;
})(ExecutionMethod || {});

export { ExecutionMethod };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=execution-method.mjs.map