"use strict";var i=Object.defineProperty;var l=(r,t)=>i(r,"name",{value:t,configurable:!0});Object.defineProperty(exports,"__esModule",{value:!0});const node=require("./node.cjs");require("node:http"),require("node:https"),require("node:zlib"),require("node:stream"),require("node:buffer"),require("node:util"),require("./shared/node-fetch-native-with-agent.61758d11.cjs"),require("node:url"),require("node:net"),require("node:fs"),require("node:path");var s=Object.defineProperty,e=l((r,t)=>s(r,"name",{value:t,configurable:!0}),"e");const o=!!globalThis.process?.env?.FORCE_NODE_FETCH;function p(){return!o&&globalThis.fetch?globalThis.fetch:node.fetch}l(p,"p"),e(p,"_getFetch");const fetch=p(),Blob=!o&&globalThis.Blob||node.Blob,File=!o&&globalThis.File||node.File,FormData=!o&&globalThis.FormData||node.FormData,Headers=!o&&globalThis.Headers||node.Headers,Request=!o&&globalThis.Request||node.Request,Response=!o&&globalThis.Response||node.Response,AbortController=!o&&globalThis.AbortController||node.AbortController;exports.AbortError=node.AbortError,exports.FetchError=node.FetchError,exports.blobFrom=node.blobFrom,exports.blobFromSync=node.blobFromSync,exports.fileFrom=node.fileFrom,exports.fileFromSync=node.fileFromSync,exports.isRedirect=node.isRedirect,exports.AbortController=AbortController,exports.Blob=Blob,exports.File=File,exports.FormData=FormData,exports.Headers=Headers,exports.Request=Request,exports.Response=Response,exports.default=fetch,exports.fetch=fetch;
