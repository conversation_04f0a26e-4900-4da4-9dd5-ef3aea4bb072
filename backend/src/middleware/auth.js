import { Client, Account } from 'node-appwrite';

// Middleware to verify Appwrite session
export const authenticateUser = async (req, res, next) => {
  try {
    // Get session from Authorization header
    const sessionId = req.headers.authorization?.replace('Bearer ', '');
    
    if (!sessionId) {
      return res.status(401).json({ 
        success: false, 
        error: 'No session token provided' 
      });
    }

    // Create a temporary client with the session
    const tempClient = new Client()
      .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
      .setProject(process.env.APPWRITE_PROJECT_ID);

    const tempAccount = new Account(tempClient);

    // Set session and try to get user
    tempClient.setSession(sessionId);
    const user = await tempAccount.get();
    
    // Attach user to request object
    req.user = user;
    req.sessionId = sessionId;
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({ 
      success: false, 
      error: 'Invalid or expired session' 
    });
  }
};

// Optional authentication (won't fail if no token)
export const optionalAuth = async (req, res, next) => {
  try {
    const sessionId = req.headers.authorization?.replace('Bearer ', '');
    
    if (sessionId) {
      // Create a temporary client with the session
      const tempClient = new Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
        .setProject(process.env.APPWRITE_PROJECT_ID);

      const tempAccount = new Account(tempClient);
      tempClient.setSession(sessionId);
      
      const user = await tempAccount.get();
      req.user = user;
      req.sessionId = sessionId;
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

// Check if user is admin (checks user profile database)
export const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    // Import UserService to check admin status in database
    const { UserService } = await import('../services/userService.js');

    try {
      const userProfile = await UserService.getUserProfile(req.user.$id);

      // Check if user has admin role in database or is the admin email
      const isAdmin = userProfile.isAdmin || req.user.email === process.env.ADMIN_EMAIL;

      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          error: 'Admin access required'
        });
      }

      // Attach profile to request for use in controllers
      req.userProfile = userProfile;
      next();
    } catch (profileError) {
      console.error('Error fetching user profile for admin check:', profileError);

      // Fallback to email check if profile doesn't exist
      if (req.user.email === process.env.ADMIN_EMAIL) {
        next();
      } else {
        return res.status(403).json({
          success: false,
          error: 'Admin access required'
        });
      }
    }
  } catch (error) {
    console.error('Admin middleware error:', error);
    return res.status(500).json({
      success: false,
      error: 'Error verifying admin status'
    });
  }
};
