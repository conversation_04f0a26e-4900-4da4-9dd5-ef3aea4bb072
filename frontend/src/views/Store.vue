<template>
  <LayoutDashboard>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-white">Store</h1>
          <p class="text-gray-400">Upgrade your storage and buy credits</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="text-right">
            <p class="text-sm text-gray-400">Your Credits</p>
            <p class="text-xl font-semibold text-purple-400">{{ userStore.credits }}</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-400">Storage</p>
            <p class="text-xl font-semibold text-blue-400">{{ userStore.storageUsedMB }}/{{ userStore.storageLimitMB }}MB</p>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="border-b border-gray-800">
        <nav class="flex space-x-8">
          <button v-for="tab in tabs" :key="tab.key"
                  @click="activeTab = tab.key"
                  :class="[
                    'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                    activeTab === tab.key
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  ]">
            <component :is="tab.icon" class="w-4 h-4 inline mr-2" />
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Storage Plans -->
      <div v-if="activeTab === 'storage'">
        <div v-if="plans.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="plan in plans"
            :key="plan.$id"
            class="bg-gray-800 rounded-lg p-6 border border-gray-700 hover:border-purple-500 transition-colors"
          >
            <div class="text-center">
              <h3 class="text-xl font-semibold text-gray-100 mb-2">{{ plan.name }}</h3>
              <p class="text-gray-400 text-sm mb-4">{{ plan.description }}</p>

              <div class="mb-4">
                <span class="text-3xl font-bold text-purple-400">{{ plan.creditCost }}</span>
                <span class="text-gray-400 ml-1">credits</span>
              </div>

              <div class="space-y-2 mb-6">
                <div class="flex items-center justify-center text-gray-300">
                  <HardDrive class="h-4 w-4 mr-2" />
                  <span>{{ plan.storageAmountMB }}MB Additional Storage</span>
                </div>
                <div v-if="plan.includesFooterCustomization" class="flex items-center justify-center text-purple-400">
                  <Star class="h-4 w-4 mr-2" />
                  <span>Footer Customization</span>
                </div>
              </div>

              <button
                @click="purchasePlan(plan)"
                :disabled="userStore.credits < plan.creditCost || isPurchasing"
                class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ isPurchasing ? 'Processing...' : 'Purchase Plan' }}
              </button>

              <p v-if="userStore.credits < plan.creditCost" class="text-red-400 text-sm mt-2">
                Insufficient credits
              </p>
            </div>
          </div>
        </div>

        <!-- No Plans Available -->
        <div v-else-if="!isLoading" class="text-center py-12">
          <Package class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-xl font-semibold text-gray-100 mb-2">No Plans Available</h3>
          <p class="text-gray-400">Storage plans are not currently available. Check back later!</p>
        </div>
      </div>

      <!-- Recent Transactions -->
      <div v-if="activeTab === 'credits'" class="space-y-6">
        <div class="text-center py-12">
          <Coins class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-xl font-semibold text-gray-100 mb-2">Credit Purchases</h3>
          <p class="text-gray-400">Credit purchasing system coming soon!</p>
        </div>
      </div>

      <!-- Loading -->
      <div v-if="isLoading && activeTab === 'storage'" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
        <p class="text-gray-400 mt-4">Loading plans...</p>
      </div>

      <!-- Recent Transactions -->
      <div v-if="transactions.length > 0" class="bg-gray-800 rounded-lg">
        <div class="px-6 py-4 border-b border-gray-700">
          <h3 class="text-lg font-medium text-gray-100">Recent Purchases</h3>
        </div>
        <div class="p-6">
          <div class="space-y-3">
            <div v-for="transaction in transactions" :key="transaction.$id" class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-100">{{ transaction.planName }}</p>
                <p class="text-xs text-gray-400">{{ formatDate(transaction.createdAt) }}</p>
              </div>
              <div class="text-right">
                <p class="text-sm text-green-400">+{{ transaction.storageAmountMB }}MB</p>
                <p class="text-xs text-gray-400">{{ transaction.creditCost }} credits</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </LayoutDashboard>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LayoutDashboard from '../components/LayoutDashboard.vue'
import { useUserStore } from '../stores/user.js'
import { HardDrive, Coins, Star, Package } from 'lucide-vue-next'
import api from '../config/api.js'
import Swal from 'sweetalert2'

const userStore = useUserStore()
const activeTab = ref('storage')
const isLoading = ref(false)
const isPurchasing = ref(false)
const plans = ref([])
const transactions = ref([])

const tabs = [
  { key: 'storage', name: 'Storage', icon: HardDrive },
  { key: 'credits', name: 'Credits', icon: Coins }
]

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

const loadPlans = async () => {
  try {
    isLoading.value = true
    const response = await api.get('/user/storage/plans')
    if (response.data.success) {
      plans.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load plans:', error)
  } finally {
    isLoading.value = false
  }
}

const loadTransactions = async () => {
  try {
    const response = await api.get('/user/transactions?limit=5')
    if (response.data.success) {
      transactions.value = response.data.data.transactions
    }
  } catch (error) {
    console.error('Failed to load transactions:', error)
  }
}

const purchasePlan = async (plan) => {
  try {
    isPurchasing.value = true

    const result = await Swal.fire({
      title: 'Confirm Purchase',
      html: `
        <div class="text-left">
          <p><strong>Plan:</strong> ${plan.name}</p>
          <p><strong>Storage:</strong> +${plan.storageAmountMB}MB</p>
          <p><strong>Cost:</strong> ${plan.creditCost} credits</p>
          ${plan.includesFooterCustomization ? '<p><strong>Includes:</strong> Footer Customization</p>' : ''}
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#7c3aed',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Purchase'
    })

    if (result.isConfirmed) {
      const response = await api.post('/user/storage/purchase', { planId: plan.$id })

      if (response.data.success) {
        Swal.fire('Success!', response.data.message, 'success')
        await userStore.fetchProfile() // Refresh user data
        await loadTransactions() // Refresh transactions
      } else {
        throw new Error(response.data.error)
      }
    }
  } catch (error) {
    console.error('Purchase failed:', error)
    Swal.fire('Error', error.response?.data?.error || error.message || 'Purchase failed', 'error')
  } finally {
    isPurchasing.value = false
  }
}

onMounted(async () => {
  document.title = 'Store - AveImgCloud'
  // Load user profile to get current stats
  try {
    await userStore.fetchProfile()
    await loadPlans()
    await loadTransactions()
  } catch (error) {
    console.error('Failed to load store data:', error)
  }
})
</script>
