# AveImage Cloud 🚀

A modern, professional image hosting service with ShareX integration, built with Vue.js and Node.js.

## Features ✨

- **Modern UI/UX**: Dark theme with glass morphism effects and smooth animations
- **ShareX Integration**: Direct upload support with custom configuration
- **OAuth Authentication**: Google and Discord login support
- **Drag & Drop Upload**: Intuitive file upload interface
- **Real-time Progress**: Live upload progress tracking
- **Image Gallery**: Beautiful grid layout for your uploaded images
- **Copy to Clipboard**: One-click URL copying
- **API Integration**: RESTful API for external applications
- **Secure Storage**: Powered by Appwrite cloud storage
- **Mobile Responsive**: Works perfectly on all devices

## Tech Stack 🛠️

### Frontend
- **Vue.js 3** with TypeScript
- **Tailwind CSS** for styling
- **Pinia** for state management
- **Vue Router** for navigation
- **Tabler Icons** for beautiful icons
- **Vite** for development and building

### Backend
- **Node.js** with Express
- **Appwrite** for database, auth, and storage
- **Multer** for file upload handling
- **CORS** and **Helmet** for security
- **Rate limiting** for API protection

## Quick Start 🚀

### Prerequisites
- Node.js 18+ 
- Appwrite account with project setup
- (Optional) ShareX for automated uploads

### 1. Clone and Install

```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd frontend
npm install
```

### 2. Environment Setup

**Backend (.env)**:
```env
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Update with your Appwrite credentials
APPWRITE_PROJECT_ID=your-project-id
APPWRITE_API_KEY=your-api-key
APPWRITE_BUCKET_ID=your-bucket-id
```

**Frontend (.env)**:
```env
VITE_APPWRITE_ENDPOINT=https://api.avehubs.com/v1
VITE_APPWRITE_PROJECT_ID=your-project-id
VITE_API_URL=http://localhost:5173/api
```

### 3. Run Development Servers

```bash
# Terminal 1: Start backend
cd backend
npm run dev

# Terminal 2: Start frontend  
cd frontend
npm run dev
```

Access your application at `http://localhost:5173`

## ShareX Configuration 📤

Import the provided `sharex-config.sxcu` file in ShareX or configure manually:
- **URL**: `http://localhost:5173/api/sharex/upload`
- **Method**: POST
- **File form name**: `file`

## API Endpoints 🔌

### Authentication
- `POST /auth/login` - Email/password login
- `POST /auth/register` - Create new account
- `GET /auth/oauth/google` - Google OAuth
- `GET /auth/oauth/discord` - Discord OAuth

### Image Management
- `POST /upload` - Upload image
- `POST /sharex/upload` - ShareX compatible upload
- `GET /images` - Get user's images
- `DELETE /images/:fileId` - Delete image

---

**Built with ❤️ for the developer community**