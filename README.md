# AveImage Cloud 🚀

A modern, professional image hosting service with ShareX integration, built with **Next.js 15** (Full-Stack).

## ✨ Migration Complete!

This application has been **successfully migrated** from Vue.js + Express backend to a **full-stack Next.js 15** application with the **exact same design and functionality**.

## Features ✨

- **Modern UI/UX**: Dark theme with glass morphism effects and smooth animations
- **ShareX Integration**: Direct upload support with custom configuration
- **OAuth Authentication**: Google and Discord login support
- **Drag & Drop Upload**: Intuitive file upload interface
- **Real-time Progress**: Live upload progress tracking
- **Image Gallery**: Beautiful grid layout for your uploaded images
- **Copy to Clipboard**: One-click URL copying
- **API Integration**: Built-in Next.js API routes
- **Secure Storage**: Powered by Appwrite cloud storage
- **Mobile Responsive**: Works perfectly on all devices

## Tech Stack 🛠️

### Full-Stack Next.js 15
- **Next.js 15** with TypeScript and App Router
- **Tailwind CSS v4** for styling
- **Zustand** for state management (replaces Pinia)
- **Lucide React** for beautiful icons
- **SweetAlert2** for notifications
- **Appwrite** for database, auth, and storage
- **Built-in API Routes** (replaces Express backend)

## Quick Start 🚀

### Prerequisites
- Node.js 18+
- Appwrite account with project setup
- (Optional) ShareX for automated uploads

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Setup

Create a `.env.local` file in the root directory:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://api.avehubs.com/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
NEXT_PUBLIC_APPWRITE_BUCKET_ID=your_bucket_id
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id
NEXT_PUBLIC_APPWRITE_IMAGES_COLLECTION_ID=images
NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID=users

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=AveImage Cloud

# Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=********
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
```

### 3. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Migration Summary 📋

### What Was Migrated:

✅ **Frontend**: Vue.js 3 → Next.js 15 with React
✅ **Backend**: Express.js → Next.js API Routes
✅ **State Management**: Pinia → Zustand
✅ **Styling**: Preserved exact Tailwind CSS design
✅ **Authentication**: Appwrite integration maintained
✅ **File Upload**: Drag & drop functionality preserved
✅ **Image Gallery**: Grid layout and modal functionality
✅ **Dashboard Layout**: Sidebar, navigation, and responsive design
✅ **Auth Pages**: Login, register, forgot password
✅ **API Endpoints**: Image upload, list, and delete

### Key Improvements:

- **Single Codebase**: No more separate frontend/backend
- **Better Performance**: Next.js optimizations and SSR capabilities
- **Type Safety**: Full TypeScript integration
- **Modern Stack**: Latest Next.js 15 with App Router
- **Simplified Deployment**: Single application to deploy

## Project Structure 📁

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes (replaces Express backend)
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── auth/             # Auth form components
│   └── layouts/          # Layout components
├── lib/                  # Utilities and configurations
├── stores/               # Zustand stores
└── globals.css           # Global styles
```

## Deployment 🚀

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
