<template>
  <div class="min-h-screen bg-gray-900 flex items-center justify-center p-4">
    <div v-if="loading" class="text-center">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
      <p class="text-gray-400">Loading image...</p>
    </div>

    <div v-else-if="error" class="text-center max-w-md">
      <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
        <AlertCircle class="w-8 h-8 text-white" />
      </div>
      <h1 class="text-2xl font-bold text-gray-100 mb-2">Image Not Found</h1>
      <p class="text-gray-400 mb-6">{{ error }}</p>
      <a 
        href="/" 
        class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
      >
        <Home class="w-4 h-4 mr-2" />
        Go Home
      </a>
    </div>

    <div v-else-if="imageData" class="max-w-4xl w-full">
      <!-- Image Container -->
      <div class="bg-gray-800 rounded-lg overflow-hidden shadow-2xl">
        <!-- Image -->
        <div class="relative">
          <img 
            :src="imageUrl" 
            :alt="imageData.embed.title"
            class="w-full h-auto max-h-screen object-contain"
            @load="imageLoaded = true"
            @error="imageError = true"
          />
          
          <!-- Loading overlay -->
          <div v-if="!imageLoaded && !imageError" class="absolute inset-0 bg-gray-800 flex items-center justify-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          </div>
          
          <!-- Error overlay -->
          <div v-if="imageError" class="absolute inset-0 bg-gray-800 flex items-center justify-center">
            <div class="text-center">
              <AlertCircle class="w-12 h-12 text-red-400 mx-auto mb-2" />
              <p class="text-gray-400">Failed to load image</p>
            </div>
          </div>
        </div>

        <!-- Image Info -->
        <div class="p-6 border-t border-gray-700">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h1 class="text-xl font-semibold text-gray-100 mb-1">{{ imageData.embed.title }}</h1>
              <p v-if="imageData.embed.description" class="text-gray-400 text-sm">{{ imageData.embed.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <button
                @click="copyImageUrl"
                class="p-2 text-gray-400 hover:text-gray-300 transition-colors"
                title="Copy image URL"
              >
                <Copy class="w-4 h-4" />
              </button>
              <button
                @click="downloadImage"
                class="p-2 text-gray-400 hover:text-gray-300 transition-colors"
                title="Download image"
              >
                <Download class="w-4 h-4" />
              </button>
              <button
                @click="shareImage"
                class="p-2 text-gray-400 hover:text-gray-300 transition-colors"
                title="Share image"
              >
                <Share class="w-4 h-4" />
              </button>
            </div>
          </div>

          <!-- Image Stats -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div class="text-center">
              <p class="text-sm text-gray-400">Size</p>
              <p class="text-gray-100 font-medium">{{ formatFileSize(imageData.embed.size) }}</p>
            </div>
            <div class="text-center">
              <p class="text-sm text-gray-400">Views</p>
              <p class="text-gray-100 font-medium">{{ imageData.embed.views }}</p>
            </div>
            <div class="text-center">
              <p class="text-sm text-gray-400">Uploaded</p>
              <p class="text-gray-100 font-medium">{{ formatDate(imageData.embed.uploaded_at) }}</p>
            </div>
            <div class="text-center">
              <p class="text-sm text-gray-400">Format</p>
              <p class="text-gray-100 font-medium">{{ getFileExtension(imageData.embed.filename) }}</p>
            </div>
          </div>

          <!-- Footer -->
          <div class="pt-4 border-t border-gray-700">
            <p class="text-center text-sm text-gray-400">
              {{ imageData.embed.footer }}
            </p>
          </div>
        </div>
      </div>

      <!-- URL Parameters Info -->
      <div v-if="hasUrlParams" class="mt-4 bg-gray-800 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-100 mb-2">Image Parameters</h3>
        <div class="flex flex-wrap gap-2">
          <span v-if="urlParams.width" class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
            Width: {{ urlParams.width }}px
          </span>
          <span v-if="urlParams.height" class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
            Height: {{ urlParams.height }}px
          </span>
          <span v-if="urlParams.quality" class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
            Quality: {{ urlParams.quality }}%
          </span>
          <span v-if="urlParams.format" class="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
            Format: {{ urlParams.format }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { AlertCircle, Home, Copy, Download, Share } from 'lucide-vue-next'
import api from '../config/api.js'
import Swal from 'sweetalert2'

const route = useRoute()
const loading = ref(true)
const error = ref(null)
const imageData = ref(null)
const imageLoaded = ref(false)
const imageError = ref(false)

const urlParams = computed(() => {
  return {
    width: route.query.width,
    height: route.query.height,
    quality: route.query.quality,
    format: route.query.format
  }
})

const hasUrlParams = computed(() => {
  return Object.values(urlParams.value).some(param => param !== undefined)
})

const imageUrl = computed(() => {
  if (!imageData.value) return ''
  
  let url = imageData.value.embed.image_url
  
  // Add URL parameters if present
  const params = new URLSearchParams()
  if (urlParams.value.width) params.append('width', urlParams.value.width)
  if (urlParams.value.height) params.append('height', urlParams.value.height)
  if (urlParams.value.quality) params.append('quality', urlParams.value.quality)
  if (urlParams.value.format) params.append('output', urlParams.value.format)
  
  if (params.toString()) {
    url += (url.includes('?') ? '&' : '?') + params.toString()
  }
  
  return url
})

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

const getFileExtension = (filename) => {
  if (!filename) return 'Unknown'
  const ext = filename.split('.').pop()
  return ext ? ext.toUpperCase() : 'Unknown'
}

const copyImageUrl = async () => {
  try {
    const currentUrl = window.location.href
    await navigator.clipboard.writeText(currentUrl)
    Swal.fire({
      icon: 'success',
      title: 'Copied!',
      text: 'Image URL copied to clipboard',
      timer: 2000,
      showConfirmButton: false
    })
  } catch (error) {
    console.error('Failed to copy URL:', error)
    Swal.fire('Error', 'Failed to copy URL', 'error')
  }
}

const downloadImage = () => {
  if (!imageData.value) return
  
  const link = document.createElement('a')
  link.href = imageUrl.value
  link.download = imageData.value.embed.filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const shareImage = async () => {
  const currentUrl = window.location.href
  
  if (navigator.share) {
    try {
      await navigator.share({
        title: imageData.value.embed.title,
        text: imageData.value.embed.description,
        url: currentUrl
      })
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error sharing:', error)
      }
    }
  } else {
    // Fallback to copying URL
    copyImageUrl()
  }
}

const loadImageData = async () => {
  try {
    loading.value = true
    const uuid = route.params.uuid
    
    if (!uuid) {
      throw new Error('Invalid image URL')
    }

    const response = await api.get(`/images/embed/${uuid}`, {
      params: urlParams.value
    })
    
    if (response.data.success) {
      imageData.value = response.data
      
      // Set page title and meta tags
      document.title = `${imageData.value.embed.title} - AveImgCloud`
      
      // Set meta tags for social sharing
      const setMetaTag = (property, content) => {
        let meta = document.querySelector(`meta[property="${property}"]`)
        if (!meta) {
          meta = document.createElement('meta')
          meta.setAttribute('property', property)
          document.head.appendChild(meta)
        }
        meta.setAttribute('content', content)
      }
      
      setMetaTag('og:title', imageData.value.embed.title)
      setMetaTag('og:description', imageData.value.embed.description || 'Image hosted on AveImgCloud')
      setMetaTag('og:image', imageUrl.value)
      setMetaTag('og:url', window.location.href)
      setMetaTag('og:type', 'website')
      
      setMetaTag('twitter:card', 'summary_large_image')
      setMetaTag('twitter:title', imageData.value.embed.title)
      setMetaTag('twitter:description', imageData.value.embed.description || 'Image hosted on AveImgCloud')
      setMetaTag('twitter:image', imageUrl.value)
      
    } else {
      throw new Error(response.data.error || 'Image not found')
    }
  } catch (err) {
    console.error('Failed to load image:', err)
    error.value = err.response?.data?.error || err.message || 'Failed to load image'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadImageData()
})
</script>
