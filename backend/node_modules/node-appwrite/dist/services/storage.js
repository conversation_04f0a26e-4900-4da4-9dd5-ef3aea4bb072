'use strict';

var client = require('../client');

class Storage {
  constructor(client) {
    this.client = client;
  }
  /**
   * List buckets
   *
   * Get a list of all the storage buckets. You can use the query params to filter your results.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.BucketList>}
   */
  async listBuckets(queries, search) {
    const apiPath = "/storage/buckets";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create bucket
   *
   * Create a new storage bucket.
   *
   * @param {string} bucketId
   * @param {string} name
   * @param {string[]} permissions
   * @param {boolean} fileSecurity
   * @param {boolean} enabled
   * @param {number} maximumFileSize
   * @param {string[]} allowedFileExtensions
   * @param {Compression} compression
   * @param {boolean} encryption
   * @param {boolean} antivirus
   * @throws {AppwriteException}
   * @returns {Promise<Models.Bucket>}
   */
  async createBucket(bucketId, name, permissions, fileSecurity, enabled, maximumFileSize, allowedFileExtensions, compression, encryption, antivirus) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/storage/buckets";
    const payload = {};
    if (typeof bucketId !== "undefined") {
      payload["bucketId"] = bucketId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    if (typeof fileSecurity !== "undefined") {
      payload["fileSecurity"] = fileSecurity;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof maximumFileSize !== "undefined") {
      payload["maximumFileSize"] = maximumFileSize;
    }
    if (typeof allowedFileExtensions !== "undefined") {
      payload["allowedFileExtensions"] = allowedFileExtensions;
    }
    if (typeof compression !== "undefined") {
      payload["compression"] = compression;
    }
    if (typeof encryption !== "undefined") {
      payload["encryption"] = encryption;
    }
    if (typeof antivirus !== "undefined") {
      payload["antivirus"] = antivirus;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get bucket
   *
   * Get a storage bucket by its unique ID. This endpoint response returns a JSON object with the storage bucket metadata.
   *
   * @param {string} bucketId
   * @throws {AppwriteException}
   * @returns {Promise<Models.Bucket>}
   */
  async getBucket(bucketId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    const apiPath = "/storage/buckets/{bucketId}".replace("{bucketId}", bucketId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update bucket
   *
   * Update a storage bucket by its unique ID.
   *
   * @param {string} bucketId
   * @param {string} name
   * @param {string[]} permissions
   * @param {boolean} fileSecurity
   * @param {boolean} enabled
   * @param {number} maximumFileSize
   * @param {string[]} allowedFileExtensions
   * @param {Compression} compression
   * @param {boolean} encryption
   * @param {boolean} antivirus
   * @throws {AppwriteException}
   * @returns {Promise<Models.Bucket>}
   */
  async updateBucket(bucketId, name, permissions, fileSecurity, enabled, maximumFileSize, allowedFileExtensions, compression, encryption, antivirus) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof name === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/storage/buckets/{bucketId}".replace("{bucketId}", bucketId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    if (typeof fileSecurity !== "undefined") {
      payload["fileSecurity"] = fileSecurity;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    if (typeof maximumFileSize !== "undefined") {
      payload["maximumFileSize"] = maximumFileSize;
    }
    if (typeof allowedFileExtensions !== "undefined") {
      payload["allowedFileExtensions"] = allowedFileExtensions;
    }
    if (typeof compression !== "undefined") {
      payload["compression"] = compression;
    }
    if (typeof encryption !== "undefined") {
      payload["encryption"] = encryption;
    }
    if (typeof antivirus !== "undefined") {
      payload["antivirus"] = antivirus;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete bucket
   *
   * Delete a storage bucket by its unique ID.
   *
   * @param {string} bucketId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteBucket(bucketId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    const apiPath = "/storage/buckets/{bucketId}".replace("{bucketId}", bucketId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List files
   *
   * Get a list of all the user files. You can use the query params to filter your results.
   *
   * @param {string} bucketId
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.FileList>}
   */
  async listFiles(bucketId, queries, search) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files".replace("{bucketId}", bucketId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create file
       *
       * Create a new file. Before using this route, you should create a new bucket resource using either a [server integration](https://appwrite.io/docs/server/storage#storageCreateBucket) API or directly from your Appwrite console.
  
  Larger files should be uploaded using multiple requests with the [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range) header to send a partial request with a maximum supported chunk of `5MB`. The `content-range` header values should always be in bytes.
  
  When the first request is sent, the server will return the **File** object, and the subsequent part request must include the file&#039;s **id** in `x-appwrite-id` header to allow the server to know that the partial upload is for the existing file and not for a new one.
  
  If you&#039;re creating a new file using one of the Appwrite SDKs, all the chunking logic will be managed by the SDK internally.
  
       *
       * @param {string} bucketId
       * @param {string} fileId
       * @param {File} file
       * @param {string[]} permissions
       * @throws {AppwriteException}
       * @returns {Promise<Models.File>}
       */
  async createFile(bucketId, fileId, file, permissions, onProgress = (progress) => {
  }) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    if (typeof file === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "file"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files".replace("{bucketId}", bucketId);
    const payload = {};
    if (typeof fileId !== "undefined") {
      payload["fileId"] = fileId;
    }
    if (typeof file !== "undefined") {
      payload["file"] = file;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "multipart/form-data"
    };
    return await this.client.chunkedUpload(
      "post",
      uri,
      apiHeaders,
      payload,
      onProgress
    );
  }
  /**
   * Get file
   *
   * Get a file by its unique ID. This endpoint response returns a JSON object with the file metadata.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @throws {AppwriteException}
   * @returns {Promise<Models.File>}
   */
  async getFile(bucketId, fileId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update file
   *
   * Update a file by its unique ID. Only users with write permissions have access to update this resource.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @param {string} name
   * @param {string[]} permissions
   * @throws {AppwriteException}
   * @returns {Promise<Models.File>}
   */
  async updateFile(bucketId, fileId, name, permissions) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete File
   *
   * Delete a file by its unique ID. Only users with write permissions have access to delete this resource.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteFile(bucketId, fileId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get file for download
   *
   * Get a file content by its unique ID. The endpoint response return with a &#039;Content-Disposition: attachment&#039; header that tells the browser to start downloading the file to user downloads directory.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @throws {AppwriteException}
   * @returns {Promise<ArrayBuffer>}
   */
  async getFileDownload(bucketId, fileId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}/download".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload,
      "arrayBuffer"
    );
  }
  /**
   * Get file preview
   *
   * Get a file preview image. Currently, this method supports preview for image files (jpg, png, and gif), other supported formats, like pdf, docs, slides, and spreadsheets, will return the file icon image. You can also pass query string arguments for cutting and resizing your preview image. Preview is supported only for image files smaller than 10MB.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @param {number} width
   * @param {number} height
   * @param {ImageGravity} gravity
   * @param {number} quality
   * @param {number} borderWidth
   * @param {string} borderColor
   * @param {number} borderRadius
   * @param {number} opacity
   * @param {number} rotation
   * @param {string} background
   * @param {ImageFormat} output
   * @throws {AppwriteException}
   * @returns {Promise<ArrayBuffer>}
   */
  async getFilePreview(bucketId, fileId, width, height, gravity, quality, borderWidth, borderColor, borderRadius, opacity, rotation, background, output) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}/preview".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    if (typeof width !== "undefined") {
      payload["width"] = width;
    }
    if (typeof height !== "undefined") {
      payload["height"] = height;
    }
    if (typeof gravity !== "undefined") {
      payload["gravity"] = gravity;
    }
    if (typeof quality !== "undefined") {
      payload["quality"] = quality;
    }
    if (typeof borderWidth !== "undefined") {
      payload["borderWidth"] = borderWidth;
    }
    if (typeof borderColor !== "undefined") {
      payload["borderColor"] = borderColor;
    }
    if (typeof borderRadius !== "undefined") {
      payload["borderRadius"] = borderRadius;
    }
    if (typeof opacity !== "undefined") {
      payload["opacity"] = opacity;
    }
    if (typeof rotation !== "undefined") {
      payload["rotation"] = rotation;
    }
    if (typeof background !== "undefined") {
      payload["background"] = background;
    }
    if (typeof output !== "undefined") {
      payload["output"] = output;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload,
      "arrayBuffer"
    );
  }
  /**
   * Get file for view
   *
   * Get a file content by its unique ID. This endpoint is similar to the download method but returns with no  &#039;Content-Disposition: attachment&#039; header.
   *
   * @param {string} bucketId
   * @param {string} fileId
   * @throws {AppwriteException}
   * @returns {Promise<ArrayBuffer>}
   */
  async getFileView(bucketId, fileId) {
    if (typeof bucketId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "bucketId"');
    }
    if (typeof fileId === "undefined") {
      throw new client.AppwriteException('Missing required parameter: "fileId"');
    }
    const apiPath = "/storage/buckets/{bucketId}/files/{fileId}/view".replace("{bucketId}", bucketId).replace("{fileId}", fileId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload,
      "arrayBuffer"
    );
  }
}

exports.Storage = Storage;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=storage.js.map