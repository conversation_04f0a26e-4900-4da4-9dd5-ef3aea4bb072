{"version": 3, "sources": ["../../src/enums/flag.ts"], "names": ["Flag"], "mappings": ";AAAO,IAAK,OAAL,kBAAKA,UAAL;AACH,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,wBAAqB;AACrB,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,uBAAoB;AACpB,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,0BAAuB;AACvB,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,sBAAmB;AACnB,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,4BAAyB;AACzB,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,kCAA+B;AAC/B,EAAAA,MAAA,wBAAqB;AACrB,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,mBAAgB;AAChB,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,uBAAoB;AACpB,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,iCAA8B;AAC9B,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,mBAAgB;AAChB,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,kBAAe;AACf,EAAAA,MAAA,sBAAmB;AACnB,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,2BAAwB;AACxB,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,wBAAqB;AACrB,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,kCAA+B;AAC/B,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,mBAAgB;AAChB,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,qBAAkB;AAClB,EAAAA,MAAA,oBAAiB;AACjB,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,oBAAiB;AACjB,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,qBAAkB;AAClB,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,oBAAiB;AACjB,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,wBAAqB;AACrB,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,UAAO;AACP,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,kBAAe;AACf,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,uBAAoB;AACpB,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,kBAAe;AACf,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,kCAA+B;AAC/B,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,WAAQ;AACR,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,YAAS;AACT,EAAAA,MAAA,cAAW;AAnMH,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum Flag {\n    Afghanistan = 'af',\n    Angola = 'ao',\n    Albania = 'al',\n    Andorra = 'ad',\n    UnitedArabEmirates = 'ae',\n    Argentina = 'ar',\n    Armenia = 'am',\n    AntiguaAndBarbuda = 'ag',\n    Australia = 'au',\n    Austria = 'at',\n    Azerbaijan = 'az',\n    Burundi = 'bi',\n    Belgium = 'be',\n    Benin = 'bj',\n    BurkinaFaso = 'bf',\n    Bangladesh = 'bd',\n    Bulgaria = 'bg',\n    Bahrain = 'bh',\n    Bahamas = 'bs',\n    BosniaAndHerzegovina = 'ba',\n    Belarus = 'by',\n    Belize = 'bz',\n    Bolivia = 'bo',\n    Brazil = 'br',\n    Barbados = 'bb',\n    BruneiDarussalam = 'bn',\n    Bhutan = 'bt',\n    Botswana = 'bw',\n    CentralAfricanRepublic = 'cf',\n    Canada = 'ca',\n    Switzerland = 'ch',\n    Chile = 'cl',\n    China = 'cn',\n    CoteDIvoire = 'ci',\n    Cameroon = 'cm',\n    DemocraticRepublicOfTheCongo = 'cd',\n    RepublicOfTheCongo = 'cg',\n    Colombia = 'co',\n    Comoros = 'km',\n    CapeVerde = 'cv',\n    CostaRica = 'cr',\n    Cuba = 'cu',\n    Cyprus = 'cy',\n    CzechRepublic = 'cz',\n    Germany = 'de',\n    Djibouti = 'dj',\n    Dominica = 'dm',\n    Denmark = 'dk',\n    DominicanRepublic = 'do',\n    Algeria = 'dz',\n    Ecuador = 'ec',\n    Egypt = 'eg',\n    Eritrea = 'er',\n    Spain = 'es',\n    Estonia = 'ee',\n    Ethiopia = 'et',\n    Finland = 'fi',\n    Fiji = 'fj',\n    France = 'fr',\n    MicronesiaFederatedStatesOf = 'fm',\n    Gabon = 'ga',\n    UnitedKingdom = 'gb',\n    Georgia = 'ge',\n    Ghana = 'gh',\n    Guinea = 'gn',\n    Gambia = 'gm',\n    GuineaBissau = 'gw',\n    EquatorialGuinea = 'gq',\n    Greece = 'gr',\n    Grenada = 'gd',\n    Guatemala = 'gt',\n    Guyana = 'gy',\n    Honduras = 'hn',\n    Croatia = 'hr',\n    Haiti = 'ht',\n    Hungary = 'hu',\n    Indonesia = 'id',\n    India = 'in',\n    Ireland = 'ie',\n    IranIslamicRepublicOf = 'ir',\n    Iraq = 'iq',\n    Iceland = 'is',\n    Israel = 'il',\n    Italy = 'it',\n    Jamaica = 'jm',\n    Jordan = 'jo',\n    Japan = 'jp',\n    Kazakhstan = 'kz',\n    Kenya = 'ke',\n    Kyrgyzstan = 'kg',\n    Cambodia = 'kh',\n    Kiribati = 'ki',\n    SaintKittsAndNevis = 'kn',\n    SouthKorea = 'kr',\n    Kuwait = 'kw',\n    LaoPeopleSDemocraticRepublic = 'la',\n    Lebanon = 'lb',\n    Liberia = 'lr',\n    Libya = 'ly',\n    SaintLucia = 'lc',\n    Liechtenstein = 'li',\n    SriLanka = 'lk',\n    Lesotho = 'ls',\n    Lithuania = 'lt',\n    Luxembourg = 'lu',\n    Latvia = 'lv',\n    Morocco = 'ma',\n    Monaco = 'mc',\n    Moldova = 'md',\n    Madagascar = 'mg',\n    Maldives = 'mv',\n    Mexico = 'mx',\n    MarshallIslands = 'mh',\n    NorthMacedonia = 'mk',\n    Mali = 'ml',\n    Malta = 'mt',\n    Myanmar = 'mm',\n    Montenegro = 'me',\n    Mongolia = 'mn',\n    Mozambique = 'mz',\n    Mauritania = 'mr',\n    Mauritius = 'mu',\n    Malawi = 'mw',\n    Malaysia = 'my',\n    Namibia = 'na',\n    Niger = 'ne',\n    Nigeria = 'ng',\n    Nicaragua = 'ni',\n    Netherlands = 'nl',\n    Norway = 'no',\n    Nepal = 'np',\n    Nauru = 'nr',\n    NewZealand = 'nz',\n    Oman = 'om',\n    Pakistan = 'pk',\n    Panama = 'pa',\n    Peru = 'pe',\n    Philippines = 'ph',\n    Palau = 'pw',\n    PapuaNewGuinea = 'pg',\n    Poland = 'pl',\n    FrenchPolynesia = 'pf',\n    NorthKorea = 'kp',\n    Portugal = 'pt',\n    Paraguay = 'py',\n    Qatar = 'qa',\n    Romania = 'ro',\n    Russia = 'ru',\n    Rwanda = 'rw',\n    SaudiArabia = 'sa',\n    Sudan = 'sd',\n    Senegal = 'sn',\n    Singapore = 'sg',\n    SolomonIslands = 'sb',\n    SierraLeone = 'sl',\n    ElSalvador = 'sv',\n    SanMarino = 'sm',\n    Somalia = 'so',\n    Serbia = 'rs',\n    SouthSudan = 'ss',\n    SaoTomeAndPrincipe = 'st',\n    Suriname = 'sr',\n    Slovakia = 'sk',\n    Slovenia = 'si',\n    Sweden = 'se',\n    Eswatini = 'sz',\n    Seychelles = 'sc',\n    Syria = 'sy',\n    Chad = 'td',\n    Togo = 'tg',\n    Thailand = 'th',\n    Tajikistan = 'tj',\n    Turkmenistan = 'tm',\n    TimorLeste = 'tl',\n    Tonga = 'to',\n    TrinidadAndTobago = 'tt',\n    Tunisia = 'tn',\n    Turkey = 'tr',\n    Tuvalu = 'tv',\n    Tanzania = 'tz',\n    Uganda = 'ug',\n    Ukraine = 'ua',\n    Uruguay = 'uy',\n    UnitedStates = 'us',\n    Uzbekistan = 'uz',\n    VaticanCity = 'va',\n    SaintVincentAndTheGrenadines = 'vc',\n    Venezuela = 've',\n    Vietnam = 'vn',\n    Vanuatu = 'vu',\n    Samoa = 'ws',\n    Yemen = 'ye',\n    SouthAfrica = 'za',\n    Zambia = 'zm',\n    Zimbabwe = 'zw',\n}"]}