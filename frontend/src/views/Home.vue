<template>
    <LayoutDashboard>
        <div class="space-y-6">
            <!-- Welcome Section -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-100">
                        Welcome{{ authStore.userName ? `, ${authStore.userName}` : '' }}
                    </h1>
                    <p class="text-gray-400">Upload and manage your images with ease.</p>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div v-for="(stat, index) in stats" :key="index"
                    class="p-6 rounded-lg bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-gray-700/50 hover:border-purple-500/50 transition-colors group">
                    <div class="flex justify-between items-start mb-4">
                        <span class="text-gray-400 text-sm">{{ stat.label }}</span>
                        <div
                            class="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center text-purple-400 group-hover:bg-purple-500 group-hover:text-white transition-colors">
                            <component :is="stat.icon" class="w-4 h-4" />
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-gray-100 mb-2">{{ stat.value }}</div>
                    <div v-if="stat.trend" class="flex items-center gap-2 text-sm">
                        <span :class="stat.trend === 'up' ? 'text-green-400' : 'text-red-400'">
                            <component :is="stat.trend === 'up' ? TrendingUpIcon : TrendingDownIcon"
                                class="w-4 h-4 inline" />
                            {{ stat.percentage }}%
                        </span>
                        <span class="text-gray-500">vs last week</span>
                    </div>
                </div>
            </div>

            <!-- Image Upload Section -->
            <div class="rounded-lg bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-gray-700/50 p-6">
                <h2 class="text-xl font-bold text-gray-100 mb-4">Upload Images</h2>
                <ImageUpload />
            </div>

            <!-- Recent Images -->
            <div class="rounded-lg bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-gray-700/50 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-100">Recent Images</h2>
                    <router-link 
                        to="/dashboard/images" 
                        class="text-sm text-purple-400 hover:text-purple-300 transition-colors"
                    >
                        View All →
                    </router-link>
                </div>
                
                <div v-if="recentImages.length === 0" class="text-gray-400 text-center py-8">
                    No images uploaded yet
                </div>
                
                <div v-else class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div
                        v-for="image in recentImages.slice(0, 6)"
                        :key="image.id"
                        class="group relative aspect-square bg-gray-700 rounded-lg overflow-hidden hover:ring-2 hover:ring-purple-500/50 transition-all"
                    >
                        <img
                            :src="image.url"
                            :alt="image.name"
                            class="w-full h-full object-cover cursor-pointer"
                            @click="viewImage(image)"
                        />
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <button
                                @click="copyImageUrl(image.url)"
                                class="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                                title="Copy URL"
                            >
                                <Copy class="w-4 h-4 text-gray-700" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="rounded-lg bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-gray-700/50 p-6">
                    <h3 class="text-lg font-semibold text-gray-100 mb-3">ShareX Integration</h3>
                    <p class="text-gray-400 text-sm mb-4">
                        Download the ShareX configuration file to start uploading directly from your desktop.
                    </p>
                    <button
                        @click="downloadShareXConfig"
                        class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center"
                    >
                        <Download class="w-4 h-4 mr-2" />
                        Download ShareX Config
                    </button>
                </div>
                
                <div class="rounded-lg bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-gray-700/50 p-6">
                    <h3 class="text-lg font-semibold text-gray-100 mb-3">API Access</h3>
                    <p class="text-gray-400 text-sm mb-4">
                        Manage your API keys and integrate with other applications.
                    </p>
                    <router-link
                        to="/dashboard/settings/api"
                        class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
                    >
                        <Key class="w-4 h-4 mr-2" />
                        Manage API Keys
                    </router-link>
                </div>
            </div>
        </div>
    </LayoutDashboard>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import LayoutDashboard from '../components/LayoutDashboard.vue'
import ImageUpload from '../components/ImageUpload.vue'
import { useAuthStore } from '../stores/auth.js'
import { useImagesStore } from '../stores/images.js'
import { 
    Image, 
    Upload, 
    HardDrive, 
    Users, 
    TrendingUp as TrendingUpIcon, 
    TrendingDown as TrendingDownIcon,
    Copy,
    Download,
    Key
} from 'lucide-vue-next'
import Swal from 'sweetalert2'

const authStore = useAuthStore()
const imageStore = useImagesStore()

// Initialize auth state
onMounted(async () => {
    await authStore.initAuth()
    if (authStore.isAuthenticated) {
        await imageStore.fetchImages(1, 10) // Fetch first 10 images for recent display
    }
})

const recentImages = computed(() => imageStore.sortedImages.slice(0, 6))

const stats = computed(() => [
    {
        label: 'Total Images',
        value: imageStore.totalImages,
        icon: Image,
        trend: imageStore.totalImages > 0 ? 'up' : null,
        percentage: imageStore.totalImages > 0 ? Math.min(100, imageStore.totalImages * 5) : 0
    },
    {
        label: 'Storage Used',
        value: formatFileSize(imageStore.totalSize),
        icon: HardDrive,
        trend: imageStore.totalSize > 0 ? 'up' : null,
        percentage: imageStore.totalSize > 0 ? Math.min(100, Math.floor(imageStore.totalSize / (1024 * 1024))) : 0
    },
    {
        label: 'This Month',
        value: getThisMonthUploads(),
        icon: Upload,
        trend: getThisMonthUploads() > 0 ? 'up' : null,
        percentage: getThisMonthUploads() * 10
    },
    {
        label: 'Account Type',
        value: authStore.isAdmin ? 'Admin' : 'User',
        icon: Users,
    }
])

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getThisMonthUploads() {
    const now = new Date()
    const thisMonth = now.getMonth()
    const thisYear = now.getFullYear()
    
    return imageStore.images.filter(image => {
        const uploadDate = new Date(image.uploadedAt)
        return uploadDate.getMonth() === thisMonth && uploadDate.getFullYear() === thisYear
    }).length
}

const viewImage = (image) => {
    // This could open a modal or navigate to image detail
    window.open(image.url, '_blank')
}

const copyImageUrl = async (url) => {
    const result = await imageStore.copyImageUrl(url)
    if (result.success) {
        Swal.fire({
            icon: 'success',
            title: 'Copied!',
            text: result.message,
            timer: 1500,
            showConfirmButton: false
        })
    } else {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: result.error
        })
    }
}

const downloadShareXConfig = () => {
    const config = {
        "Version": "15.0.0",
        "Name": "AveImage Cloud",
        "DestinationType": "ImageUploader",
        "RequestMethod": "POST",
        "RequestURL": `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api'}/images/sharex/upload`,
        "Headers": {
            "User-Agent": "ShareX/AveImage-Cloud",
            "Authorization": `Bearer ${localStorage.getItem('session')}`
        },
        "Body": "MultipartFormData",
        "FileFormName": "file",
        "URL": "$response$",
        "ErrorMessage": "$json:error$"
    }
    
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = 'aveimage-cloud.sxcu'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    Swal.fire({
        icon: 'success',
        title: 'Downloaded!',
        text: 'ShareX configuration file has been downloaded.',
        timer: 2000,
        showConfirmButton: false
    })
}
</script>
