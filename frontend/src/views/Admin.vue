<template>
  <LayoutDashboard>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-white">Administration</h1>
          <p class="text-gray-400">Manage users, system settings, and analytics</p>
        </div>
        <div class="flex items-center space-x-2">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-900 text-purple-200">
            <Crown class="w-3 h-3 mr-1" />
            Admin Panel
          </span>
        </div>
      </div>

      <!-- Tabs -->
      <div class="border-b border-gray-800">
        <nav class="flex space-x-8">
          <button v-for="tab in tabs" :key="tab.key"
                  @click="activeTab = tab.key"
                  :class="[
                    'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                    activeTab === tab.key
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  ]">
            <component :is="tab.icon" class="w-4 h-4 inline mr-2" />
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-6">
        <AdminOverview />
      </div>

      <!-- Users Tab -->
      <div v-if="activeTab === 'users'" class="space-y-6">
        <AdminUsers />
      </div>

      <!-- Store Tab -->
      <div v-if="activeTab === 'store'" class="space-y-6">
        <AdminStore />
      </div>

      <!-- Settings Tab -->
      <div v-if="activeTab === 'settings'" class="space-y-6">
        <div class="text-center py-12">
          <Settings class="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-xl font-semibold text-white mb-2">System Settings</h3>
          <p class="text-gray-400">Global system settings will be available soon.</p>
        </div>
      </div>
    </div>
  </LayoutDashboard>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LayoutDashboard from '../components/LayoutDashboard.vue'
import AdminUsers from '../components/Admin/AdminUsers.vue'
import AdminOverview from '../components/Admin/AdminOverview.vue'
import AdminStore from '../components/Admin/AdminStore.vue'
import { 
  Crown, BarChart3, Users, Settings, 
  HardDrive, Coins, Shield 
} from 'lucide-vue-next'
import api from '../config/api.js'

const activeTab = ref('overview')
const stats = ref({})

const tabs = [
  { key: 'overview', name: 'Overview', icon: BarChart3 },
  { key: 'users', name: 'Users', icon: Users },
  { key: 'store', name: 'Store', icon: Coins },
  { key: 'settings', name: 'Settings', icon: Settings }
]

const loadStats = async () => {
  try {
    const response = await api.get('/admin/overview')
    if (response.data.success) {
      stats.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load admin stats:', error)
  }
}

onMounted(() => {
  document.title = 'Admin Panel - AveImgCloud'
  loadStats()
})
</script>
