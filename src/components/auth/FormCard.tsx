'use client';

import { HelpCircle } from 'lucide-react';

interface FormCardProps {
  title: string;
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent) => void;
}

export default function FormCard({ title, children, onSubmit }: FormCardProps) {
  return (
    <div className="w-full max-w-md bg-[#12121f]/90 backdrop-blur-sm rounded-lg shadow-xl p-8">
      {/* Logo and Title */}
      <div className="flex items-center gap-3 mb-6">
        <img src="https://github.com/mythicalltd.png" alt="Logo" className="w-8 h-8" />
        <h1 className="text-xl font-semibold text-white">MythicalSystems</h1>
      </div>

      {/* Form Title */}
      <h2 className="text-xl text-white mb-6">{title}</h2>

      {/* Form Content */}
      <form onSubmit={onSubmit}>
        {children}
      </form>

      {/* <PERSON>er Links */}
      <div className="mt-6 flex items-center justify-center gap-6 text-sm text-gray-400">
        <a href="#" className="flex items-center gap-2 hover:text-white transition-colors">
          <HelpCircle className="w-4 h-4" />
          Support center
        </a>
      </div>
    </div>
  );
}
