import rateLimit from 'express-rate-limit';
import { config } from '../config/config.js';

// General rate limiting
export const generalLimiter = rateLimit({
  windowMs: config.rateLimiting.windowMs,
  max: config.rateLimiting.max,
  message: {
    success: false,
    error: 'Too many requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Upload rate limiting (more strict)
export const uploadLimiter = rateLimit({
  windowMs: config.rateLimiting.upload.windowMs,
  max: config.rateLimiting.upload.max,
  message: {
    success: false,
    error: 'Too many uploads, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Auth rate limiting (for login attempts)
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    error: 'Too many authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});
