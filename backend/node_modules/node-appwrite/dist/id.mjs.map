{"version": 3, "sources": ["../src/id.ts"], "names": [], "mappings": ";AAAO,IAAM,KAAN,MAAM,IAAG;AAAA;AAAA;AAAA,EAGZ,OAAO,gBAAwB;AAC3B,UAAM,MAAM,oBAAI,KAAK;AACrB,UAAM,MAAM,KAAK,MAAM,IAAI,QAAQ,IAAI,GAAI;AAC3C,UAAM,OAAO,IAAI,gBAAgB;AAGjC,UAAM,eAAe,IAAI,SAAS,EAAE,IAAI,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACzE,WAAO;AAAA,EACX;AAAA,EAEA,OAAc,OAAO,IAAoB;AACrC,WAAO;AAAA,EACX;AAAA,EAEA,OAAc,OAAO,UAAkB,GAAW;AAE9C,UAAM,SAAS,IAAG,cAAc;AAChC,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,YAAM,iBAAiB,KAAK,MAAM,KAAK,OAAO,IAAI,EAAE,EAAE,SAAS,EAAE;AACjE,uBAAiB;AAAA,IACrB;AACA,WAAO,SAAS;AAAA,EACpB;AACJ", "sourcesContent": ["export class ID {\n    // Generate an hex ID based on timestamp\n    // Recreated from https://www.php.net/manual/en/function.uniqid.php\n    static #hexTimestamp(): string {\n        const now = new Date();\n        const sec = Math.floor(now.getTime() / 1000);\n        const msec = now.getMilliseconds();\n\n        // Convert to hexadecimal\n        const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n        return hexTimestamp;\n    }\n\n    public static custom(id: string): string {\n        return id\n    }\n\n    public static unique(padding: number = 7): string {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = ID.#hexTimestamp();\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n"]}