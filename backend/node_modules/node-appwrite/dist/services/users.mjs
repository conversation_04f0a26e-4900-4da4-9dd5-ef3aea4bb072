import { AppwriteException } from '../client.mjs';

// src/services/users.ts
var Users = class {
  constructor(client) {
    this.client = client;
  }
  /**
   * List users
   *
   * Get a list of all the project&#039;s users. You can use the query params to filter your results.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.UserList<Preferences>>}
   */
  async list(queries, search) {
    const apiPath = "/users";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user
   *
   * Create a new user.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} phone
   * @param {string} password
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async create(userId, email, phone, password, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof phone !== "undefined") {
      payload["phone"] = phone;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with Argon2 password
   *
   * Create a new user. Password provided must be hashed with the [Argon2](https://en.wikipedia.org/wiki/Argon2) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createArgon2User(userId, email, password, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/argon2";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with bcrypt password
   *
   * Create a new user. Password provided must be hashed with the [Bcrypt](https://en.wikipedia.org/wiki/Bcrypt) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createBcryptUser(userId, email, password, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/bcrypt";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List Identities
   *
   * Get identities for all users.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.IdentityList>}
   */
  async listIdentities(queries, search) {
    const apiPath = "/users/identities";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete identity
   *
   * Delete an identity by its unique ID.
   *
   * @param {string} identityId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteIdentity(identityId) {
    if (typeof identityId === "undefined") {
      throw new AppwriteException('Missing required parameter: "identityId"');
    }
    const apiPath = "/users/identities/{identityId}".replace("{identityId}", identityId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with MD5 password
   *
   * Create a new user. Password provided must be hashed with the [MD5](https://en.wikipedia.org/wiki/MD5) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createMD5User(userId, email, password, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/md5";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with PHPass password
   *
   * Create a new user. Password provided must be hashed with the [PHPass](https://www.openwall.com/phpass/) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createPHPassUser(userId, email, password, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/phpass";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with Scrypt password
   *
   * Create a new user. Password provided must be hashed with the [Scrypt](https://github.com/Tarsnap/scrypt) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} passwordSalt
   * @param {number} passwordCpu
   * @param {number} passwordMemory
   * @param {number} passwordParallel
   * @param {number} passwordLength
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createScryptUser(userId, email, password, passwordSalt, passwordCpu, passwordMemory, passwordParallel, passwordLength, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    if (typeof passwordSalt === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordSalt"');
    }
    if (typeof passwordCpu === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordCpu"');
    }
    if (typeof passwordMemory === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordMemory"');
    }
    if (typeof passwordParallel === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordParallel"');
    }
    if (typeof passwordLength === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordLength"');
    }
    const apiPath = "/users/scrypt";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof passwordSalt !== "undefined") {
      payload["passwordSalt"] = passwordSalt;
    }
    if (typeof passwordCpu !== "undefined") {
      payload["passwordCpu"] = passwordCpu;
    }
    if (typeof passwordMemory !== "undefined") {
      payload["passwordMemory"] = passwordMemory;
    }
    if (typeof passwordParallel !== "undefined") {
      payload["passwordParallel"] = passwordParallel;
    }
    if (typeof passwordLength !== "undefined") {
      payload["passwordLength"] = passwordLength;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with Scrypt modified password
   *
   * Create a new user. Password provided must be hashed with the [Scrypt Modified](https://gist.github.com/Meldiron/eecf84a0225eccb5a378d45bb27462cc) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {string} passwordSalt
   * @param {string} passwordSaltSeparator
   * @param {string} passwordSignerKey
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createScryptModifiedUser(userId, email, password, passwordSalt, passwordSaltSeparator, passwordSignerKey, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    if (typeof passwordSalt === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordSalt"');
    }
    if (typeof passwordSaltSeparator === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordSaltSeparator"');
    }
    if (typeof passwordSignerKey === "undefined") {
      throw new AppwriteException('Missing required parameter: "passwordSignerKey"');
    }
    const apiPath = "/users/scrypt-modified";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof passwordSalt !== "undefined") {
      payload["passwordSalt"] = passwordSalt;
    }
    if (typeof passwordSaltSeparator !== "undefined") {
      payload["passwordSaltSeparator"] = passwordSaltSeparator;
    }
    if (typeof passwordSignerKey !== "undefined") {
      payload["passwordSignerKey"] = passwordSignerKey;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create user with SHA password
   *
   * Create a new user. Password provided must be hashed with the [SHA](https://en.wikipedia.org/wiki/Secure_Hash_Algorithm) algorithm. Use the [POST /users](https://appwrite.io/docs/server/users#usersCreate) endpoint to create users with a plain text password.
   *
   * @param {string} userId
   * @param {string} email
   * @param {string} password
   * @param {PasswordHash} passwordVersion
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async createSHAUser(userId, email, password, passwordVersion, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/sha";
    const payload = {};
    if (typeof userId !== "undefined") {
      payload["userId"] = userId;
    }
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    if (typeof passwordVersion !== "undefined") {
      payload["passwordVersion"] = passwordVersion;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get user
   *
   * Get a user by its unique ID.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async get(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete user
   *
   * Delete a user by its unique ID, thereby releasing it&#039;s ID. Since ID is released and can be reused, all user-related resources like documents or storage files should be deleted before user deletion. If you want to keep ID reserved, use the [updateStatus](https://appwrite.io/docs/server/users#usersUpdateStatus) endpoint instead.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async delete(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update email
   *
   * Update the user email by its unique ID.
   *
   * @param {string} userId
   * @param {string} email
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updateEmail(userId, email) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof email === "undefined") {
      throw new AppwriteException('Missing required parameter: "email"');
    }
    const apiPath = "/users/{userId}/email".replace("{userId}", userId);
    const payload = {};
    if (typeof email !== "undefined") {
      payload["email"] = email;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update user labels
       *
       * Update the user labels by its unique ID. 
  
  Labels can be used to grant access to resources. While teams are a way for user&#039;s to share access to a resource, labels can be defined by the developer to grant access without an invitation. See the [Permissions docs](https://appwrite.io/docs/permissions) for more info.
       *
       * @param {string} userId
       * @param {string[]} labels
       * @throws {AppwriteException}
       * @returns {Promise<Models.User<Preferences>>}
       */
  async updateLabels(userId, labels) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof labels === "undefined") {
      throw new AppwriteException('Missing required parameter: "labels"');
    }
    const apiPath = "/users/{userId}/labels".replace("{userId}", userId);
    const payload = {};
    if (typeof labels !== "undefined") {
      payload["labels"] = labels;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List user logs
   *
   * Get the user activity logs list by its unique ID.
   *
   * @param {string} userId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.LogList>}
   */
  async listLogs(userId, queries) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/logs".replace("{userId}", userId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List user memberships
   *
   * Get the user membership list by its unique ID.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.MembershipList>}
   */
  async listMemberships(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/memberships".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update MFA
   *
   * Enable or disable MFA on a user account.
   *
   * @param {string} userId
   * @param {boolean} mfa
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updateMfa(userId, mfa) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof mfa === "undefined") {
      throw new AppwriteException('Missing required parameter: "mfa"');
    }
    const apiPath = "/users/{userId}/mfa".replace("{userId}", userId);
    const payload = {};
    if (typeof mfa !== "undefined") {
      payload["mfa"] = mfa;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete Authenticator
   *
   * Delete an authenticator app.
   *
   * @param {string} userId
   * @param {AuthenticatorType} type
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async deleteMfaAuthenticator(userId, type) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof type === "undefined") {
      throw new AppwriteException('Missing required parameter: "type"');
    }
    const apiPath = "/users/{userId}/mfa/authenticators/{type}".replace("{userId}", userId).replace("{type}", type);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List Factors
   *
   * List the factors available on the account to be used as a MFA challange.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.MfaFactors>}
   */
  async listMfaFactors(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/mfa/factors".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get MFA Recovery Codes
   *
   * Get recovery codes that can be used as backup for MFA flow by User ID. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.MfaRecoveryCodes>}
   */
  async getMfaRecoveryCodes(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/mfa/recovery-codes".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Regenerate MFA Recovery Codes
   *
   * Regenerate recovery codes that can be used as backup for MFA flow by User ID. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.MfaRecoveryCodes>}
   */
  async updateMfaRecoveryCodes(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/mfa/recovery-codes".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create MFA Recovery Codes
   *
   * Generate recovery codes used as backup for MFA flow for User ID. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method by client SDK.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.MfaRecoveryCodes>}
   */
  async createMfaRecoveryCodes(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/mfa/recovery-codes".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update name
   *
   * Update the user name by its unique ID.
   *
   * @param {string} userId
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updateName(userId, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof name === "undefined") {
      throw new AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/users/{userId}/name".replace("{userId}", userId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update password
   *
   * Update the user password by its unique ID.
   *
   * @param {string} userId
   * @param {string} password
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updatePassword(userId, password) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof password === "undefined") {
      throw new AppwriteException('Missing required parameter: "password"');
    }
    const apiPath = "/users/{userId}/password".replace("{userId}", userId);
    const payload = {};
    if (typeof password !== "undefined") {
      payload["password"] = password;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update phone
   *
   * Update the user phone by its unique ID.
   *
   * @param {string} userId
   * @param {string} number
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updatePhone(userId, number) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof number === "undefined") {
      throw new AppwriteException('Missing required parameter: "number"');
    }
    const apiPath = "/users/{userId}/phone".replace("{userId}", userId);
    const payload = {};
    if (typeof number !== "undefined") {
      payload["number"] = number;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get user preferences
   *
   * Get the user preferences by its unique ID.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Preferences>}
   */
  async getPrefs(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/prefs".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update user preferences
   *
   * Update the user preferences by its unique ID. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.
   *
   * @param {string} userId
   * @param {object} prefs
   * @throws {AppwriteException}
   * @returns {Promise<Preferences>}
   */
  async updatePrefs(userId, prefs) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof prefs === "undefined") {
      throw new AppwriteException('Missing required parameter: "prefs"');
    }
    const apiPath = "/users/{userId}/prefs".replace("{userId}", userId);
    const payload = {};
    if (typeof prefs !== "undefined") {
      payload["prefs"] = prefs;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List user sessions
   *
   * Get the user sessions list by its unique ID.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<Models.SessionList>}
   */
  async listSessions(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/sessions".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create session
       *
       * Creates a session for a user. Returns an immediately usable session object.
  
  If you want to generate a token for a custom authentication flow, use the [POST /users/{userId}/tokens](https://appwrite.io/docs/server/users#createToken) endpoint.
       *
       * @param {string} userId
       * @throws {AppwriteException}
       * @returns {Promise<Models.Session>}
       */
  async createSession(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/sessions".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete user sessions
   *
   * Delete all user&#039;s sessions by using the user&#039;s unique ID.
   *
   * @param {string} userId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteSessions(userId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/sessions".replace("{userId}", userId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete user session
   *
   * Delete a user sessions by its unique ID.
   *
   * @param {string} userId
   * @param {string} sessionId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteSession(userId, sessionId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof sessionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "sessionId"');
    }
    const apiPath = "/users/{userId}/sessions/{sessionId}".replace("{userId}", userId).replace("{sessionId}", sessionId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update user status
   *
   * Update the user status by its unique ID. Use this endpoint as an alternative to deleting a user if you want to keep user&#039;s ID reserved.
   *
   * @param {string} userId
   * @param {boolean} status
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updateStatus(userId, status) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof status === "undefined") {
      throw new AppwriteException('Missing required parameter: "status"');
    }
    const apiPath = "/users/{userId}/status".replace("{userId}", userId);
    const payload = {};
    if (typeof status !== "undefined") {
      payload["status"] = status;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List User Targets
   *
   * List the messaging targets that are associated with a user.
   *
   * @param {string} userId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.TargetList>}
   */
  async listTargets(userId, queries) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/targets".replace("{userId}", userId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create User Target
   *
   * Create a messaging target.
   *
   * @param {string} userId
   * @param {string} targetId
   * @param {MessagingProviderType} providerType
   * @param {string} identifier
   * @param {string} providerId
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.Target>}
   */
  async createTarget(userId, targetId, providerType, identifier, providerId, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof targetId === "undefined") {
      throw new AppwriteException('Missing required parameter: "targetId"');
    }
    if (typeof providerType === "undefined") {
      throw new AppwriteException('Missing required parameter: "providerType"');
    }
    if (typeof identifier === "undefined") {
      throw new AppwriteException('Missing required parameter: "identifier"');
    }
    const apiPath = "/users/{userId}/targets".replace("{userId}", userId);
    const payload = {};
    if (typeof targetId !== "undefined") {
      payload["targetId"] = targetId;
    }
    if (typeof providerType !== "undefined") {
      payload["providerType"] = providerType;
    }
    if (typeof identifier !== "undefined") {
      payload["identifier"] = identifier;
    }
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get User Target
   *
   * Get a user&#039;s push notification target by ID.
   *
   * @param {string} userId
   * @param {string} targetId
   * @throws {AppwriteException}
   * @returns {Promise<Models.Target>}
   */
  async getTarget(userId, targetId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof targetId === "undefined") {
      throw new AppwriteException('Missing required parameter: "targetId"');
    }
    const apiPath = "/users/{userId}/targets/{targetId}".replace("{userId}", userId).replace("{targetId}", targetId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update User target
   *
   * Update a messaging target.
   *
   * @param {string} userId
   * @param {string} targetId
   * @param {string} identifier
   * @param {string} providerId
   * @param {string} name
   * @throws {AppwriteException}
   * @returns {Promise<Models.Target>}
   */
  async updateTarget(userId, targetId, identifier, providerId, name) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof targetId === "undefined") {
      throw new AppwriteException('Missing required parameter: "targetId"');
    }
    const apiPath = "/users/{userId}/targets/{targetId}".replace("{userId}", userId).replace("{targetId}", targetId);
    const payload = {};
    if (typeof identifier !== "undefined") {
      payload["identifier"] = identifier;
    }
    if (typeof providerId !== "undefined") {
      payload["providerId"] = providerId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete user target
   *
   * Delete a messaging target.
   *
   * @param {string} userId
   * @param {string} targetId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteTarget(userId, targetId) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof targetId === "undefined") {
      throw new AppwriteException('Missing required parameter: "targetId"');
    }
    const apiPath = "/users/{userId}/targets/{targetId}".replace("{userId}", userId).replace("{targetId}", targetId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create token
       *
       * Returns a token with a secret key for creating a session. Use the user ID and secret and submit a request to the [PUT /account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process.
  
       *
       * @param {string} userId
       * @param {number} length
       * @param {number} expire
       * @throws {AppwriteException}
       * @returns {Promise<Models.Token>}
       */
  async createToken(userId, length, expire) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    const apiPath = "/users/{userId}/tokens".replace("{userId}", userId);
    const payload = {};
    if (typeof length !== "undefined") {
      payload["length"] = length;
    }
    if (typeof expire !== "undefined") {
      payload["expire"] = expire;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update email verification
   *
   * Update the user email verification status by its unique ID.
   *
   * @param {string} userId
   * @param {boolean} emailVerification
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updateEmailVerification(userId, emailVerification) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof emailVerification === "undefined") {
      throw new AppwriteException('Missing required parameter: "emailVerification"');
    }
    const apiPath = "/users/{userId}/verification".replace("{userId}", userId);
    const payload = {};
    if (typeof emailVerification !== "undefined") {
      payload["emailVerification"] = emailVerification;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update phone verification
   *
   * Update the user phone verification status by its unique ID.
   *
   * @param {string} userId
   * @param {boolean} phoneVerification
   * @throws {AppwriteException}
   * @returns {Promise<Models.User<Preferences>>}
   */
  async updatePhoneVerification(userId, phoneVerification) {
    if (typeof userId === "undefined") {
      throw new AppwriteException('Missing required parameter: "userId"');
    }
    if (typeof phoneVerification === "undefined") {
      throw new AppwriteException('Missing required parameter: "phoneVerification"');
    }
    const apiPath = "/users/{userId}/verification/phone".replace("{userId}", userId);
    const payload = {};
    if (typeof phoneVerification !== "undefined") {
      payload["phoneVerification"] = phoneVerification;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
};

export { Users };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=users.mjs.map