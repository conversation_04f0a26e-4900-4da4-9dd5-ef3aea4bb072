// src/enums/messaging-provider-type.ts
var MessagingProviderType = /* @__PURE__ */ ((MessagingProviderType2) => {
  MessagingProviderType2["Email"] = "email";
  MessagingProviderType2["Sms"] = "sms";
  MessagingProviderType2["Push"] = "push";
  return MessagingProviderType2;
})(MessagingProviderType || {});

export { MessagingProviderType };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=messaging-provider-type.mjs.map