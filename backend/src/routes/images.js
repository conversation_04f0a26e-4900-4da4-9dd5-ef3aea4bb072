import express from 'express';
import imageController from '../controllers/imageController.js';
import { authenticateUser, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get image embed data by UUID (public endpoint - no auth required)
router.get('/embed/:uuid', imageController.getImageEmbed);

// Serve image directly by UUID (public endpoint - no auth required)
router.get('/i/:uuid', imageController.serveImage);

// Protected routes (require authentication)
router.use(authenticateUser);

// Upload image
router.post('/upload', imageController.uploadImage);

// ShareX upload (special endpoint)
router.post('/sharex', imageController.shareXUpload);

// Get user's images
router.get('/my-images', imageController.getUserImages);

// Get image by ID
router.get('/:imageId', imageController.getImageById);

// Delete image
router.delete('/:imageId', imageController.deleteImage);

// Update image settings
router.put('/:imageId', imageController.updateImageSettings);

export default router;
