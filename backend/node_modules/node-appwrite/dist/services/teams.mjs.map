{"version": 3, "sources": ["../../src/services/teams.ts"], "names": [], "mappings": ";AAAA,SAAS,yBAA+D;AAGjE,IAAM,QAAN,MAAY;AAAA,EAGf,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,KAA6C,SAAoB,QAAwD;AAC3H,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,OAA+C,QAAgB,MAAc,OAAqD;AACpI,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,IAA4C,QAAmD;AACjG,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAmD,QAAgB,MAAiD;AACtH,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,QAA6B;AACtC,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,gBAAgB,QAAgB,SAAoB,QAAiD;AACvG,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM,iBAAiB,QAAgB,OAAiB,OAAgB,QAAiB,OAAgB,KAAc,MAA2C;AAC9J,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,QAAgB,cAAkD;AAClF,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,iBAAiB,QAAgB,cAAsB,OAA6C;AACtG,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,QAAgB,cAAmC;AACtE,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,uBAAuB,QAAgB,cAAsB,QAAgB,QAA4C;AAC3H,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,oDAAoD,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AACtI,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,SAAiD,QAAsC;AACzF,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAoD,QAAgB,OAAqC;AAC3G,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Teams {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List teams\n     *\n     * Get a list of all the teams in which the current user is a member. You can use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TeamList<Preferences>>}\n     */\n    async list<Preferences extends Models.Preferences>(queries?: string[], search?: string): Promise<Models.TeamList<Preferences>> {\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create team\n     *\n     * Create a new team. The user who creates the team will automatically be assigned as the owner of the team. Only the users with the owner role can invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async create<Preferences extends Models.Preferences>(teamId: string, name: string, roles?: string[]): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams';\n        const payload: Payload = {};\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get team\n     *\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async get<Preferences extends Models.Preferences>(teamId: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update name\n     *\n     * Update the team&#039;s name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Team<Preferences>>}\n     */\n    async updateName<Preferences extends Models.Preferences>(teamId: string, name: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete team\n     *\n     * Delete a team using its ID. Only team members with the owner role can delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async delete(teamId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List team memberships\n     *\n     * Use this endpoint to list a team&#039;s members using the team&#039;s ID. All team members have read access to this endpoint.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MembershipList>}\n     */\n    async listMemberships(teamId: string, queries?: string[], search?: string): Promise<Models.MembershipList> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create team membership\n     *\n     * Invite a new member to join your team. Provide an ID for existing users, or invite unregistered users using an email or phone number. If initiated from a Client SDK, Appwrite will send an email or sms with a link to join the team to the invited user, and an account will be created for them if one doesn&#039;t exist. If initiated from a Server SDK, the new member will be added automatically to the team.\n\nYou only need to provide one of a user ID, email, or phone number. Appwrite will prioritize accepting the user ID &gt; email &gt; phone number if you provide more than one of these parameters.\n\nUse the `url` parameter to redirect the user from the invitation email to your app. After the user is redirected, use the [Update Team Membership Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus) endpoint to allow the user to accept the invitation to the team. \n\nPlease note that to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) Appwrite will accept the only redirect URLs under the domains you have added as a platform on the Appwrite Console.\n\n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async createMembership(teamId: string, roles: string[], email?: string, userId?: string, phone?: string, url?: string, name?: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get team membership\n     *\n     * Get a team member by the membership unique id. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async getMembership(teamId: string, membershipId: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update membership\n     *\n     * Modify the roles of a team member. Only team members with the owner role have access to this endpoint. Learn more about [roles and permissions](https://appwrite.io/docs/permissions).\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async updateMembership(teamId: string, membershipId: string, roles: string[]): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete team membership\n     *\n     * This endpoint allows a user to leave a team or for a team owner to delete the membership of any other team member. You can also use this endpoint to delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteMembership(teamId: string, membershipId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update team membership status\n     *\n     * Use this endpoint to allow a user to accept an invitation to join a team after being redirected back to your app from the invitation email received by the user.\n\nIf the request is successful, a session for the user is automatically created.\n\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Membership>}\n     */\n    async updateMembershipStatus(teamId: string, membershipId: string, userId: string, secret: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get team preferences\n     *\n     * Get the team&#039;s shared preferences by its unique ID. If a preference doesn&#039;t need to be shared by all team members, prefer storing them in [user preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async getPrefs<Preferences extends Models.Preferences>(teamId: string): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update preferences\n     *\n     * Update the team&#039;s preferences by its unique ID. The object you pass is stored as is and replaces any previous value. The maximum allowed prefs size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async updatePrefs<Preferences extends Models.Preferences>(teamId: string, prefs: object): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}