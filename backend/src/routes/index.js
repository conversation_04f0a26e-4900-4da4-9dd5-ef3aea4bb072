import express from 'express';
import authRoutes from './auth.js';
import imageRoutes from './images.js';
import userRoutes from './user.js';
import adminRoutes from './admin.js';
import sharexRoutes from './sharex.js';

const router = express.Router();

// Mount route modules
router.use('/auth', authRoutes);
router.use('/images', imageRoutes);
router.use('/user', userRoutes);
router.use('/admin', adminRoutes);
router.use('/sharex', sharexRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'AveImage Cloud API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// API info endpoint
router.get('/info', (req, res) => {
  res.json({
    success: true,
    api: {
      name: 'AveImage Cloud API',
      version: '1.0.0',
      description: 'Modern image hosting service with custom embeds',
      endpoints: {
        auth: '/api/auth',
        images: '/api/images',
        user: '/api/user',
        admin: '/api/admin'
      },
      features: [
        'Image Upload & Hosting',
        'Custom Embed Generation',
        'ShareX Integration',
        'User Management',
        'Admin Dashboard',
        'Credits System',
        'Storage Management'
      ]
    }
  });
});

export default router;
