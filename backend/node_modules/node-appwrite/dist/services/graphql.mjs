import { AppwriteException } from '../client.mjs';

// src/services/graphql.ts
var Graphql = class {
  constructor(client) {
    this.client = client;
  }
  /**
   * GraphQL endpoint
   *
   * Execute a GraphQL mutation.
   *
   * @param {object} query
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async query(query) {
    if (typeof query === "undefined") {
      throw new AppwriteException('Missing required parameter: "query"');
    }
    const apiPath = "/graphql";
    const payload = {};
    if (typeof query !== "undefined") {
      payload["query"] = query;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "x-sdk-graphql": "true",
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * GraphQL endpoint
   *
   * Execute a GraphQL mutation.
   *
   * @param {object} query
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async mutation(query) {
    if (typeof query === "undefined") {
      throw new AppwriteException('Missing required parameter: "query"');
    }
    const apiPath = "/graphql/mutation";
    const payload = {};
    if (typeof query !== "undefined") {
      payload["query"] = query;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "x-sdk-graphql": "true",
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
};

export { Graphql };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=graphql.mjs.map