export { Models } from './models.js';
export { Query, QueryTypes, QueryTypesList } from './query.js';

type Payload = {
    [key: string]: any;
};
type UploadProgress = {
    $id: string;
    progress: number;
    sizeUploaded: number;
    chunksTotal: number;
    chunksUploaded: number;
};
type Headers = {
    [key: string]: string;
};
declare class AppwriteException extends Error {
    code: number;
    response: string;
    type: string;
    constructor(message: string, code?: number, type?: string, response?: string);
}
declare class Client {
    static CHUNK_SIZE: number;
    config: {
        endpoint: string;
        selfSigned: boolean;
        project: string;
        key: string;
        jwt: string;
        locale: string;
        session: string;
        forwardeduseragent: string;
    };
    headers: Headers;
    /**
     * Set Endpoint
     *
     * Your project endpoint
     *
     * @param {string} endpoint
     *
     * @returns {this}
     */
    setEndpoint(endpoint: string): this;
    /**
     * Set self-signed
     *
     * @param {boolean} selfSigned
     *
     * @returns {this}
     */
    setSelfSigned(selfSigned: boolean): this;
    /**
     * Add header
     *
     * @param {string} header
     * @param {string} value
     *
     * @returns {this}
     */
    addHeader(header: string, value: string): this;
    /**
     * Set Project
     *
     * Your project ID
     *
     * @param value string
     *
     * @return {this}
     */
    setProject(value: string): this;
    /**
     * Set Key
     *
     * Your secret API key
     *
     * @param value string
     *
     * @return {this}
     */
    setKey(value: string): this;
    /**
     * Set JWT
     *
     * Your secret JSON Web Token
     *
     * @param value string
     *
     * @return {this}
     */
    setJWT(value: string): this;
    /**
     * Set Locale
     *
     * @param value string
     *
     * @return {this}
     */
    setLocale(value: string): this;
    /**
     * Set Session
     *
     * The user session to authenticate with
     *
     * @param value string
     *
     * @return {this}
     */
    setSession(value: string): this;
    /**
     * Set ForwardedUserAgent
     *
     * The user agent string of the client that made the request
     *
     * @param value string
     *
     * @return {this}
     */
    setForwardedUserAgent(value: string): this;
    prepareRequest(method: string, url: URL, headers?: Headers, params?: Payload): {
        uri: string;
        options: RequestInit;
    };
    chunkedUpload(method: string, url: URL, headers: Headers | undefined, originalPayload: Payload | undefined, onProgress: (progress: UploadProgress) => void): Promise<any>;
    redirect(method: string, url: URL, headers?: Headers, params?: Payload): Promise<string>;
    call(method: string, url: URL, headers?: Headers, params?: Payload, responseType?: string): Promise<any>;
    static flatten(data: Payload, prefix?: string): Payload;
}

export { AppwriteException, Client, Payload, UploadProgress };
