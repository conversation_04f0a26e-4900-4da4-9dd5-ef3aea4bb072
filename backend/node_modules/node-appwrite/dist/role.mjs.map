{"version": 3, "sources": ["../src/role.ts"], "names": [], "mappings": ";AAGO,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASd,OAAc,MAAc;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAc,KAAK,IAAY,SAAiB,IAAY;AACxD,QAAI,WAAW,IAAI;AACf,aAAO,QAAQ,EAAE;AAAA,IACrB;AACA,WAAO,QAAQ,EAAE,IAAI,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAc,MAAM,SAAiB,IAAY;AAC7C,QAAI,WAAW,IAAI;AACf,aAAO;AAAA,IACX;AACA,WAAO,SAAS,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAc,SAAiB;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAc,KAAK,IAAY,OAAe,IAAY;AACtD,QAAI,SAAS,IAAI;AACb,aAAO,QAAQ,EAAE;AAAA,IACrB;AACA,WAAO,QAAQ,EAAE,IAAI,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAc,OAAO,IAAoB;AACrC,WAAO,UAAU,EAAE;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAc,MAAM,MAAsB;AACtC,WAAO,SAAS,IAAI;AAAA,EACxB;AACJ", "sourcesContent": ["/**\n * Helper class to generate role strings for `Permission`.\n */\nexport class Role {\n\n    /**\n     * Grants access to anyone.\n     * \n     * This includes authenticated and unauthenticated users.\n     * \n     * @returns {string}\n     */\n    public static any(): string {\n        return 'any'\n    }\n\n    /**\n     * Grants access to a specific user by user ID.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id \n     * @param {string} status \n     * @returns {string}\n     */\n    public static user(id: string, status: string = ''): string {\n        if (status === '') {\n            return `user:${id}`\n        }\n        return `user:${id}/${status}`\n    }\n\n    /**\n     * Grants access to any authenticated or anonymous user.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     * \n     * @param {string} status \n     * @returns {string}\n     */\n    public static users(status: string = ''): string {\n        if (status === '') {\n            return 'users'\n        }\n        return `users/${status}`\n    }\n\n    /**\n     * Grants access to any guest user without a session.\n     * \n     * Authenticated users don't have access to this role.\n     * \n     * @returns {string}\n     */\n    public static guests(): string {\n        return 'guests'\n    }\n\n    /**\n     * Grants access to a team by team ID.\n     * \n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     * \n     * @param {string} id \n     * @param {string} role \n     * @returns {string}\n     */\n    public static team(id: string, role: string = ''): string {\n        if (role === '') {\n            return `team:${id}`\n        }\n        return `team:${id}/${role}`\n    }\n\n    /**\n     * Grants access to a specific member of a team.\n     * \n     * When the member is removed from the team, they will\n     * no longer have access.\n     * \n     * @param {string} id \n     * @returns {string}\n     */\n    public static member(id: string): string {\n        return `member:${id}`\n    }\n\n    /**\n     * Grants access to a user with the specified label.\n     * \n     * @param {string} name \n     * @returns  {string}\n     */\n    public static label(name: string): string {\n        return `label:${name}`\n    }\n}"]}