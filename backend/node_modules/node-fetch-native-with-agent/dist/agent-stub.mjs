var o=Object.defineProperty;var e=(t,c)=>o(t,"name",{value:c,configurable:!0});var i=Object.defineProperty,r=e((t,c)=>i(t,"name",{value:c,configurable:!0}),"e");function a(){return{agent:void 0,dispatcher:void 0}}e(a,"createAgent"),r(a,"createAgent");function n(){return globalThis.fetch}e(n,"createFetch"),r(n,"createFetch");const h=globalThis.fetch;export{a as createAgent,n as createFetch,h as fetch};
