import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import Login from './views/auth/Login.vue'
import Register from './views/auth/Register.vue'
import ForgotPassword from './views/auth/ForgotPassword.vue'
import ResetPassword from './views/auth/ResetPassword.vue'
import OAuthCallback from './views/auth/OAuthCallback.vue'

import NotFound from './views/errors/NotFound.vue'
import Forbidden from './views/errors/Forbidden.vue'
import ServerError from './views/errors/ServerError.vue'

import DashboardHome from './views/Home.vue'
import DashboardAccount from './views/Account.vue'
import Images from './views/Images.vue'
import Upload from './views/Upload.vue'

const routes = [
    // Auth routes
    {
        path: '/auth/login',
        name: 'Login',
        component: Login,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/register',
        name: 'Register',
        component: Register,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: { requiresGuest: true }
    },
    {
        path: '/auth/callback',
        name: 'OAuthCallback',
        component: OAuthCallback
    },
    {
        path: '/auth/callback/success',
        name: 'OAuthCallbackSuccess',
        component: OAuthCallback
    },
    {
        path: '/auth/callback/failure',
        name: 'OAuthCallbackFailure',
        component: OAuthCallback
    },
    
    // Dashboard routes
    {
        path: '/',
        redirect: '/dashboard'
    },
    {
        path: '/images',
        redirect: '/dashboard/images'
    },
    {
        path: '/dashboard',
        name: 'Dashboard',
        component: DashboardHome,
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/images',
        name: 'Images',
        component: Images,
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/account',
        name: 'Account',
        component: DashboardAccount,
        meta: { requiresAuth: true }
    },
    {
        path: '/admin',
        name: 'Admin',
        component: () => import('./views/Admin.vue'),
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
        path: '/store',
        name: 'Store',
        component: () => import('./views/Store.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/dashboard/upload',
        name: 'Upload',
        component: Upload,
        meta: { requiresAuth: true }
    },
    {
        path: '/sharex',
        name: 'ShareX',
        component: () => import('./views/ShareX.vue'),
        meta: { requiresAuth: true }
    },
    {
        path: '/i/:uuid',
        name: 'ImageEmbed',
        component: () => import('./views/ImageEmbed.vue'),
        meta: { requiresAuth: false }
    },
    
    // Legacy route redirects
    {
        path: '/account',
        redirect: '/dashboard/account'
    },
    
    // Error routes
    {
        path: '/errors/403',
        name: 'Forbidden',
        component: Forbidden
    },
    {
        path: '/errors/500',
        name: 'ServerError',
        component: ServerError
    },
    
    // 404 catch-all
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: NotFound
    }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()

    // Initialize auth state only once on app start
    if (!authStore._initialized) {
        await authStore.initAuth()
        authStore._initialized = true
    }

    // Wait for auth initialization to complete
    let attempts = 0
    while (authStore.isLoading && attempts < 50) { // Max 5 seconds
        await new Promise(resolve => setTimeout(resolve, 100))
        attempts++
    }

    const isAuthenticated = authStore.isAuthenticated && authStore.user && authStore.session

    // Check if route requires authentication
    if (to.meta.requiresAuth && !isAuthenticated) {
        console.log('Route requires auth but user not authenticated, redirecting to login')
        next({ name: 'Login', query: { redirect: to.fullPath } })
        return
    }

    // Check if route requires admin privileges
    if (to.meta.requiresAdmin && isAuthenticated) {
        try {
            // Import user store to check admin status
            const { useUserStore } = await import('./stores/user.js')
            const userStore = useUserStore()

            // Fetch user profile if not already loaded
            if (!userStore.profile) {
                console.log('Fetching user profile for admin check...')
                await userStore.fetchProfile()
            }

            console.log('Admin check - isAdmin:', userStore.isAdmin, 'profile:', userStore.profile)

            if (!userStore.isAdmin) {
                console.log('User is not admin, redirecting to forbidden')
                next({ name: 'Forbidden' })
                return
            }
        } catch (error) {
            console.error('Failed to fetch user profile for admin check:', error)
            // If we can't verify admin status, redirect to login
            next({ name: 'Login', query: { redirect: to.fullPath } })
            return
        }
    }

    // Check if route is for guests only (redirect authenticated users)
    if (to.meta.requiresGuest && isAuthenticated) {
        next({ name: 'Dashboard' })
        return
    }

    console.log('Navigation allowed to:', to.path)
    next()
})

export default router