{"version": 3, "sources": ["../../appwrite/node_modules/tslib/tslib.es6.js", "../../appwrite/src/service.ts", "../../appwrite/src/query.ts", "../../appwrite/src/client.ts", "../../appwrite/src/services/account.ts", "../../appwrite/src/services/avatars.ts", "../../appwrite/src/services/databases.ts", "../../appwrite/src/services/functions.ts", "../../appwrite/src/services/graphql.ts", "../../appwrite/src/services/locale.ts", "../../appwrite/src/services/messaging.ts", "../../appwrite/src/services/storage.ts", "../../appwrite/src/services/teams.ts", "../../appwrite/src/permission.ts", "../../appwrite/src/role.ts", "../../appwrite/src/id.ts", "../../appwrite/src/enums/authenticator-type.ts", "../../appwrite/src/enums/authentication-factor.ts", "../../appwrite/src/enums/o-auth-provider.ts", "../../appwrite/src/enums/browser.ts", "../../appwrite/src/enums/credit-card.ts", "../../appwrite/src/enums/flag.ts", "../../appwrite/src/enums/execution-method.ts", "../../appwrite/src/enums/image-gravity.ts", "../../appwrite/src/enums/image-format.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n", "import { Client } from './client';\nimport type { Payload } from './client';\n\nexport class Service {\n    static CHUNK_SIZE = 5*1024*1024; // 5MB\n\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Service.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}", "type QueryTypesSingle = string | number | boolean;\nexport type QueryTypesList = string[] | number[] | boolean[] | Query[];\nexport type QueryTypes = QueryTypesSingle | QueryTypesList;\ntype AttributesTypes = string | string[];\n\nexport class Query {\n  method: string;\n  attribute: AttributesTypes | undefined;\n  values: QueryTypesList | undefined;\n\n  constructor(\n    method: string,\n    attribute?: AttributesTypes,\n    values?: QueryTypes\n  ) {\n    this.method = method;\n    this.attribute = attribute;\n\n    if (values !== undefined) {\n      if (Array.isArray(values)) {\n        this.values = values;\n      } else {\n        this.values = [values] as QueryTypesList;\n      }\n    }\n  }\n\n  toString(): string {\n    return JSON.stringify({\n      method: this.method,\n      attribute: this.attribute,\n      values: this.values,\n    });\n  }\n\n  static equal = (attribute: string, value: QueryTypes): string =>\n    new Query(\"equal\", attribute, value).toString();\n\n  static notEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"notEqual\", attribute, value).toString();\n\n  static lessThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThan\", attribute, value).toString();\n\n  static lessThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThanEqual\", attribute, value).toString();\n\n  static greaterThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThan\", attribute, value).toString();\n\n  static greaterThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThanEqual\", attribute, value).toString();\n\n  static isNull = (attribute: string): string =>\n    new Query(\"isNull\", attribute).toString();\n\n  static isNotNull = (attribute: string): string =>\n    new Query(\"isNotNull\", attribute).toString();\n\n  static between = (attribute: string, start: string | number, end: string | number) =>\n    new Query(\"between\", attribute, [start, end] as QueryTypesList).toString();\n\n  static startsWith = (attribute: string, value: string): string =>\n    new Query(\"startsWith\", attribute, value).toString();\n\n  static endsWith = (attribute: string, value: string): string =>\n    new Query(\"endsWith\", attribute, value).toString();\n\n  static select = (attributes: string[]): string =>\n    new Query(\"select\", undefined, attributes).toString();\n\n  static search = (attribute: string, value: string): string =>\n    new Query(\"search\", attribute, value).toString();\n\n  static orderDesc = (attribute: string): string =>\n    new Query(\"orderDesc\", attribute).toString();\n\n  static orderAsc = (attribute: string): string =>\n    new Query(\"orderAsc\", attribute).toString();\n\n  static cursorAfter = (documentId: string): string =>\n    new Query(\"cursorAfter\", undefined, documentId).toString();\n\n  static cursorBefore = (documentId: string): string =>\n    new Query(\"cursorBefore\", undefined, documentId).toString();\n\n  static limit = (limit: number): string =>\n    new Query(\"limit\", undefined, limit).toString();\n\n  static offset = (offset: number): string =>\n    new Query(\"offset\", undefined, offset).toString();\n\n  static contains = (attribute: string, value: string | string[]): string =>\n    new Query(\"contains\", attribute, value).toString();\n\n  static or = (queries: string[]) =>\n    new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n  static and = (queries: string[]) =>\n    new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n}\n", "import { Models } from './models';\nimport { Service } from './service';\n\ntype Payload = {\n    [key: string]: any;\n}\n\ntype Headers = {\n    [key: string]: string;\n}\n\ntype RealtimeResponse = {\n    type: 'error' | 'event' | 'connected' | 'response';\n    data: RealtimeResponseAuthenticated | RealtimeResponseConnected | RealtimeResponseError | RealtimeResponseEvent<unknown>;\n}\n\ntype RealtimeRequest = {\n    type: 'authentication';\n    data: RealtimeRequestAuthenticate;\n}\n\nexport type RealtimeResponseEvent<T extends unknown> = {\n    events: string[];\n    channels: string[];\n    timestamp: number;\n    payload: T;\n}\n\ntype RealtimeResponseError = {\n    code: number;\n    message: string;\n}\n\ntype RealtimeResponseConnected = {\n    channels: string[];\n    user?: object;\n}\n\ntype RealtimeResponseAuthenticated = {\n    to: string;\n    success: boolean;\n    user: object;\n}\n\ntype RealtimeRequestAuthenticate = {\n    session: string;\n}\n\ntype Realtime = {\n    socket?: WebSocket;\n    timeout?: number;\n    url?: string;\n    lastMessage?: RealtimeResponse;\n    channels: Set<string>;\n    subscriptions: Map<number, {\n        channels: string[];\n        callback: (payload: RealtimeResponseEvent<any>) => void\n    }>;\n    subscriptionsCounter: number;\n    reconnect: boolean;\n    reconnectAttempts: number;\n    getTimeout: () => number;\n    connect: () => void;\n    createSocket: () => void;\n    cleanUp: (channels: string[]) => void;\n    onMessage: (event: MessageEvent) => void;\n}\n\nexport type UploadProgress = {\n    $id: string;\n    progress: number;\n    sizeUploaded: number;\n    chunksTotal: number;\n    chunksUploaded: number;\n}\n\nclass AppwriteException extends Error {\n    code: number;\n    response: string;\n    type: string;\n    constructor(message: string, code: number = 0, type: string = '', response: string = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n\nclass Client {\n    config = {\n        endpoint: 'https://cloud.appwrite.io/v1',\n        endpointRealtime: '',\n        project: '',\n        jwt: '',\n        locale: '',\n        session: '',\n    };\n    headers: Headers = {\n        'x-sdk-name': 'Web',\n        'x-sdk-platform': 'client',\n        'x-sdk-language': 'web',\n        'x-sdk-version': '15.0.0',\n        'X-Appwrite-Response-Format': '1.5.0',\n    };\n\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint: string): this {\n        this.config.endpoint = endpoint;\n        this.config.endpointRealtime = this.config.endpointRealtime || this.config.endpoint.replace('https://', 'wss://').replace('http://', 'ws://');\n\n        return this;\n    }\n\n    /**\n     * Set Realtime Endpoint\n     *\n     * @param {string} endpointRealtime\n     *\n     * @returns {this}\n     */\n    setEndpointRealtime(endpointRealtime: string): this {\n        this.config.endpointRealtime = endpointRealtime;\n\n        return this;\n    }\n\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value: string): this {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value: string): this {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value: string): this {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value: string): this {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n\n\n    private realtime: Realtime = {\n        socket: undefined,\n        timeout: undefined,\n        url: '',\n        channels: new Set(),\n        subscriptions: new Map(),\n        subscriptionsCounter: 0,\n        reconnect: true,\n        reconnectAttempts: 0,\n        lastMessage: undefined,\n        connect: () => {\n            clearTimeout(this.realtime.timeout);\n            this.realtime.timeout = window?.setTimeout(() => {\n                this.realtime.createSocket();\n            }, 50);\n        },\n        getTimeout: () => {\n            switch (true) {\n                case this.realtime.reconnectAttempts < 5:\n                    return 1000;\n                case this.realtime.reconnectAttempts < 15:\n                    return 5000;\n                case this.realtime.reconnectAttempts < 100:\n                    return 10_000;\n                default:\n                    return 60_000;\n            }\n        },\n        createSocket: () => {\n            if (this.realtime.channels.size < 1) {\n                this.realtime.reconnect = false;\n                this.realtime.socket?.close();\n                return;\n            }\n\n            const channels = new URLSearchParams();\n            channels.set('project', this.config.project);\n            this.realtime.channels.forEach(channel => {\n                channels.append('channels[]', channel);\n            });\n\n            const url = this.config.endpointRealtime + '/realtime?' + channels.toString();\n\n            if (\n                url !== this.realtime.url || // Check if URL is present\n                !this.realtime.socket || // Check if WebSocket has not been created\n                this.realtime.socket?.readyState > WebSocket.OPEN // Check if WebSocket is CLOSING (3) or CLOSED (4)\n            ) {\n                if (\n                    this.realtime.socket &&\n                    this.realtime.socket?.readyState < WebSocket.CLOSING // Close WebSocket if it is CONNECTING (0) or OPEN (1)\n                ) {\n                    this.realtime.reconnect = false;\n                    this.realtime.socket.close();\n                }\n\n                this.realtime.url = url;\n                this.realtime.socket = new WebSocket(url);\n                this.realtime.socket.addEventListener('message', this.realtime.onMessage);\n                this.realtime.socket.addEventListener('open', _event => {\n                    this.realtime.reconnectAttempts = 0;\n                });\n                this.realtime.socket.addEventListener('close', event => {\n                    if (\n                        !this.realtime.reconnect ||\n                        (\n                            this.realtime?.lastMessage?.type === 'error' && // Check if last message was of type error\n                            (<RealtimeResponseError>this.realtime?.lastMessage.data).code === 1008 // Check for policy violation 1008\n                        )\n                    ) {\n                        this.realtime.reconnect = true;\n                        return;\n                    }\n\n                    const timeout = this.realtime.getTimeout();\n                    console.error(`Realtime got disconnected. Reconnect will be attempted in ${timeout / 1000} seconds.`, event.reason);\n\n                    setTimeout(() => {\n                        this.realtime.reconnectAttempts++;\n                        this.realtime.createSocket();\n                    }, timeout);\n                })\n            }\n        },\n        onMessage: (event) => {\n            try {\n                const message: RealtimeResponse = JSON.parse(event.data);\n                this.realtime.lastMessage = message;\n                switch (message.type) {\n                    case 'connected':\n                        const cookie = JSON.parse(window.localStorage.getItem('cookieFallback') ?? '{}');\n                        const session = cookie?.[`a_session_${this.config.project}`];\n                        const messageData = <RealtimeResponseConnected>message.data;\n\n                        if (session && !messageData.user) {\n                            this.realtime.socket?.send(JSON.stringify(<RealtimeRequest>{\n                                type: 'authentication',\n                                data: {\n                                    session\n                                }\n                            }));\n                        }\n                        break;\n                    case 'event':\n                        let data = <RealtimeResponseEvent<unknown>>message.data;\n                        if (data?.channels) {\n                            const isSubscribed = data.channels.some(channel => this.realtime.channels.has(channel));\n                            if (!isSubscribed) return;\n                            this.realtime.subscriptions.forEach(subscription => {\n                                if (data.channels.some(channel => subscription.channels.includes(channel))) {\n                                    setTimeout(() => subscription.callback(data));\n                                }\n                            })\n                        }\n                        break;\n                    case 'error':\n                        throw message.data;\n                    default:\n                        break;\n                }\n            } catch (e) {\n                console.error(e);\n            }\n        },\n        cleanUp: channels => {\n            this.realtime.channels.forEach(channel => {\n                if (channels.includes(channel)) {\n                    let found = Array.from(this.realtime.subscriptions).some(([_key, subscription] )=> {\n                        return subscription.channels.includes(channel);\n                    })\n\n                    if (!found) {\n                        this.realtime.channels.delete(channel);\n                    }\n                }\n            })\n        }\n    }\n\n    /**\n     * Subscribes to Appwrite events and passes you the payload in realtime.\n     * \n     * @param {string|string[]} channels \n     * Channel to subscribe - pass a single channel as a string or multiple with an array of strings.\n     * \n     * Possible channels are:\n     * - account\n     * - collections\n     * - collections.[ID]\n     * - collections.[ID].documents\n     * - documents\n     * - documents.[ID]\n     * - files\n     * - files.[ID]\n     * - executions\n     * - executions.[ID]\n     * - functions.[ID]\n     * - teams\n     * - teams.[ID]\n     * - memberships\n     * - memberships.[ID]\n     * @param {(payload: RealtimeMessage) => void} callback Is called on every realtime update.\n     * @returns {() => void} Unsubscribes from events.\n     */\n    subscribe<T extends unknown>(channels: string | string[], callback: (payload: RealtimeResponseEvent<T>) => void): () => void {\n        let channelArray = typeof channels === 'string' ? [channels] : channels;\n        channelArray.forEach(channel => this.realtime.channels.add(channel));\n\n        const counter = this.realtime.subscriptionsCounter++;\n        this.realtime.subscriptions.set(counter, {\n            channels: channelArray,\n            callback\n        });\n\n        this.realtime.connect();\n\n        return () => {\n            this.realtime.subscriptions.delete(counter);\n            this.realtime.cleanUp(channelArray);\n            this.realtime.connect();\n        }\n    }\n\n    async call(method: string, url: URL, headers: Headers = {}, params: Payload = {}): Promise<any> {\n        method = method.toUpperCase();\n\n\n        headers = Object.assign({}, this.headers, headers);\n\n        let options: RequestInit = {\n            method,\n            headers,\n            credentials: 'include'\n        };\n\n        if (typeof window !== 'undefined' && window.localStorage) {\n            const cookieFallback = window.localStorage.getItem('cookieFallback');\n            if (cookieFallback) {\n                headers['X-Fallback-Cookies'] = cookieFallback;\n            }\n        }\n\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Service.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        } else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n\n                case 'multipart/form-data':\n                    let formData = new FormData();\n\n                    for (const key in params) {\n                        if (Array.isArray(params[key])) {\n                            params[key].forEach((value: any) => {\n                                formData.append(key + '[]', value);\n                            })\n                        } else {\n                            formData.append(key, params[key]);\n                        }\n                    }\n\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n\n        try {\n            let data = null;\n            const response = await fetch(url.toString(), options);\n\n            if (response.headers.get('content-type')?.includes('application/json')) {\n                data = await response.json();\n            } else {\n                data = {\n                    message: await response.text()\n                };\n            }\n\n            if (400 <= response.status) {\n                throw new AppwriteException(data?.message, response.status, data?.type, data);\n            }\n\n            const cookieFallback = response.headers.get('X-Fallback-Cookies');\n\n            if (typeof window !== 'undefined' && window.localStorage && cookieFallback) {\n                window.console.warn('Appwrite is using localStorage for session management. Increase your security by adding a custom domain as your API endpoint.');\n                window.localStorage.setItem('cookieFallback', cookieFallback);\n            }\n\n            return data;\n        } catch (e) {\n            if (e instanceof AppwriteException) {\n                throw e;\n            }\n            throw new AppwriteException((<Error>e).message);\n        }\n    }\n}\n\nexport { Client, AppwriteException };\nexport { Query } from './query';\nexport type { Models, Payload };\nexport type { QueryTypes, QueryTypesList } from './query';\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\nimport { AuthenticatorType } from '../enums/authenticator-type';\nimport { AuthenticationFactor } from '../enums/authentication-factor';\nimport { OAuthProvider } from '../enums/o-auth-provider';\n\nexport class Account extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * Get account\n     *\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async get<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create account\n     *\n     * Use this endpoint to allow a new user to register a new account in your\n     * project. After the user registration completes successfully, you can use\n     * the\n     * [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification)\n     * route to start verifying the user email address. To allow the new user to\n     * login to their new account, you need to create a new [account\n     * session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async create<Preferences extends Models.Preferences>(userId: string, email: string, password: string, name?: string): Promise<Models.User<Preferences>> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update email\n     *\n     * Update currently logged in user account email address. After changing user\n     * address, the user confirmation status will get reset. A new confirmation\n     * email is not sent automatically however you can use the send confirmation\n     * email endpoint again to send the confirmation email. For security measures,\n     * user password is required to complete this request.\n     * This endpoint can also be used to convert an anonymous account to a normal\n     * one, by passing an email address and a new password.\n     * \n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateEmail<Preferences extends Models.Preferences>(email: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account/email';\n        const payload: Payload = {};\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List Identities\n     *\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listIdentities(queries?: string[]): Promise<Models.IdentityList> {\n        const apiPath = '/account/identities';\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete identity\n     *\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteIdentity(identityId: string): Promise<{}> {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create JWT\n     *\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT\n     * to authenticate on behalf of the current user when working with the\n     * Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes\n     * from its creation and will be invalid if the user will logout in that time\n     * frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createJWT(): Promise<Models.Jwt> {\n        const apiPath = '/account/jwt';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List logs\n     *\n     * Get the list of latest security activity logs for the currently logged in\n     * user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listLogs(queries?: string[]): Promise<Models.LogList> {\n        const apiPath = '/account/logs';\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update MFA\n     *\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMFA<Preferences extends Models.Preferences>(mfa: boolean): Promise<Models.User<Preferences>> {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n\n        const apiPath = '/account/mfa';\n        const payload: Payload = {};\n\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Add Authenticator\n     *\n     * Add an authenticator app to be used as an MFA factor. Verify the\n     * authenticator using the [verify\n     * authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator)\n     * method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createMfaAuthenticator(type: AuthenticatorType): Promise<Models.MfaType> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Verify Authenticator\n     *\n     * Verify an authenticator app after adding it using the [add\n     * authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator)\n     * method. add \n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMfaAuthenticator<Preferences extends Models.Preferences>(type: AuthenticatorType, otp: string): Promise<Models.User<Preferences>> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete Authenticator\n     *\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteMfaAuthenticator(type: AuthenticatorType, otp: string): Promise<{}> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create 2FA Challenge\n     *\n     * Begin the process of MFA verification after sign-in. Finish the flow with\n     * [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge)\n     * method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createMfaChallenge(factor: AuthenticationFactor): Promise<Models.MfaChallenge> {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create MFA Challenge (confirmation)\n     *\n     * Complete the MFA challenge by providing the one-time password. Finish the\n     * process of MFA verification by providing the one-time password. To begin\n     * the flow, use\n     * [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge)\n     * method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMfaChallenge(challengeId: string, otp: string): Promise<{}> {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List Factors\n     *\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listMfaFactors(): Promise<Models.MfaFactors> {\n        const apiPath = '/account/mfa/factors';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get MFA Recovery Codes\n     *\n     * Get recovery codes that can be used as backup for MFA flow. Before getting\n     * codes, they must be generated using\n     * [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes)\n     * method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create MFA Recovery Codes\n     *\n     * Generate recovery codes as backup for MFA flow. It's recommended to\n     * generate and show then immediately after user successfully adds their\n     * authehticator. Recovery codes can be used as a MFA verification type in\n     * [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge)\n     * method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Regenerate MFA Recovery Codes\n     *\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before\n     * regenerating codes, they must be first generated using\n     * [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes)\n     * method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update name\n     *\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateName<Preferences extends Models.Preferences>(name: string): Promise<Models.User<Preferences>> {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n\n        const apiPath = '/account/name';\n        const payload: Payload = {};\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update password\n     *\n     * Update currently logged in user password. For validation, user is required\n     * to pass in the new password, and the old password. For users created with\n     * OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePassword<Preferences extends Models.Preferences>(password: string, oldPassword?: string): Promise<Models.User<Preferences>> {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account/password';\n        const payload: Payload = {};\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update phone\n     *\n     * Update the currently logged in user's phone number. After updating the\n     * phone number, the phone verification status will be reset. A confirmation\n     * SMS is not sent automatically, however you can use the [POST\n     * /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification)\n     * endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePhone<Preferences extends Models.Preferences>(phone: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account/phone';\n        const payload: Payload = {};\n\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get account preferences\n     *\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getPrefs<Preferences extends Models.Preferences>(): Promise<Preferences> {\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update preferences\n     *\n     * Update currently logged in user account preferences. The object you pass is\n     * stored as is, and replaces any previous value. The maximum allowed prefs\n     * size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePrefs<Preferences extends Models.Preferences>(prefs: Partial<Preferences>): Promise<Models.User<Preferences>> {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create password recovery\n     *\n     * Sends the user an email with a temporary secret key for password reset.\n     * When the user clicks the confirmation link he is redirected back to your\n     * app password reset URL with the secret key and email address values\n     * attached to the URL query string. Use the query string params to submit a\n     * request to the [PUT\n     * /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery)\n     * endpoint to complete the process. The verification link sent to the user's\n     * email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createRecovery(email: string, url: string): Promise<Models.Token> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create password recovery (confirmation)\n     *\n     * Use this endpoint to complete the user account password reset. Both the\n     * **userId** and **secret** arguments will be passed as query parameters to\n     * the redirect URL you have provided when sending your request to the [POST\n     * /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery)\n     * endpoint.\n     * \n     * Please note that in order to avoid a [Redirect\n     * Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md)\n     * the only valid redirect URLs are the ones from domains you have set when\n     * adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateRecovery(userId: string, secret: string, password: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List sessions\n     *\n     * Get the list of active sessions across different devices for the currently\n     * logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listSessions(): Promise<Models.SessionList> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete sessions\n     *\n     * Delete all sessions from the user account and remove any sessions cookies\n     * from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteSessions(): Promise<{}> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create anonymous session\n     *\n     * Use this endpoint to allow a new user to register an anonymous account in\n     * your project. This route will also create a new session for the user. To\n     * allow the new user to convert an anonymous account to a normal account, you\n     * need to update its [email and\n     * password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail)\n     * or create an [OAuth2\n     * session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createAnonymousSession(): Promise<Models.Session> {\n        const apiPath = '/account/sessions/anonymous';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create email password session\n     *\n     * Allow the user to login into their account by providing a valid email and\n     * password combination. This route will create a new session for the user.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createEmailPasswordSession(email: string, password: string): Promise<Models.Session> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n\n        const apiPath = '/account/sessions/email';\n        const payload: Payload = {};\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update magic URL session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId**\n     * and **secret** parameters from the successful response of authentication\n     * flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMagicURLSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/account/sessions/magic-url';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create OAuth2 session\n     *\n     * Allow the user to login to their account using the OAuth2 provider of their\n     * choice. Each OAuth2 provider should be enabled from the Appwrite console\n     * first. Use the success and failure arguments to provide a redirect URL's\n     * back to your app when login is completed.\n     * \n     * If there is already an active session, the new session will be attached to\n     * the logged-in account. If there are no active sessions, the server will\n     * attempt to look for a user with the same email address as the email\n     * received from the OAuth2 provider and attach the new session to the\n     * existing user. If no matching user is found - the server will create a new\n     * user.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     * \n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void|string}\n    */\n    createOAuth2Session(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): void | URL {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n\n        const apiPath = '/account/sessions/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n        } else {\n            return uri;\n        }\n    }\n\n    /**\n     * Update phone session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId**\n     * and **secret** parameters from the successful response of authentication\n     * flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePhoneSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/account/sessions/phone';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId**\n     * and **secret** parameters from the successful response of authentication\n     * flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/account/sessions/token';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get session\n     *\n     * Use this endpoint to get a logged in user's session using a Session ID.\n     * Inputting 'current' will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update session\n     *\n     * Use this endpoint to extend a session's length. Extending a session is\n     * useful when session expiry is short. If the session was created using an\n     * OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete session\n     *\n     * Logout the user. Use 'current' as the session ID to logout on this device,\n     * use a session ID to logout on another device. If you're looking to logout\n     * the user on all devices, use [Delete\n     * Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions)\n     * instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteSession(sessionId: string): Promise<{}> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update status\n     *\n     * Block the currently logged in user account. Behind the scene, the user\n     * record is not deleted but permanently blocked from any access. To\n     * completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateStatus<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account/status';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create push target\n     *\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createPushTarget(targetId: string, identifier: string, providerId?: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n\n        const apiPath = '/account/targets/push';\n        const payload: Payload = {};\n\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update push target\n     *\n     *\n     * @param {string} targetId\n     * @param {string} identifier\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePushTarget(targetId: string, identifier: string): Promise<Models.Target> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n\n        if (typeof identifier === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identifier\"');\n        }\n\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n\n        if (typeof identifier !== 'undefined') {\n            payload['identifier'] = identifier;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete push target\n     *\n     *\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deletePushTarget(targetId: string): Promise<{}> {\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n\n        const apiPath = '/account/targets/{targetId}/push'.replace('{targetId}', targetId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create email token (OTP)\n     *\n     * Sends the user an email with a secret key for creating a session. If the\n     * provided user ID has not be registered, a new user will be created. Use the\n     * returned user ID and secret and submit a request to the [POST\n     * /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession)\n     * endpoint to complete the login process. The secret sent to the user's email\n     * is valid for 15 minutes.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createEmailToken(userId: string, email: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        const apiPath = '/account/tokens/email';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create magic URL token\n     *\n     * Sends the user an email with a secret key for creating a session. If the\n     * provided user ID has not been registered, a new user will be created. When\n     * the user clicks the link in the email, the user is redirected back to the\n     * URL you provided with the secret key and userId values attached to the URL\n     * query string. Use the query string parameters to submit a request to the\n     * [POST\n     * /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession)\n     * endpoint to complete the login process. The link sent to the user's email\n     * address is valid for 1 hour. If you are on a mobile device you can leave\n     * the URL parameter empty, so that the login completion will be handled by\n     * your Appwrite instance by default.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     * \n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createMagicURLToken(userId: string, email: string, url?: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n\n        const apiPath = '/account/tokens/magic-url';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create OAuth2 token\n     *\n     * Allow the user to login to their account using the OAuth2 provider of their\n     * choice. Each OAuth2 provider should be enabled from the Appwrite console\n     * first. Use the success and failure arguments to provide a redirect URL's\n     * back to your app when login is completed. \n     * \n     * If authentication succeeds, `userId` and `secret` of a token will be\n     * appended to the success URL as query parameters. These can be used to\n     * create a new session using the [Create\n     * session](https://appwrite.io/docs/references/cloud/client-web/account#createSession)\n     * endpoint.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {void|string}\n    */\n    createOAuth2Token(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): void | URL {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        if (typeof window !== 'undefined' && window?.location) {\n            window.location.href = uri.toString();\n        } else {\n            return uri;\n        }\n    }\n\n    /**\n     * Create phone token\n     *\n     * Sends the user an SMS with a secret key for creating a session. If the\n     * provided user ID has not be registered, a new user will be created. Use the\n     * returned user ID and secret and submit a request to the [POST\n     * /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession)\n     * endpoint to complete the login process. The secret sent to the user's phone\n     * is valid for 15 minutes.\n     * \n     * A user is limited to 10 active sessions at a time by default. [Learn more\n     * about session\n     * limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createPhoneToken(userId: string, phone: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n\n        const apiPath = '/account/tokens/phone';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create email verification\n     *\n     * Use this endpoint to send a verification message to your user email address\n     * to confirm they are the valid owners of that address. Both the **userId**\n     * and **secret** arguments will be passed as query parameters to the URL you\n     * have provided to be attached to the verification email. The provided URL\n     * should redirect the user back to your app and allow you to complete the\n     * verification process by verifying both the **userId** and **secret**\n     * parameters. Learn more about how to [complete the verification\n     * process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification).\n     * The verification link sent to the user's email address is valid for 7 days.\n     * \n     * Please note that in order to avoid a [Redirect\n     * Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md),\n     * the only valid redirect URLs are the ones from domains you have set when\n     * adding your platforms in the console interface.\n     * \n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createVerification(url: string): Promise<Models.Token> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create email verification (confirmation)\n     *\n     * Use this endpoint to complete the user email verification process. Use both\n     * the **userId** and **secret** parameters that were attached to your app URL\n     * to verify the user email ownership. If confirmed this route will return a\n     * 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create phone verification\n     *\n     * Use this endpoint to send a verification SMS to the currently logged in\n     * user. This endpoint is meant for use after updating a user's phone number\n     * using the\n     * [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone)\n     * endpoint. Learn more about how to [complete the verification\n     * process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification).\n     * The verification code sent to the user's phone number is valid for 15\n     * minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createPhoneVerification(): Promise<Models.Token> {\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create phone verification (confirmation)\n     *\n     * Use this endpoint to complete the user phone verification process. Use the\n     * **userId** and **secret** that were sent to your user's phone number to\n     * verify the user email ownership. If confirmed this route will return a 200\n     * status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePhoneVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\nimport { <PERSON>rowser } from '../enums/browser';\nimport { CreditCard } from '../enums/credit-card';\nimport { Flag } from '../enums/flag';\n\nexport class Avatars extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * Get browser icon\n     *\n     * You can use this endpoint to show different browser icons to your users.\n     * The code argument receives the browser code as it appears in your user [GET\n     * /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions)\n     * endpoint. Use width, height and quality arguments to change the output\n     * settings.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled\n     * with preserved aspect ratio. If both dimensions are 0, the API provides an\n     * image at source quality. If dimensions are not specified, the default size\n     * of image returned is 100x100px.\n     *\n     * @param {<PERSON>rowser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getBrowser(code: Browser, width?: number, height?: number, quality?: number): URL {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get credit card icon\n     *\n     * The credit card endpoint will return you the icon of the credit card\n     * provider you need. Use width, height and quality arguments to change the\n     * output settings.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled\n     * with preserved aspect ratio. If both dimensions are 0, the API provides an\n     * image at source quality. If dimensions are not specified, the default size\n     * of image returned is 100x100px.\n     * \n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getCreditCard(code: CreditCard, width?: number, height?: number, quality?: number): URL {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get favicon\n     *\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote\n     * website URL.\n     * \n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getFavicon(url: string): URL {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n\n        const apiPath = '/avatars/favicon';\n        const payload: Payload = {};\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get country flag\n     *\n     * You can use this endpoint to show different country flags icons to your\n     * users. The code argument receives the 2 letter country code. Use width,\n     * height and quality arguments to change the output settings. Country codes\n     * follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled\n     * with preserved aspect ratio. If both dimensions are 0, the API provides an\n     * image at source quality. If dimensions are not specified, the default size\n     * of image returned is 100x100px.\n     * \n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getFlag(code: Flag, width?: number, height?: number, quality?: number): URL {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get image from URL\n     *\n     * Use this endpoint to fetch a remote image URL and crop it to any image size\n     * you want. This endpoint is very useful if you need to crop and display\n     * remote images in your app or in case you want to make sure a 3rd party\n     * image is properly served using a TLS protocol.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled\n     * with preserved aspect ratio. If both dimensions are 0, the API provides an\n     * image at source quality. If dimensions are not specified, the default size\n     * of image returned is 400x400px.\n     * \n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getImage(url: string, width?: number, height?: number): URL {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n\n        const apiPath = '/avatars/image';\n        const payload: Payload = {};\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get user initials\n     *\n     * Use this endpoint to show your user initials avatar icon on your website or\n     * app. By default, this route will try to print your logged-in user name or\n     * email initials. You can also overwrite the user name if you pass the 'name'\n     * parameter. If no name is given and no user is logged, an empty avatar will\n     * be returned.\n     * \n     * You can use the color and background params to change the avatar colors. By\n     * default, a random theme will be selected. The random theme will persist for\n     * the user's initials when reloading the same theme will always return for\n     * the same initials.\n     * \n     * When one dimension is specified and the other is 0, the image is scaled\n     * with preserved aspect ratio. If both dimensions are 0, the API provides an\n     * image at source quality. If dimensions are not specified, the default size\n     * of image returned is 100x100px.\n     * \n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getInitials(name?: string, width?: number, height?: number, background?: string): URL {\n        const apiPath = '/avatars/initials';\n        const payload: Payload = {};\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get QR code\n     *\n     * Converts a given plain text to a QR code image. You can use the query\n     * parameters to change the size and style of the resulting image.\n     * \n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getQR(text: string, size?: number, margin?: number, download?: boolean): URL {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n\n        const apiPath = '/avatars/qr';\n        const payload: Payload = {};\n\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\n\nexport class Databases extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * List documents\n     *\n     * Get a list of all the user's documents in a given collection. You can use\n     * the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listDocuments<Document extends Models.Document>(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.DocumentList<Document>> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create document\n     *\n     * Create a new Document. Before using this route, you should create a new\n     * collection resource using either a [server\n     * integration](https://appwrite.io/docs/server/databases#databasesCreateCollection)\n     * API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data: Omit<Document, keyof Models.Document>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get document\n     *\n     * Get a document by its unique ID. This endpoint response returns a JSON\n     * object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, queries?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update document\n     *\n     * Update a document by its unique ID. Using the patch method you can pass\n     * only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data?: Partial<Omit<Document, keyof Models.Document>>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete document\n     *\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteDocument(databaseId: string, collectionId: string, documentId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\nimport { ExecutionMethod } from '../enums/execution-method';\n\nexport class Functions extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * List executions\n     *\n     * Get a list of all the current user function execution logs. You can use the\n     * query params to filter your results.\n     *\n     * @param {string} functionId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listExecutions(functionId: string, queries?: string[], search?: string): Promise<Models.ExecutionList> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create execution\n     *\n     * Trigger a function execution. The returned object will return you the\n     * current execution status. You can ping the `Get Execution` endpoint to get\n     * updates on the current execution status. Once this endpoint is called, your\n     * function execution process will start asynchronously.\n     *\n     * @param {string} functionId\n     * @param {string} body\n     * @param {boolean} async\n     * @param {string} xpath\n     * @param {ExecutionMethod} method\n     * @param {object} headers\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createExecution(functionId: string, body?: string, async?: boolean, xpath?: string, method?: ExecutionMethod, headers?: object): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n\n        const apiPath = '/functions/{functionId}/executions'.replace('{functionId}', functionId);\n        const payload: Payload = {};\n\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n\n        if (typeof async !== 'undefined') {\n            payload['async'] = async;\n        }\n\n        if (typeof xpath !== 'undefined') {\n            payload['path'] = xpath;\n        }\n\n        if (typeof method !== 'undefined') {\n            payload['method'] = method;\n        }\n\n        if (typeof headers !== 'undefined') {\n            payload['headers'] = headers;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get execution\n     *\n     * Get a function execution log by its unique ID.\n     *\n     * @param {string} functionId\n     * @param {string} executionId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getExecution(functionId: string, executionId: string): Promise<Models.Execution> {\n        if (typeof functionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"functionId\"');\n        }\n\n        if (typeof executionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"executionId\"');\n        }\n\n        const apiPath = '/functions/{functionId}/executions/{executionId}'.replace('{functionId}', functionId).replace('{executionId}', executionId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\n\nexport class Graphql extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async query(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n\n        const apiPath = '/graphql';\n        const payload: Payload = {};\n\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async mutation(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n\n        const apiPath = '/graphql/mutation';\n        const payload: Payload = {};\n\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\n\nexport class Locale extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * Get user locale\n     *\n     * Get the current user location based on IP. Returns an object with user\n     * country code, country name, continent name, continent code, ip address and\n     * suggested currency. You can use the locale header to get the data in a\n     * supported language.\n     * \n     * ([IP Geolocation by DB-IP](https://db-ip.com))\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async get(): Promise<Models.Locale> {\n        const apiPath = '/locale';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List Locale Codes\n     *\n     * List of all locale codes in [ISO\n     * 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listCodes(): Promise<Models.LocaleCodeList> {\n        const apiPath = '/locale/codes';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List continents\n     *\n     * List of all continents. You can use the locale header to get the data in a\n     * supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listContinents(): Promise<Models.ContinentList> {\n        const apiPath = '/locale/continents';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List countries\n     *\n     * List of all countries. You can use the locale header to get the data in a\n     * supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listCountries(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List EU countries\n     *\n     * List of all countries that are currently members of the EU. You can use the\n     * locale header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listCountriesEU(): Promise<Models.CountryList> {\n        const apiPath = '/locale/countries/eu';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List countries phone codes\n     *\n     * List of all countries phone codes. You can use the locale header to get the\n     * data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listCountriesPhones(): Promise<Models.PhoneList> {\n        const apiPath = '/locale/countries/phones';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List currencies\n     *\n     * List of all currencies, including currency symbol, name, plural, and\n     * decimal digits for all major and minor currencies. You can use the locale\n     * header to get the data in a supported language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listCurrencies(): Promise<Models.CurrencyList> {\n        const apiPath = '/locale/currencies';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List languages\n     *\n     * List of all languages classified by ISO 639-1 including 2-letter code, name\n     * in English, and name in the respective language.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listLanguages(): Promise<Models.LanguageList> {\n        const apiPath = '/locale/languages';\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\n\nexport class Messaging extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * Create subscriber\n     *\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createSubscriber(topicId: string, subscriberId: string, targetId: string): Promise<Models.Subscriber> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete subscriber\n     *\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteSubscriber(topicId: string, subscriberId: string): Promise<{}> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\nimport { ImageGravity } from '../enums/image-gravity';\nimport { ImageFormat } from '../enums/image-format';\n\nexport class Storage extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * List files\n     *\n     * Get a list of all the user files. You can use the query params to filter\n     * your results.\n     *\n     * @param {string} bucketId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listFiles(bucketId: string, queries?: string[], search?: string): Promise<Models.FileList> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create file\n     *\n     * Create a new file. Before using this route, you should create a new bucket\n     * resource using either a [server\n     * integration](https://appwrite.io/docs/server/storage#storageCreateBucket)\n     * API or directly from your Appwrite console.\n     * \n     * Larger files should be uploaded using multiple requests with the\n     * [content-range](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Range)\n     * header to send a partial request with a maximum supported chunk of `5MB`.\n     * The `content-range` header values should always be in bytes.\n     * \n     * When the first request is sent, the server will return the **File** object,\n     * and the subsequent part request must include the file's **id** in\n     * `x-appwrite-id` header to allow the server to know that the partial upload\n     * is for the existing file and not for a new one.\n     * \n     * If you're creating a new file using one of the Appwrite SDKs, all the\n     * chunking logic will be managed by the SDK internally.\n     * \n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {File} file\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createFile(bucketId: string, fileId: string, file: File, permissions?: string[], onProgress = (progress: UploadProgress) => {}): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        if (typeof file === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"file\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files'.replace('{bucketId}', bucketId);\n        const payload: Payload = {};\n\n        if (typeof fileId !== 'undefined') {\n            payload['fileId'] = fileId;\n        }\n\n        if (typeof file !== 'undefined') {\n            payload['file'] = file;\n        }\n\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        if(!(file instanceof File)) {\n            throw new AppwriteException('Parameter \"file\" has to be a File.');\n        }\n\n        const size = file.size;\n\n        if (size <= Service.CHUNK_SIZE) {\n            return await this.client.call('post', uri, {\n                'content-type': 'multipart/form-data',\n            }, payload);\n        }\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'multipart/form-data',\n        }\n\n        let offset = 0;\n        let response = undefined;\n        if(fileId != 'unique()') {\n            try {\n                response = await this.client.call('GET', new URL(this.client.config.endpoint + apiPath + '/' + fileId), apiHeaders);\n                offset = response.chunksUploaded * Service.CHUNK_SIZE;\n            } catch(e) {\n            }\n        }\n\n        while (offset < size) {\n            let end = Math.min(offset + Service.CHUNK_SIZE - 1, size - 1);\n\n            apiHeaders['content-range'] = 'bytes ' + offset + '-' + end + '/' + size;\n            if (response && response.$id) {\n                apiHeaders['x-appwrite-id'] = response.$id;\n            }\n\n            const chunk = file.slice(offset, end + 1);\n            payload['file'] = new File([chunk], file.name);\n            response = await this.client.call('post', uri, apiHeaders, payload);\n\n            if (onProgress) {\n                onProgress({\n                    $id: response.$id,\n                    progress: (offset / size) * 100,\n                    sizeUploaded: offset,\n                    chunksTotal: response.chunksTotal,\n                    chunksUploaded: response.chunksUploaded\n                });\n            }\n            offset += Service.CHUNK_SIZE;\n        }\n        return response;\n    }\n\n    /**\n     * Get file\n     *\n     * Get a file by its unique ID. This endpoint response returns a JSON object\n     * with the file metadata.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getFile(bucketId: string, fileId: string): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update file\n     *\n     * Update a file by its unique ID. Only users with write permissions have\n     * access to update this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateFile(bucketId: string, fileId: string, name?: string, permissions?: string[]): Promise<Models.File> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete File\n     *\n     * Delete a file by its unique ID. Only users with write permissions have\n     * access to delete this resource.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteFile(bucketId: string, fileId: string): Promise<{}> {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get file for download\n     *\n     * Get a file content by its unique ID. The endpoint response return with a\n     * 'Content-Disposition: attachment' header that tells the browser to start\n     * downloading the file to user downloads directory.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getFileDownload(bucketId: string, fileId: string): URL {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/download'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get file preview\n     *\n     * Get a file preview image. Currently, this method supports preview for image\n     * files (jpg, png, and gif), other supported formats, like pdf, docs, slides,\n     * and spreadsheets, will return the file icon image. You can also pass query\n     * string arguments for cutting and resizing your preview image. Preview is\n     * supported only for image files smaller than 10MB.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @param {number} width\n     * @param {number} height\n     * @param {ImageGravity} gravity\n     * @param {number} quality\n     * @param {number} borderWidth\n     * @param {string} borderColor\n     * @param {number} borderRadius\n     * @param {number} opacity\n     * @param {number} rotation\n     * @param {string} background\n     * @param {ImageFormat} output\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getFilePreview(bucketId: string, fileId: string, width?: number, height?: number, gravity?: ImageGravity, quality?: number, borderWidth?: number, borderColor?: string, borderRadius?: number, opacity?: number, rotation?: number, background?: string, output?: ImageFormat): URL {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/preview'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n\n        if (typeof gravity !== 'undefined') {\n            payload['gravity'] = gravity;\n        }\n\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n\n        if (typeof borderWidth !== 'undefined') {\n            payload['borderWidth'] = borderWidth;\n        }\n\n        if (typeof borderColor !== 'undefined') {\n            payload['borderColor'] = borderColor;\n        }\n\n        if (typeof borderRadius !== 'undefined') {\n            payload['borderRadius'] = borderRadius;\n        }\n\n        if (typeof opacity !== 'undefined') {\n            payload['opacity'] = opacity;\n        }\n\n        if (typeof rotation !== 'undefined') {\n            payload['rotation'] = rotation;\n        }\n\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n\n        if (typeof output !== 'undefined') {\n            payload['output'] = output;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n\n    /**\n     * Get file for view\n     *\n     * Get a file content by its unique ID. This endpoint is similar to the\n     * download method but returns with no  'Content-Disposition: attachment'\n     * header.\n     *\n     * @param {string} bucketId\n     * @param {string} fileId\n     * @throws {AppwriteException}\n     * @returns {URL}\n    */\n    getFileView(bucketId: string, fileId: string): URL {\n        if (typeof bucketId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"bucketId\"');\n        }\n\n        if (typeof fileId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"fileId\"');\n        }\n\n        const apiPath = '/storage/buckets/{bucketId}/files/{fileId}/view'.replace('{bucketId}', bucketId).replace('{fileId}', fileId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        payload['project'] = this.client.config.project;\n\n\n        for (const [key, value] of Object.entries(Service.flatten(payload))) {\n            uri.searchParams.append(key, value);\n        }\n        return uri;\n    }\n};\n", "import { Service } from '../service';\nimport { AppwriteException, Client } from '../client';\nimport type { Models } from '../models';\nimport type { UploadProgress, Payload } from '../client';\n\nexport class Teams extends Service {\n\n     constructor(client: Client)\n     {\n        super(client);\n     }\n\n    /**\n     * List teams\n     *\n     * Get a list of all the teams in which the current user is a member. You can\n     * use the parameters to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async list<Preferences extends Models.Preferences>(queries?: string[], search?: string): Promise<Models.TeamList<Preferences>> {\n        const apiPath = '/teams';\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create team\n     *\n     * Create a new team. The user who creates the team will automatically be\n     * assigned as the owner of the team. Only the users with the owner role can\n     * invite new members, add new owners and delete or update the team.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async create<Preferences extends Models.Preferences>(teamId: string, name: string, roles?: string[]): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n\n        const apiPath = '/teams';\n        const payload: Payload = {};\n\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get team\n     *\n     * Get a team by its ID. All team members have read access for this resource.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async get<Preferences extends Models.Preferences>(teamId: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update name\n     *\n     * Update the team's name by its unique ID.\n     *\n     * @param {string} teamId\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateName<Preferences extends Models.Preferences>(teamId: string, name: string): Promise<Models.Team<Preferences>> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete team\n     *\n     * Delete a team using its ID. Only team members with the owner role can\n     * delete the team.\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async delete(teamId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        const apiPath = '/teams/{teamId}'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * List team memberships\n     *\n     * Use this endpoint to list a team's members using the team's ID. All team\n     * members have read access to this endpoint.\n     *\n     * @param {string} teamId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async listMemberships(teamId: string, queries?: string[], search?: string): Promise<Models.MembershipList> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Create team membership\n     *\n     * Invite a new member to join your team. Provide an ID for existing users, or\n     * invite unregistered users using an email or phone number. If initiated from\n     * a Client SDK, Appwrite will send an email or sms with a link to join the\n     * team to the invited user, and an account will be created for them if one\n     * doesn't exist. If initiated from a Server SDK, the new member will be added\n     * automatically to the team.\n     * \n     * You only need to provide one of a user ID, email, or phone number. Appwrite\n     * will prioritize accepting the user ID > email > phone number if you provide\n     * more than one of these parameters.\n     * \n     * Use the `url` parameter to redirect the user from the invitation email to\n     * your app. After the user is redirected, use the [Update Team Membership\n     * Status](https://appwrite.io/docs/references/cloud/client-web/teams#updateMembershipStatus)\n     * endpoint to allow the user to accept the invitation to the team. \n     * \n     * Please note that to avoid a [Redirect\n     * Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md)\n     * Appwrite will accept the only redirect URLs under the domains you have\n     * added as a platform on the Appwrite Console.\n     * \n     *\n     * @param {string} teamId\n     * @param {string[]} roles\n     * @param {string} email\n     * @param {string} userId\n     * @param {string} phone\n     * @param {string} url\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async createMembership(teamId: string, roles: string[], email?: string, userId?: string, phone?: string, url?: string, name?: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('post', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get team membership\n     *\n     * Get a team member by the membership unique id. All team members have read\n     * access for this resource.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getMembership(teamId: string, membershipId: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update membership\n     *\n     * Modify the roles of a team member. Only team members with the owner role\n     * have access to this endpoint. Learn more about [roles and\n     * permissions](https://appwrite.io/docs/permissions).\n     * \n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string[]} roles\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMembership(teamId: string, membershipId: string, roles: string[]): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n\n        if (typeof roles === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"roles\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n\n        if (typeof roles !== 'undefined') {\n            payload['roles'] = roles;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Delete team membership\n     *\n     * This endpoint allows a user to leave a team or for a team owner to delete\n     * the membership of any other team member. You can also use this endpoint to\n     * delete a user membership even if it is not accepted.\n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async deleteMembership(teamId: string, membershipId: string): Promise<{}> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('delete', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update team membership status\n     *\n     * Use this endpoint to allow a user to accept an invitation to join a team\n     * after being redirected back to your app from the invitation email received\n     * by the user.\n     * \n     * If the request is successful, a session for the user is automatically\n     * created.\n     * \n     *\n     * @param {string} teamId\n     * @param {string} membershipId\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updateMembershipStatus(teamId: string, membershipId: string, userId: string, secret: string): Promise<Models.Membership> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof membershipId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"membershipId\"');\n        }\n\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n\n        const apiPath = '/teams/{teamId}/memberships/{membershipId}/status'.replace('{teamId}', teamId).replace('{membershipId}', membershipId);\n        const payload: Payload = {};\n\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('patch', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Get team preferences\n     *\n     * Get the team's shared preferences by its unique ID. If a preference doesn't\n     * need to be shared by all team members, prefer storing them in [user\n     * preferences](https://appwrite.io/docs/references/cloud/client-web/account#getPrefs).\n     *\n     * @param {string} teamId\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async getPrefs<Preferences extends Models.Preferences>(teamId: string): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('get', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n\n    /**\n     * Update preferences\n     *\n     * Update the team's preferences by its unique ID. The object you pass is\n     * stored as is and replaces any previous value. The maximum allowed prefs\n     * size is 64kB and throws an error if exceeded.\n     *\n     * @param {string} teamId\n     * @param {object} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise}\n    */\n    async updatePrefs<Preferences extends Models.Preferences>(teamId: string, prefs: object): Promise<Preferences> {\n        if (typeof teamId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"teamId\"');\n        }\n\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n\n        const apiPath = '/teams/{teamId}/prefs'.replace('{teamId}', teamId);\n        const payload: Payload = {};\n\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n\n        const uri = new URL(this.client.config.endpoint + apiPath);\n        return await this.client.call('put', uri, {\n            'content-type': 'application/json',\n        }, payload);\n    }\n};\n", "export class Permission {\n\n    static read = (role: string): string => {\n        return `read(\"${role}\")`\n    }\n\n    static write = (role: string): string => {\n        return `write(\"${role}\")`\n    }\n\n    static create = (role: string): string => {\n        return `create(\"${role}\")`\n    }\n\n    static update = (role: string): string => {\n        return `update(\"${role}\")`\n    }\n\n    static delete = (role: string): string => {\n        return `delete(\"${role}\")`\n    }\n}\n", "/**\n * Helper class to generate role strings for `Permission`.\n */\nexport class Role {\n\n    /**\n     * Grants access to anyone.\n     * \n     * This includes authenticated and unauthenticated users.\n     * \n     * @returns {string}\n     */\n    public static any(): string {\n        return 'any'\n    }\n\n    /**\n     * Grants access to a specific user by user ID.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     *\n     * @param {string} id \n     * @param {string} status \n     * @returns {string}\n     */\n    public static user(id: string, status: string = ''): string {\n        if (status === '') {\n            return `user:${id}`\n        }\n        return `user:${id}/${status}`\n    }\n\n    /**\n     * Grants access to any authenticated or anonymous user.\n     * \n     * You can optionally pass verified or unverified for\n     * `status` to target specific types of users.\n     * \n     * @param {string} status \n     * @returns {string}\n     */\n    public static users(status: string = ''): string {\n        if (status === '') {\n            return 'users'\n        }\n        return `users/${status}`\n    }\n\n    /**\n     * Grants access to any guest user without a session.\n     * \n     * Authenticated users don't have access to this role.\n     * \n     * @returns {string}\n     */\n    public static guests(): string {\n        return 'guests'\n    }\n\n    /**\n     * Grants access to a team by team ID.\n     * \n     * You can optionally pass a role for `role` to target\n     * team members with the specified role.\n     * \n     * @param {string} id \n     * @param {string} role \n     * @returns {string}\n     */\n    public static team(id: string, role: string = ''): string {\n        if (role === '') {\n            return `team:${id}`\n        }\n        return `team:${id}/${role}`\n    }\n\n    /**\n     * Grants access to a specific member of a team.\n     * \n     * When the member is removed from the team, they will\n     * no longer have access.\n     * \n     * @param {string} id \n     * @returns {string}\n     */\n    public static member(id: string): string {\n        return `member:${id}`\n    }\n\n    /**\n     * Grants access to a user with the specified label.\n     * \n     * @param {string} name \n     * @returns  {string}\n     */\n    public static label(name: string): string {\n        return `label:${name}`\n    }\n}", "export class ID {\n    // Generate an hex ID based on timestamp\n    // Recreated from https://www.php.net/manual/en/function.uniqid.php\n    static #hexTimestamp(): string {\n        const now = new Date();\n        const sec = Math.floor(now.getTime() / 1000);\n        const msec = now.getMilliseconds();\n\n        // Convert to hexadecimal\n        const hexTimestamp = sec.toString(16) + msec.toString(16).padStart(5, '0');\n        return hexTimestamp;\n    }\n\n    public static custom(id: string): string {\n        return id\n    }\n\n    public static unique(padding: number = 7): string {\n        // Generate a unique ID with padding to have a longer ID\n        const baseId = ID.#hexTimestamp();\n        let randomPadding = '';\n        for (let i = 0; i < padding; i++) {\n            const randomHexDigit = Math.floor(Math.random() * 16).toString(16);\n            randomPadding += randomHexDigit;\n        }\n        return baseId + randomPadding;\n    }\n}\n", "export enum AuthenticatorType {\n    Totp = 'totp',\n}", "export enum AuthenticationFactor {\n    Email = 'email',\n    Phone = 'phone',\n    Totp = 'totp',\n    Recoverycode = 'recoverycode',\n}", "export enum OAuthProvider {\n    Amazon = 'amazon',\n    Apple = 'apple',\n    Auth0 = 'auth0',\n    Authentik = 'authentik',\n    Autodesk = 'autodesk',\n    Bitbucket = 'bitbucket',\n    Bitly = 'bitly',\n    Box = 'box',\n    Dailymotion = 'dailymotion',\n    Discord = 'discord',\n    Disqus = 'disqus',\n    Dropbox = 'dropbox',\n    Etsy = 'etsy',\n    Facebook = 'facebook',\n    Github = 'github',\n    Gitlab = 'gitlab',\n    Google = 'google',\n    Linkedin = 'linkedin',\n    Microsoft = 'microsoft',\n    Notion = 'notion',\n    Oidc = 'oidc',\n    Okta = 'okta',\n    Paypal = 'paypal',\n    PaypalSandbox = 'paypalSandbox',\n    Podio = 'podio',\n    Salesforce = 'salesforce',\n    Slack = 'slack',\n    Spotify = 'spotify',\n    Stripe = 'stripe',\n    Tradeshift = 'tradeshift',\n    TradeshiftBox = 'tradeshiftBox',\n    Twitch = 'twitch',\n    Wordpress = 'wordpress',\n    Yahoo = 'yahoo',\n    Yammer = 'yammer',\n    Yandex = 'yandex',\n    Zoho = 'zoho',\n    Zoom = 'zoom',\n    Mock = 'mock',\n}", "export enum Browser {\n    AvantBrowser = 'aa',\n    AndroidWebViewBeta = 'an',\n    GoogleChrome = 'ch',\n    GoogleChromeIOS = 'ci',\n    GoogleChromeMobile = 'cm',\n    Chromium = 'cr',\n    MozillaFirefox = 'ff',\n    Safari = 'sf',\n    MobileSafari = 'mf',\n    MicrosoftEdge = 'ps',\n    MicrosoftEdgeIOS = 'oi',\n    OperaMini = 'om',\n    Opera = 'op',\n    OperaNext = 'on',\n}", "export enum CreditCard {\n    AmericanExpress = 'amex',\n    Argencard = 'argencard',\n    Cabal = 'cabal',\n    Cencosud = 'cencosud',\n    DinersClub = 'diners',\n    Discover = 'discover',\n    Elo = 'elo',\n    Hipercard = 'hipercard',\n    JCB = 'jcb',\n    Mastercard = 'mastercard',\n    Naranja = 'naranja',\n    TarjetaShopping = 'targeta-shopping',\n    UnionChinaPay = 'union-china-pay',\n    Visa = 'visa',\n    MIR = 'mir',\n    Mae<PERSON> = 'maestro',\n}", "export enum Flag {\n    Afghanistan = 'af',\n    Angola = 'ao',\n    Albania = 'al',\n    Andorra = 'ad',\n    UnitedArabEmirates = 'ae',\n    Argentina = 'ar',\n    Armenia = 'am',\n    AntiguaAndBarbuda = 'ag',\n    Australia = 'au',\n    Austria = 'at',\n    Azerbaijan = 'az',\n    Burundi = 'bi',\n    Belgium = 'be',\n    Benin = 'bj',\n    BurkinaFaso = 'bf',\n    Bangladesh = 'bd',\n    Bulgaria = 'bg',\n    Bahrain = 'bh',\n    Bahamas = 'bs',\n    BosniaAndHerzegovina = 'ba',\n    Belarus = 'by',\n    Belize = 'bz',\n    Bolivia = 'bo',\n    Brazil = 'br',\n    Barbados = 'bb',\n    BruneiDarussalam = 'bn',\n    Bhutan = 'bt',\n    Botswana = 'bw',\n    CentralAfricanRepublic = 'cf',\n    Canada = 'ca',\n    Switzerland = 'ch',\n    Chile = 'cl',\n    China = 'cn',\n    CoteDIvoire = 'ci',\n    Cameroon = 'cm',\n    DemocraticRepublicOfTheCongo = 'cd',\n    RepublicOfTheCongo = 'cg',\n    Colombia = 'co',\n    Comoros = 'km',\n    CapeVerde = 'cv',\n    CostaRica = 'cr',\n    Cuba = 'cu',\n    Cyprus = 'cy',\n    CzechRepublic = 'cz',\n    Germany = 'de',\n    Djibouti = 'dj',\n    Dominica = 'dm',\n    Denmark = 'dk',\n    DominicanRepublic = 'do',\n    Algeria = 'dz',\n    Ecuador = 'ec',\n    Egypt = 'eg',\n    Eritrea = 'er',\n    Spain = 'es',\n    Estonia = 'ee',\n    Ethiopia = 'et',\n    Finland = 'fi',\n    Fiji = 'fj',\n    France = 'fr',\n    MicronesiaFederatedStatesOf = 'fm',\n    Gabon = 'ga',\n    UnitedKingdom = 'gb',\n    Georgia = 'ge',\n    Ghana = 'gh',\n    Guinea = 'gn',\n    Gambia = 'gm',\n    GuineaBissau = 'gw',\n    EquatorialGuinea = 'gq',\n    Greece = 'gr',\n    Grenada = 'gd',\n    Guatemala = 'gt',\n    Guyana = 'gy',\n    Honduras = 'hn',\n    Croatia = 'hr',\n    Haiti = 'ht',\n    Hungary = 'hu',\n    Indonesia = 'id',\n    India = 'in',\n    Ireland = 'ie',\n    IranIslamicRepublicOf = 'ir',\n    Iraq = 'iq',\n    Iceland = 'is',\n    Israel = 'il',\n    Italy = 'it',\n    Jamaica = 'jm',\n    Jordan = 'jo',\n    Japan = 'jp',\n    Kazakhstan = 'kz',\n    Kenya = 'ke',\n    Kyrgyzstan = 'kg',\n    Cambodia = 'kh',\n    Kiribati = 'ki',\n    SaintKittsAndNevis = 'kn',\n    SouthKorea = 'kr',\n    Kuwait = 'kw',\n    LaoPeopleSDemocraticRepublic = 'la',\n    Lebanon = 'lb',\n    Liberia = 'lr',\n    Libya = 'ly',\n    SaintLucia = 'lc',\n    Liechtenstein = 'li',\n    SriLanka = 'lk',\n    Lesotho = 'ls',\n    Lithuania = 'lt',\n    Luxembourg = 'lu',\n    Latvia = 'lv',\n    Morocco = 'ma',\n    Monaco = 'mc',\n    Moldova = 'md',\n    Madagascar = 'mg',\n    Maldives = 'mv',\n    Mexico = 'mx',\n    MarshallIslands = 'mh',\n    NorthMacedonia = 'mk',\n    Mali = 'ml',\n    Malta = 'mt',\n    Myanmar = 'mm',\n    Montenegro = 'me',\n    Mongolia = 'mn',\n    Mozambique = 'mz',\n    Mauritania = 'mr',\n    Mauritius = 'mu',\n    Malawi = 'mw',\n    Malaysia = 'my',\n    Namibia = 'na',\n    Niger = 'ne',\n    Nigeria = 'ng',\n    Nicaragua = 'ni',\n    Netherlands = 'nl',\n    Norway = 'no',\n    Nepal = 'np',\n    Nauru = 'nr',\n    NewZealand = 'nz',\n    Oman = 'om',\n    Pakistan = 'pk',\n    Panama = 'pa',\n    Peru = 'pe',\n    Philippines = 'ph',\n    Palau = 'pw',\n    PapuaNewGuinea = 'pg',\n    Poland = 'pl',\n    FrenchPolynesia = 'pf',\n    NorthKorea = 'kp',\n    Portugal = 'pt',\n    Paraguay = 'py',\n    Qatar = 'qa',\n    Romania = 'ro',\n    Russia = 'ru',\n    Rwanda = 'rw',\n    SaudiArabia = 'sa',\n    Sudan = 'sd',\n    Senegal = 'sn',\n    Singapore = 'sg',\n    SolomonIslands = 'sb',\n    SierraLeone = 'sl',\n    ElSalvador = 'sv',\n    SanMarino = 'sm',\n    Somalia = 'so',\n    Serbia = 'rs',\n    SouthSudan = 'ss',\n    SaoTomeAndPrincipe = 'st',\n    Suriname = 'sr',\n    Slovakia = 'sk',\n    Slovenia = 'si',\n    Sweden = 'se',\n    Eswatini = 'sz',\n    Seychelles = 'sc',\n    Syria = 'sy',\n    Chad = 'td',\n    Togo = 'tg',\n    Thailand = 'th',\n    Tajikistan = 'tj',\n    Turkmenistan = 'tm',\n    TimorLeste = 'tl',\n    Tonga = 'to',\n    TrinidadAndTobago = 'tt',\n    Tunisia = 'tn',\n    Turkey = 'tr',\n    Tuvalu = 'tv',\n    Tanzania = 'tz',\n    Uganda = 'ug',\n    Ukraine = 'ua',\n    Uruguay = 'uy',\n    UnitedStates = 'us',\n    Uzbekistan = 'uz',\n    VaticanCity = 'va',\n    SaintVincentAndTheGrenadines = 'vc',\n    Venezuela = 've',\n    Vietnam = 'vn',\n    Vanuatu = 'vu',\n    Samoa = 'ws',\n    Yemen = 'ye',\n    SouthAfrica = 'za',\n    Zambia = 'zm',\n    Zimbabwe = 'zw',\n}", "export enum ExecutionMethod {\n    GET = 'GET',\n    POST = 'POST',\n    PUT = 'PUT',\n    PATCH = 'PATCH',\n    DELETE = 'DELETE',\n    OPTIONS = 'OPTIONS',\n}", "export enum ImageGravity {\n    Center = 'center',\n    Topleft = 'top-left',\n    Top = 'top',\n    Topright = 'top-right',\n    Left = 'left',\n    Right = 'right',\n    Bottomleft = 'bottom-left',\n    Bottom = 'bottom',\n    Bottomright = 'bottom-right',\n}", "export enum ImageFormat {\n    Jpg = 'jpg',\n    Jpeg = 'jpeg',\n    Gif = 'gif',\n    Png = 'png',\n    Webp = 'webp',\n}"], "mappings": ";;;AAqEO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;IAAE,CAAE;EAAE;AAC1G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AACzF,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;MAAE,SAAU,GAAG;AAAE,eAAO,CAAC;MAAE;IAAE;AAC5F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;IAAE;AAC5G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAA,CAAE,GAAG,KAAI,CAAE;EAC5E,CAAK;AACL;AA0JO,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;ICxOa,gBAAA,SAAO;EAKhB,YAAY,QAAc;AACtB,SAAK,SAAS;;EAGlB,OAAO,QAAQ,MAAe,SAAS,IAAE;AACrC,QAAI,SAAkB,CAAA;AAEtB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,UAAI,WAAW,SAAS,SAAS,MAAM,MAAK,MAAM;AAClD,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,iBAAc,OAAA,OAAA,OAAA,OAAA,CAAA,GAAA,MAAM,GAAK,SAAQ,QAAQ,OAAO,QAAQ,CAAC;MAC5D,OAAM;AACH,eAAO,QAAQ,IAAI;MACtB;IACJ;AAED,WAAO;;;AApBJ,QAAU,aAAG,IAAE,OAAK;ICClB,cAAK;EAKhB,YACE,QACA,WACA,QAAmB;AAEnB,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,QAAI,WAAW,QAAW;AACxB,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,SAAS;MACf,OAAM;AACL,aAAK,SAAS,CAAC,MAAM;MACtB;IACF;;EAGH,WAAQ;AACN,WAAO,KAAK,UAAU;MACpB,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB,QAAQ,KAAK;IACd,CAAA;;;AAGI,MAAK,QAAG,CAAC,WAAmB,UACjC,IAAI,MAAM,SAAS,WAAW,KAAK,EAAE,SAAQ;AAExC,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAE3C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAE3C,MAAa,gBAAG,CAAC,WAAmB,UACzC,IAAI,MAAM,iBAAiB,WAAW,KAAK,EAAE,SAAQ;AAEhD,MAAW,cAAG,CAAC,WAAmB,UACvC,IAAI,MAAM,eAAe,WAAW,KAAK,EAAE,SAAQ;AAE9C,MAAgB,mBAAG,CAAC,WAAmB,UAC5C,IAAI,MAAM,oBAAoB,WAAW,KAAK,EAAE,SAAQ;AAEnD,MAAA,SAAS,CAAC,cACf,IAAI,MAAM,UAAU,SAAS,EAAE,SAAQ;AAElC,MAAA,YAAY,CAAC,cAClB,IAAI,MAAM,aAAa,SAAS,EAAE,SAAQ;AAErC,MAAO,UAAG,CAAC,WAAmB,OAAwB,QAC3D,IAAI,MAAM,WAAW,WAAW,CAAC,OAAO,GAAG,CAAmB,EAAE,SAAQ;AAEnE,MAAU,aAAG,CAAC,WAAmB,UACtC,IAAI,MAAM,cAAc,WAAW,KAAK,EAAE,SAAQ;AAE7C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAE3C,MAAA,SAAS,CAAC,eACf,IAAI,MAAM,UAAU,QAAW,UAAU,EAAE,SAAQ;AAE9C,MAAM,SAAG,CAAC,WAAmB,UAClC,IAAI,MAAM,UAAU,WAAW,KAAK,EAAE,SAAQ;AAEzC,MAAA,YAAY,CAAC,cAClB,IAAI,MAAM,aAAa,SAAS,EAAE,SAAQ;AAErC,MAAA,WAAW,CAAC,cACjB,IAAI,MAAM,YAAY,SAAS,EAAE,SAAQ;AAEpC,MAAA,cAAc,CAAC,eACpB,IAAI,MAAM,eAAe,QAAW,UAAU,EAAE,SAAQ;AAEnD,MAAA,eAAe,CAAC,eACrB,IAAI,MAAM,gBAAgB,QAAW,UAAU,EAAE,SAAQ;AAEpD,MAAA,QAAQ,CAAC,UACd,IAAI,MAAM,SAAS,QAAW,KAAK,EAAE,SAAQ;AAExC,MAAA,SAAS,CAAC,WACf,IAAI,MAAM,UAAU,QAAW,MAAM,EAAE,SAAQ;AAE1C,MAAQ,WAAG,CAAC,WAAmB,UACpC,IAAI,MAAM,YAAY,WAAW,KAAK,EAAE,SAAQ;AAE3C,MAAA,KAAK,CAAC,YACX,IAAI,MAAM,MAAM,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAQ;AAEzE,MAAA,MAAM,CAAC,YACZ,IAAI,MAAM,OAAO,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAQ;ACvBnF,IAAM,oBAAN,cAAgC,MAAK;EAIjC,YAAY,SAAiB,OAAe,GAAG,OAAe,IAAI,WAAmB,IAAE;AACnF,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;;AAEvB;AAED,IAAM,SAAN,MAAY;EAAZ,cAAA;AACI,SAAA,SAAS;MACL,UAAU;MACV,kBAAkB;MAClB,SAAS;MACT,KAAK;MACL,QAAQ;MACR,SAAS;;AAEb,SAAA,UAAmB;MACf,cAAc;MACd,kBAAkB;MAClB,kBAAkB;MAClB,iBAAiB;MACjB,8BAA8B;;AA2F1B,SAAA,WAAqB;MACzB,QAAQ;MACR,SAAS;MACT,KAAK;MACL,UAAU,oBAAI,IAAG;MACjB,eAAe,oBAAI,IAAG;MACtB,sBAAsB;MACtB,WAAW;MACX,mBAAmB;MACnB,aAAa;MACb,SAAS,MAAK;AACV,qBAAa,KAAK,SAAS,OAAO;AAClC,aAAK,SAAS,UAAU,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,WAAW,MAAK;AAC5C,eAAK,SAAS,aAAY;WAC3B,EAAE;;MAET,YAAY,MAAK;AACb,gBAAQ,MAAI;UACR,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX,KAAK,KAAK,SAAS,oBAAoB;AACnC,mBAAO;UACX;AACI,mBAAO;QACd;;MAEL,cAAc,MAAK;;AACf,YAAI,KAAK,SAAS,SAAS,OAAO,GAAG;AACjC,eAAK,SAAS,YAAY;AAC1B,WAAAA,MAAA,KAAK,SAAS,YAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,MAAK;AAC3B;QACH;AAED,cAAM,WAAW,IAAI,gBAAe;AACpC,iBAAS,IAAI,WAAW,KAAK,OAAO,OAAO;AAC3C,aAAK,SAAS,SAAS,QAAQ,aAAU;AACrC,mBAAS,OAAO,cAAc,OAAO;QACzC,CAAC;AAED,cAAM,MAAM,KAAK,OAAO,mBAAmB,eAAe,SAAS,SAAQ;AAE3E,YACI,QAAQ,KAAK,SAAS;QACtB,CAAC,KAAK,SAAS;UACf,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,cAAa,UAAU,MAC/C;AACE,cACI,KAAK,SAAS,YACd,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,cAAa,UAAU,SAC/C;AACE,iBAAK,SAAS,YAAY;AAC1B,iBAAK,SAAS,OAAO,MAAK;UAC7B;AAED,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,SAAS,IAAI,UAAU,GAAG;AACxC,eAAK,SAAS,OAAO,iBAAiB,WAAW,KAAK,SAAS,SAAS;AACxE,eAAK,SAAS,OAAO,iBAAiB,QAAQ,YAAS;AACnD,iBAAK,SAAS,oBAAoB;UACtC,CAAC;AACD,eAAK,SAAS,OAAO,iBAAiB,SAAS,WAAQ;;AACnD,gBACI,CAAC,KAAK,SAAS,eAEXC,OAAAD,MAAA,KAAK,cAAU,QAAAA,QAAA,SAAA,SAAAA,IAAA,iBAAa,QAAAC,QAAA,SAAA,SAAAA,IAAA,UAAS;cACbC,MAAA,KAAK,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAE,YAAY,MAAM,SAAS,MAExE;AACE,mBAAK,SAAS,YAAY;AAC1B;YACH;AAED,kBAAM,UAAU,KAAK,SAAS,WAAU;AACxC,oBAAQ,MAAM,6DAA6D,UAAU,GAAI,aAAa,MAAM,MAAM;AAElH,uBAAW,MAAK;AACZ,mBAAK,SAAS;AACd,mBAAK,SAAS,aAAY;eAC3B,OAAO;UACd,CAAC;QACJ;;MAEL,WAAW,CAAC,UAAS;;AACjB,YAAI;AACA,gBAAM,UAA4B,KAAK,MAAM,MAAM,IAAI;AACvD,eAAK,SAAS,cAAc;AAC5B,kBAAQ,QAAQ,MAAI;YAChB,KAAK;AACD,oBAAM,SAAS,KAAK,OAAMF,MAAA,OAAO,aAAa,QAAQ,gBAAgB,OAAK,QAAAA,QAAA,SAAAA,MAAA,IAAI;AAC/E,oBAAM,UAAU,WAAA,QAAA,WAAM,SAAA,SAAN,OAAS,aAAa,KAAK,OAAO,OAAO,EAAE;AAC3D,oBAAM,cAAyC,QAAQ;AAEvD,kBAAI,WAAW,CAAC,YAAY,MAAM;AAC9B,iBAAA,KAAA,KAAK,SAAS,YAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,KAAK,UAA2B;kBACvD,MAAM;kBACN,MAAM;oBACF;kBACH;gBACJ,CAAA,CAAC;cACL;AACD;YACJ,KAAK;AACD,kBAAI,OAAuC,QAAQ;AACnD,kBAAI,SAAA,QAAA,SAAI,SAAA,SAAJ,KAAM,UAAU;AAChB,sBAAM,eAAe,KAAK,SAAS,KAAK,aAAW,KAAK,SAAS,SAAS,IAAI,OAAO,CAAC;AACtF,oBAAI,CAAC;AAAc;AACnB,qBAAK,SAAS,cAAc,QAAQ,kBAAe;AAC/C,sBAAI,KAAK,SAAS,KAAK,aAAW,aAAa,SAAS,SAAS,OAAO,CAAC,GAAG;AACxE,+BAAW,MAAM,aAAa,SAAS,IAAI,CAAC;kBAC/C;gBACL,CAAC;cACJ;AACD;YACJ,KAAK;AACD,oBAAM,QAAQ;YAClB;AACI;UACP;QACJ,SAAQ,GAAG;AACR,kBAAQ,MAAM,CAAC;QAClB;;MAEL,SAAS,cAAW;AAChB,aAAK,SAAS,SAAS,QAAQ,aAAU;AACrC,cAAI,SAAS,SAAS,OAAO,GAAG;AAC5B,gBAAI,QAAQ,MAAM,KAAK,KAAK,SAAS,aAAa,EAAE,KAAK,CAAC,CAAC,MAAM,YAAY,MAAK;AAC9E,qBAAO,aAAa,SAAS,SAAS,OAAO;YACjD,CAAC;AAED,gBAAI,CAAC,OAAO;AACR,mBAAK,SAAS,SAAS,OAAO,OAAO;YACxC;UACJ;QACL,CAAC;;;;;;;;;;;;;EAtNT,YAAY,UAAgB;AACxB,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,mBAAmB,KAAK,OAAO,oBAAoB,KAAK,OAAO,SAAS,QAAQ,YAAY,QAAQ,EAAE,QAAQ,WAAW,OAAO;AAE5I,WAAO;;;;;;;;;EAUX,oBAAoB,kBAAwB;AACxC,SAAK,OAAO,mBAAmB;AAE/B,WAAO;;;;;;;;;;;EAYX,WAAW,OAAa;AACpB,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;;;;;;;;;;;EAYX,OAAO,OAAa;AAChB,SAAK,QAAQ,gBAAgB,IAAI;AACjC,SAAK,OAAO,MAAM;AAClB,WAAO;;;;;;;;;EAUX,UAAU,OAAa;AACnB,SAAK,QAAQ,mBAAmB,IAAI;AACpC,SAAK,OAAO,SAAS;AACrB,WAAO;;;;;;;;;;;EAYX,WAAW,OAAa;AACpB,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwKX,UAA6B,UAA6B,UAAqD;AAC3G,QAAI,eAAe,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI;AAC/D,iBAAa,QAAQ,aAAW,KAAK,SAAS,SAAS,IAAI,OAAO,CAAC;AAEnE,UAAM,UAAU,KAAK,SAAS;AAC9B,SAAK,SAAS,cAAc,IAAI,SAAS;MACrC,UAAU;MACV;IACH,CAAA;AAED,SAAK,SAAS,QAAO;AAErB,WAAO,MAAK;AACR,WAAK,SAAS,cAAc,OAAO,OAAO;AAC1C,WAAK,SAAS,QAAQ,YAAY;AAClC,WAAK,SAAS,QAAO;IACzB;;EAGE,KAAK,QAAgB,KAAU,UAAmB,CAAA,GAAI,SAAkB,CAAA,GAAE;;;AAC5E,eAAS,OAAO,YAAW;AAG3B,gBAAU,OAAO,OAAO,CAAA,GAAI,KAAK,SAAS,OAAO;AAEjD,UAAI,UAAuB;QACvB;QACA;QACA,aAAa;;AAGjB,UAAI,OAAO,WAAW,eAAe,OAAO,cAAc;AACtD,cAAM,iBAAiB,OAAO,aAAa,QAAQ,gBAAgB;AACnE,YAAI,gBAAgB;AAChB,kBAAQ,oBAAoB,IAAI;QACnC;MACJ;AAED,UAAI,WAAW,OAAO;AAClB,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,MAAM,CAAC,GAAG;AAChE,cAAI,aAAa,OAAO,KAAK,KAAK;QACrC;MACJ,OAAM;AACH,gBAAQ,QAAQ,cAAc,GAAC;UAC3B,KAAK;AACD,oBAAQ,OAAO,KAAK,UAAU,MAAM;AACpC;UAEJ,KAAK;AACD,gBAAI,WAAW,IAAI,SAAQ;AAE3B,uBAAW,OAAO,QAAQ;AACtB,kBAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AAC5B,uBAAO,GAAG,EAAE,QAAQ,CAAC,UAAc;AAC/B,2BAAS,OAAO,MAAM,MAAM,KAAK;gBACrC,CAAC;cACJ,OAAM;AACH,yBAAS,OAAO,KAAK,OAAO,GAAG,CAAC;cACnC;YACJ;AAED,oBAAQ,OAAO;AACf,mBAAO,QAAQ,cAAc;AAC7B;QACP;MACJ;AAED,UAAI;AACA,YAAI,OAAO;AACX,cAAM,WAAW,MAAM,MAAM,IAAI,SAAQ,GAAI,OAAO;AAEpD,aAAIA,MAAA,SAAS,QAAQ,IAAI,cAAc,OAAG,QAAAA,QAAA,SAAA,SAAAA,IAAA,SAAS,kBAAkB,GAAG;AACpE,iBAAO,MAAM,SAAS,KAAI;QAC7B,OAAM;AACH,iBAAO;YACH,SAAS,MAAM,SAAS,KAAI;;QAEnC;AAED,YAAI,OAAO,SAAS,QAAQ;AACxB,gBAAM,IAAI,kBAAkB,SAAI,QAAJ,SAAA,SAAA,SAAA,KAAM,SAAS,SAAS,QAAQ,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,MAAM,IAAI;QAC/E;AAED,cAAM,iBAAiB,SAAS,QAAQ,IAAI,oBAAoB;AAEhE,YAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB,gBAAgB;AACxE,iBAAO,QAAQ,KAAK,+HAA+H;AACnJ,iBAAO,aAAa,QAAQ,kBAAkB,cAAc;QAC/D;AAED,eAAO;MACV,SAAQ,GAAG;AACR,YAAI,aAAa,mBAAmB;AAChC,gBAAM;QACT;AACD,cAAM,IAAI,kBAA0B,EAAG,OAAO;MACjD;;EACJ;AACJ;ACjcK,IAAO,UAAP,cAAuB,QAAO;EAE/B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;EAWV,MAAG;;AACL,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;EAoBK,OAA+C,QAAgB,OAAe,UAAkB,MAAa;;AAC/G,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;EAmBK,YAAoD,OAAe,UAAgB;;AACrF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,eAAe,SAAkB;;AACnC,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,eAAe,YAAkB;;AACnC,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,mCAAmC,QAAQ,gBAAgB,UAAU;AACrF,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,YAAS;;AACX,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,SAAS,SAAkB;;AAC7B,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,UAAkD,KAAY;;AAChE,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,uBAAuB,MAAuB;;AAChD,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,uBAA+D,MAAyB,KAAW;;AACrG,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,uBAAuB,MAAyB,KAAW;;AAC7D,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,mBAAmB,QAA4B;;AACjD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;EAgBK,mBAAmB,aAAqB,KAAW;;AACrD,UAAI,OAAO,gBAAgB,aAAa;AACpC,cAAM,IAAI,kBAAkB,2CAA2C;MAC1E;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;EAUK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,sBAAmB;;AACrB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,WAAmD,MAAY;;AACjE,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,eAAuD,UAAkB,aAAoB;;AAC/F,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;EAgBK,YAAoD,OAAe,UAAgB;;AACrF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;EAUK,WAAQ;;AACV,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,YAAoD,OAA2B;;AACjF,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;EAmBK,eAAe,OAAe,KAAW;;AAC3C,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;EAsBK,eAAe,QAAgB,QAAgB,UAAgB;;AACjE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,eAAY;;AACd,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;EAgBK,yBAAsB;;AACxB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;EAiBK,2BAA2B,OAAe,UAAgB;;AAC5D,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,sBAAsB,QAAgB,QAAc;;AACtD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BD,oBAAoB,UAAyB,SAAkB,SAAkB,QAAiB;AAC9F,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AAED,UAAM,UAAU,sCAAsC,QAAQ,cAAc,QAAQ;AACpF,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,QAAI,OAAO,WAAW,gBAAe,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,WAAU;AACnD,aAAO,SAAS,OAAO,IAAI,SAAQ;IACtC,OAAM;AACH,aAAO;IACV;;;;;;;;;;;;;;EAeC,mBAAmB,QAAgB,QAAc;;AACnD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,cAAc,QAAgB,QAAc;;AAC9C,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,WAAW,WAAiB;;AAC9B,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AAED,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,cAAc,WAAiB;;AACjC,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AAED,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,cAAc,WAAiB;;AACjC,UAAI,OAAO,cAAc,aAAa;AAClC,cAAM,IAAI,kBAAkB,yCAAyC;MACxE;AAED,YAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,eAAY;;AACd,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,iBAAiB,UAAkB,YAAoB,YAAmB;;AAC5E,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,iBAAiB,UAAkB,YAAkB;;AACvD,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,mCAAmC,QAAQ,cAAc,QAAQ;AACjF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;EAUK,iBAAiB,UAAgB;;AACnC,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU,mCAAmC,QAAQ,cAAc,QAAQ;AACjF,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;EAsBK,iBAAiB,QAAgB,OAAe,QAAgB;;AAClE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BK,oBAAoB,QAAgB,OAAe,KAAc,QAAgB;;AACnF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BD,kBAAkB,UAAyB,SAAkB,SAAkB,QAAiB;AAC5F,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AAED,UAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,QAAI,OAAO,WAAW,gBAAe,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,WAAU;AACnD,aAAO,SAAS,OAAO,IAAI,SAAQ;IACtC,OAAM;AACH,aAAO;IACV;;;;;;;;;;;;;;;;;;;;;EAsBC,iBAAiB,QAAgB,OAAa;;AAChD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;EAyBK,mBAAmB,KAAW;;AAChC,UAAI,OAAO,QAAQ,aAAa;AAC5B,cAAM,IAAI,kBAAkB,mCAAmC;MAClE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,mBAAmB,QAAgB,QAAc;;AACnD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;EAiBK,0BAAuB;;AACzB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,wBAAwB,QAAgB,QAAc;;AACxD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ACphDK,IAAO,UAAP,cAAuB,QAAO;EAE/B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;EAwBhB,WAAW,MAAe,OAAgB,QAAiB,SAAgB;AACvE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AAED,UAAM,UAAU,2BAA2B,QAAQ,UAAU,IAAI;AACjE,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;;;;;;;EAuBX,cAAc,MAAkB,OAAgB,QAAiB,SAAgB;AAC7E,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AAED,UAAM,UAAU,+BAA+B,QAAQ,UAAU,IAAI;AACrE,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;EAcX,WAAW,KAAW;AAClB,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;IAClE;AAED,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;IACpB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;;;;;;;;EAwBX,QAAQ,MAAY,OAAgB,QAAiB,SAAgB;AACjE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AAED,UAAM,UAAU,wBAAwB,QAAQ,UAAU,IAAI;AAC9D,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;;;;;;;EAuBX,SAAS,KAAa,OAAgB,QAAe;AACjD,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;IAClE;AAED,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;IACpB;AAED,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BX,YAAY,MAAe,OAAgB,QAAiB,YAAmB;AAC3E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AAED,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;IAC3B;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;EAiBX,MAAM,MAAc,MAAe,QAAiB,UAAkB;AAClE,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;IACnE;AAED,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AAED,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;IACrB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;IACzB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;AAEd;AC7VK,IAAO,YAAP,cAAyB,QAAO;EAEjC,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;;EAeV,cAAgD,YAAoB,cAAsB,SAAkB;;AAC9G,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,YAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;EAkBK,eAAiD,YAAoB,cAAsB,YAAoB,MAA6C,aAAsB;;AACpL,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,YAAY,IAAI;MAC3B;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,YAA8C,YAAoB,cAAsB,YAAoB,SAAkB;;AAChI,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;EAgBK,eAAiD,YAAoB,cAAsB,YAAoB,MAAuD,aAAsB;;AAC9L,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,eAAe,YAAoB,cAAsB,YAAkB;;AAC7E,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ACjNK,IAAO,YAAP,cAAyB,QAAO;EAEjC,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;;EAeV,eAAe,YAAoB,SAAoB,QAAe;;AACxE,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;EAmBK,gBAAgB,YAAoB,MAAe,OAAiB,OAAgB,QAA0B,SAAgB;;AAChI,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,YAAM,UAAU,qCAAqC,QAAQ,gBAAgB,UAAU;AACvF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,aAAa,YAAoB,aAAmB;;AACtD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,IAAI,kBAAkB,0CAA0C;MACzE;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,cAAM,IAAI,kBAAkB,2CAA2C;MAC1E;AAED,YAAM,UAAU,mDAAmD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,iBAAiB,WAAW;AAC3I,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ACxHK,IAAO,UAAP,cAAuB,QAAO;EAE/B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;EAYV,MAAM,OAAa;;AACrB,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,iBAAiB;QACjB,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,SAAS,OAAa;;AACxB,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,iBAAiB;QACjB,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;AC9DK,IAAO,SAAP,cAAsB,QAAO;EAE9B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;;;EAgBV,MAAG;;AACL,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,YAAS;;AACX,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,gBAAa;;AACf,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,kBAAe;;AACjB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,sBAAmB;;AACrB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,iBAAc;;AAChB,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,gBAAa;;AACf,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ACnKK,IAAO,YAAP,cAAyB,QAAO;EAEjC,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;EAcV,iBAAiB,SAAiB,cAAsB,UAAgB;;AAC1E,UAAI,OAAO,YAAY,aAAa;AAChC,cAAM,IAAI,kBAAkB,uCAAuC;MACtE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU,0CAA0C,QAAQ,aAAa,OAAO;AACtF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,iBAAiB,aAAa;AACrC,gBAAQ,cAAc,IAAI;MAC7B;AAED,UAAI,OAAO,aAAa,aAAa;AACjC,gBAAQ,UAAU,IAAI;MACzB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,iBAAiB,SAAiB,cAAoB;;AACxD,UAAI,OAAO,YAAY,aAAa;AAChC,cAAM,IAAI,kBAAkB,uCAAuC;MACtE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,YAAM,UAAU,yDAAyD,QAAQ,aAAa,OAAO,EAAE,QAAQ,kBAAkB,YAAY;AAC7I,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ACzEK,IAAO,UAAP,cAAuB,QAAO;EAE/B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;;EAeV,UAAU,UAAkB,SAAoB,QAAe;;AACjE,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,YAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BK,WAAW,UAAkB,QAAgB,MAAY,aAAwB,aAAa,CAAC,aAAwB;EAAA,GAAO;;AAChI,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAG,EAAE,gBAAgB,OAAO;AACxB,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,OAAO,KAAK;AAElB,UAAI,QAAQ,QAAQ,YAAY;AAC5B,eAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;UACvC,gBAAgB;WACjB,OAAO;MACb;AAED,YAAM,aAA2C;QAC7C,gBAAgB;;AAGpB,UAAI,SAAS;AACb,UAAI,WAAW;AACf,UAAG,UAAU,YAAY;AACrB,YAAI;AACA,qBAAW,MAAM,KAAK,OAAO,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,UAAU,MAAM,MAAM,GAAG,UAAU;AAClH,mBAAS,SAAS,iBAAiB,QAAQ;QAC9C,SAAO,GAAG;QACV;MACJ;AAED,aAAO,SAAS,MAAM;AAClB,YAAI,MAAM,KAAK,IAAI,SAAS,QAAQ,aAAa,GAAG,OAAO,CAAC;AAE5D,mBAAW,eAAe,IAAI,WAAW,SAAS,MAAM,MAAM,MAAM;AACpE,YAAI,YAAY,SAAS,KAAK;AAC1B,qBAAW,eAAe,IAAI,SAAS;QAC1C;AAED,cAAM,QAAQ,KAAK,MAAM,QAAQ,MAAM,CAAC;AACxC,gBAAQ,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI;AAC7C,mBAAW,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK,YAAY,OAAO;AAElE,YAAI,YAAY;AACZ,qBAAW;YACP,KAAK,SAAS;YACd,UAAW,SAAS,OAAQ;YAC5B,cAAc;YACd,aAAa,SAAS;YACtB,gBAAgB,SAAS;UAC5B,CAAA;QACJ;AACD,kBAAU,QAAQ;MACrB;AACD,aAAO;KACV;EAAA;;;;;;;;;;;;EAaK,QAAQ,UAAkB,QAAc;;AAC1C,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,WAAW,UAAkB,QAAgB,MAAe,aAAsB;;AACpF,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,gBAAgB,aAAa;AACpC,gBAAQ,aAAa,IAAI;MAC5B;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,WAAW,UAAkB,QAAc;;AAC7C,UAAI,OAAO,aAAa,aAAa;AACjC,cAAM,IAAI,kBAAkB,wCAAwC;MACvE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,6CAA6C,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AACvH,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcD,gBAAgB,UAAkB,QAAc;AAC5C,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AAED,UAAM,UAAU,sDAAsD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAChI,UAAM,UAAmB,CAAA;AAEzB,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BX,eAAe,UAAkB,QAAgB,OAAgB,QAAiB,SAAwB,SAAkB,aAAsB,aAAsB,cAAuB,SAAkB,UAAmB,YAAqB,QAAoB;AACzQ,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AAED,UAAM,UAAU,qDAAqD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC/H,UAAM,UAAmB,CAAA;AAEzB,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;IACtB;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;IAC5B;AAED,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;IAC5B;AAED,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;IAC7B;AAED,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;IACxB;AAED,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;IACzB;AAED,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;IAC3B;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;IACvB;AAED,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;;;;;;;;;;;;;EAeX,YAAY,UAAkB,QAAc;AACxC,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;IACvE;AAED,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;IACrE;AAED,UAAM,UAAU,kDAAkD,QAAQ,cAAc,QAAQ,EAAE,QAAQ,YAAY,MAAM;AAC5H,UAAM,UAAmB,CAAA;AAEzB,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,YAAQ,SAAS,IAAI,KAAK,OAAO,OAAO;AAGxC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,OAAO,CAAC,GAAG;AACjE,UAAI,aAAa,OAAO,KAAK,KAAK;IACrC;AACD,WAAO;;AAEd;ACzZK,IAAO,QAAP,cAAqB,QAAO;EAE7B,YAAY,QAAc;AAEvB,UAAM,MAAM;;;;;;;;;;;;;EAcV,KAA6C,SAAoB,QAAe;;AAClF,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;EAeK,OAA+C,QAAgB,MAAc,OAAgB;;AAC/F,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU;AAChB,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;EAWK,IAA4C,QAAc;;AAC5D,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,WAAmD,QAAgB,MAAY;;AACjF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,cAAM,IAAI,kBAAkB,oCAAoC;MACnE;AAED,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;EAYK,OAAO,QAAc;;AACvB,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,kBAAkB,QAAQ,YAAY,MAAM;AAC5D,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,gBAAgB,QAAgB,SAAoB,QAAe;;AACrE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,YAAY,aAAa;AAChC,gBAAQ,SAAS,IAAI;MACxB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqCK,iBAAiB,QAAgB,OAAiB,OAAgB,QAAiB,OAAgB,KAAc,MAAa;;AAChI,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU,8BAA8B,QAAQ,YAAY,MAAM;AACxE,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,UAAI,OAAO,QAAQ,aAAa;AAC5B,gBAAQ,KAAK,IAAI;MACpB;AAED,UAAI,OAAO,SAAS,aAAa;AAC7B,gBAAQ,MAAM,IAAI;MACrB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK;QACvC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,cAAc,QAAgB,cAAoB;;AACpD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;EAgBK,iBAAiB,QAAgB,cAAsB,OAAe;;AACxE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,iBAAiB,QAAgB,cAAoB;;AACvD,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,YAAM,UAAU,6CAA6C,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AAC/H,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,UAAU,KAAK;QACzC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;;;;;;;EAoBK,uBAAuB,QAAgB,cAAsB,QAAgB,QAAc;;AAC7F,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,iBAAiB,aAAa;AACrC,cAAM,IAAI,kBAAkB,4CAA4C;MAC3E;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,oDAAoD,QAAQ,YAAY,MAAM,EAAE,QAAQ,kBAAkB,YAAY;AACtI,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,UAAI,OAAO,WAAW,aAAa;AAC/B,gBAAQ,QAAQ,IAAI;MACvB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;QACxC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;EAaK,SAAiD,QAAc;;AACjE,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,YAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,YAAM,UAAmB,CAAA;AAEzB,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;;;;;;;;;;;;;EAcK,YAAoD,QAAgB,OAAa;;AACnF,UAAI,OAAO,WAAW,aAAa;AAC/B,cAAM,IAAI,kBAAkB,sCAAsC;MACrE;AAED,UAAI,OAAO,UAAU,aAAa;AAC9B,cAAM,IAAI,kBAAkB,qCAAqC;MACpE;AAED,YAAM,UAAU,wBAAwB,QAAQ,YAAY,MAAM;AAClE,YAAM,UAAmB,CAAA;AAEzB,UAAI,OAAO,UAAU,aAAa;AAC9B,gBAAQ,OAAO,IAAI;MACtB;AAED,YAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AACzD,aAAO,MAAM,KAAK,OAAO,KAAK,OAAO,KAAK;QACtC,gBAAgB;SACjB,OAAO;KACb;EAAA;AACJ;ICneY,mBAAU;;AAEZ,WAAA,OAAO,CAAC,SAAwB;AACnC,SAAO,SAAS,IAAI;AACxB;AAEO,WAAA,QAAQ,CAAC,SAAwB;AACpC,SAAO,UAAU,IAAI;AACzB;AAEO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;AAEO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;AAEO,WAAA,SAAS,CAAC,SAAwB;AACrC,SAAO,WAAW,IAAI;AAC1B;ICjBS,aAAI;;;;;;;;EASN,OAAO,MAAG;AACb,WAAO;;;;;;;;;;;;EAaJ,OAAO,KAAK,IAAY,SAAiB,IAAE;AAC9C,QAAI,WAAW,IAAI;AACf,aAAO,QAAQ,EAAE;IACpB;AACD,WAAO,QAAQ,EAAE,IAAI,MAAM;;;;;;;;;;;EAYxB,OAAO,MAAM,SAAiB,IAAE;AACnC,QAAI,WAAW,IAAI;AACf,aAAO;IACV;AACD,WAAO,SAAS,MAAM;;;;;;;;;EAUnB,OAAO,SAAM;AAChB,WAAO;;;;;;;;;;;;EAaJ,OAAO,KAAK,IAAY,OAAe,IAAE;AAC5C,QAAI,SAAS,IAAI;AACb,aAAO,QAAQ,EAAE;IACpB;AACD,WAAO,QAAQ,EAAE,IAAI,IAAI;;;;;;;;;;;EAYtB,OAAO,OAAO,IAAU;AAC3B,WAAO,UAAU,EAAE;;;;;;;;EAShB,OAAO,MAAM,MAAY;AAC5B,WAAO,SAAS,IAAI;;AAE3B;;;ICnGY,WAAA,IAAE;EAaJ,OAAO,OAAO,IAAU;AAC3B,WAAO;;EAGJ,OAAO,OAAO,UAAkB,GAAC;AAEpC,UAAM,SAAS,uBAAA,KAAE,IAAA,KAAA,gBAAA,EAAF,KAAA,GAAE;AACjB,QAAI,gBAAgB;AACpB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,YAAM,iBAAiB,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,EAAE,SAAS,EAAE;AACjE,uBAAiB;IACpB;AACD,WAAO,SAAS;;AAEvB;;AAvBO,QAAM,MAAM,oBAAI,KAAI;AACpB,QAAM,MAAM,KAAK,MAAM,IAAI,QAAO,IAAK,GAAI;AAC3C,QAAM,OAAO,IAAI,gBAAe;AAGhC,QAAM,eAAe,IAAI,SAAS,EAAE,IAAI,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACzE,SAAO;AACX;ICXQ;CAAZ,SAAYG,oBAAiB;AACzB,EAAAA,mBAAA,MAAA,IAAA;AACJ,GAFY,sBAAA,oBAEX,CAAA,EAAA;ICFW;CAAZ,SAAYC,uBAAoB;AAC5B,EAAAA,sBAAA,OAAA,IAAA;AACA,EAAAA,sBAAA,OAAA,IAAA;AACA,EAAAA,sBAAA,MAAA,IAAA;AACA,EAAAA,sBAAA,cAAA,IAAA;AACJ,GALY,yBAAA,uBAKX,CAAA,EAAA;ICLW;CAAZ,SAAYC,gBAAa;AACrB,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,KAAA,IAAA;AACA,EAAAA,eAAA,aAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,eAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,YAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,YAAA,IAAA;AACA,EAAAA,eAAA,eAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,WAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACJ,GAxCY,kBAAA,gBAwCX,CAAA,EAAA;ICxCW;CAAZ,SAAYC,UAAO;AACf,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,iBAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,UAAA,IAAA;AACA,EAAAA,SAAA,gBAAA,IAAA;AACA,EAAAA,SAAA,QAAA,IAAA;AACA,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,eAAA,IAAA;AACA,EAAAA,SAAA,kBAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACA,EAAAA,SAAA,OAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACJ,GAfY,YAAA,UAeX,CAAA,EAAA;ICfW;CAAZ,SAAYC,aAAU;AAClB,EAAAA,YAAA,iBAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,YAAA,IAAA;AACA,EAAAA,YAAA,UAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,YAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACA,EAAAA,YAAA,iBAAA,IAAA;AACA,EAAAA,YAAA,eAAA,IAAA;AACA,EAAAA,YAAA,MAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACJ,GAjBY,eAAA,aAiBX,CAAA,EAAA;ICjBW;CAAZ,SAAYC,OAAI;AACZ,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,sBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,kBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,wBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,6BAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,kBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,uBAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,eAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,iBAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,iBAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,gBAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,oBAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,MAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,mBAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,cAAA,IAAA;AACA,EAAAA,MAAA,YAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,8BAAA,IAAA;AACA,EAAAA,MAAA,WAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,SAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,OAAA,IAAA;AACA,EAAAA,MAAA,aAAA,IAAA;AACA,EAAAA,MAAA,QAAA,IAAA;AACA,EAAAA,MAAA,UAAA,IAAA;AACJ,GApMY,SAAA,OAoMX,CAAA,EAAA;ICpMW;CAAZ,SAAYC,kBAAe;AACvB,EAAAA,iBAAA,KAAA,IAAA;AACA,EAAAA,iBAAA,MAAA,IAAA;AACA,EAAAA,iBAAA,KAAA,IAAA;AACA,EAAAA,iBAAA,OAAA,IAAA;AACA,EAAAA,iBAAA,QAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACJ,GAPY,oBAAA,kBAOX,CAAA,EAAA;ICPW;CAAZ,SAAYC,eAAY;AACpB,EAAAA,cAAA,QAAA,IAAA;AACA,EAAAA,cAAA,SAAA,IAAA;AACA,EAAAA,cAAA,KAAA,IAAA;AACA,EAAAA,cAAA,UAAA,IAAA;AACA,EAAAA,cAAA,MAAA,IAAA;AACA,EAAAA,cAAA,OAAA,IAAA;AACA,EAAAA,cAAA,YAAA,IAAA;AACA,EAAAA,cAAA,QAAA,IAAA;AACA,EAAAA,cAAA,aAAA,IAAA;AACJ,GAVY,iBAAA,eAUX,CAAA,EAAA;ICVW;CAAZ,SAAYC,cAAW;AACnB,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACA,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,KAAA,IAAA;AACA,EAAAA,aAAA,MAAA,IAAA;AACJ,GANY,gBAAA,cAMX,CAAA,EAAA;", "names": ["_a", "_b", "_c", "AuthenticatorType", "AuthenticationFactor", "OAuth<PERSON><PERSON><PERSON>", "Browser", "CreditCard", "Flag", "ExecutionMethod", "ImageGravity", "ImageFormat"]}