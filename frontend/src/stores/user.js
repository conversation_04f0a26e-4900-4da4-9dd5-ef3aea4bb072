import { defineStore } from 'pinia';
import api from '../config/api.js';

export const useUserStore = defineStore('user', {
  state: () => ({
    profile: null,
    stats: null,
    isLoading: false
  }),

  getters: {
    credits: (state) => state.profile?.credits || 0,
    storageUsed: (state) => state.profile?.storageUsed || 0,
    storageLimit: (state) => state.profile?.storageLimit || 52428800, // 50MB default
    storageUsedMB: (state) => ((state.profile?.storageUsed || 0) / (1024 * 1024)).toFixed(2),
    storageLimitMB: (state) => ((state.profile?.storageLimit || 52428800) / (1024 * 1024)).toFixed(0),
    storageUsagePercent: (state) => {
      if (!state.profile) return 0;
      return Math.round((state.profile.storageUsed / state.profile.storageLimit) * 100);
    },
    isAdmin: (state) => state.profile?.isAdmin || false,
    embedFooter: (state) => state.profile?.embedFooter || "Powered by AveImgCloud ⭐",
    embedColor: (state) => state.profile?.embedColor || "#7C3AED"
  },

  actions: {
    // Get user profile and stats
    async fetchProfile() {
      this.isLoading = true;
      try {
        const response = await api.get('/user/profile');
        if (response.data.success) {
          this.profile = response.data.data.profile;
          this.stats = response.data.data;
          return this.profile;
        } else {
          throw new Error(response.data.error || 'Failed to fetch profile');
        }
      } catch (error) {
        console.error('Fetch profile error:', error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // Update profile settings
    async updateProfile(updates) {
      this.isLoading = true;
      try {
        const response = await api.put('/user/profile', updates);
        if (response.data.success) {
          // Update local state
          this.profile = { ...this.profile, ...response.data.data };
          return { success: true, message: 'Profile updated successfully!' };
        } else {
          throw new Error(response.data.error || 'Update failed');
        }
      } catch (error) {
        console.error('Update profile error:', error);
        const message = error.response?.data?.error || error.message || 'Update failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Get credits info
    async fetchCredits() {
      try {
        const response = await api.get('/user/credits');
        if (response.data.success) {
          const data = response.data.data;
          // Update profile with latest credit info
          this.profile = { ...this.profile, ...data };
          return data;
        } else {
          throw new Error(response.data.error || 'Failed to fetch credits');
        }
      } catch (error) {
        console.error('Fetch credits error:', error);
        throw error;
      }
    },

    // Purchase credits
    async purchaseCredits(amount, packageType = 'custom') {
      this.isLoading = true;
      try {
        const response = await api.post('/user/purchase/credits', {
          amount,
          packageType
        });
        
        if (response.data.success) {
          // Update credits in state
          this.profile.credits = response.data.data.credits;
          return { success: true, message: response.data.message };
        } else {
          throw new Error(response.data.error || 'Purchase failed');
        }
      } catch (error) {
        console.error('Purchase credits error:', error);
        const message = error.response?.data?.error || error.message || 'Purchase failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Purchase storage
    async purchaseStorage(additionalStorageMB, packageType = 'custom') {
      this.isLoading = true;
      try {
        const response = await api.post('/user/purchase/storage', {
          additionalStorageMB,
          packageType
        });
        
        if (response.data.success) {
          // Update storage limit in state
          this.profile.storageLimit = response.data.data.storageLimit;
          return { success: true, message: response.data.message };
        } else {
          throw new Error(response.data.error || 'Purchase failed');
        }
      } catch (error) {
        console.error('Purchase storage error:', error);
        const message = error.response?.data?.error || error.message || 'Purchase failed';
        return { success: false, error: message };
      } finally {
        this.isLoading = false;
      }
    },

    // Update storage usage (called after upload/delete)
    updateStorageUsage(sizeChange) {
      if (this.profile) {
        this.profile.storageUsed = Math.max(0, this.profile.storageUsed + sizeChange);
      }
    },

    // Check if user has enough storage
    hasStorageSpace(requiredSize) {
      if (!this.profile) return false;
      return (this.profile.storageUsed + requiredSize) <= this.profile.storageLimit;
    },

    // Check if user has enough credits
    hasCredits(requiredCredits) {
      if (!this.profile) return false;
      return this.profile.credits >= requiredCredits;
    },

    // Clear user data
    clearProfile() {
      this.profile = null;
      this.stats = null;
    }
  }
});
