type QueryTypesSingle = string | number | boolean;
type QueryTypesList = string[] | number[] | boolean[] | Query[];
type QueryTypes = QueryTypesSingle | QueryTypesList;
type AttributesTypes = string | string[];
declare class Query {
    method: string;
    attribute: AttributesTypes | undefined;
    values: QueryTypesList | undefined;
    constructor(method: string, attribute?: AttributesTypes, values?: QueryTypes);
    toString(): string;
    static equal: (attribute: string, value: QueryTypes) => string;
    static notEqual: (attribute: string, value: QueryTypes) => string;
    static lessThan: (attribute: string, value: QueryTypes) => string;
    static lessThanEqual: (attribute: string, value: QueryTypes) => string;
    static greaterThan: (attribute: string, value: QueryTypes) => string;
    static greaterThanEqual: (attribute: string, value: QueryTypes) => string;
    static isNull: (attribute: string) => string;
    static isNotNull: (attribute: string) => string;
    static between: (attribute: string, start: string | number, end: string | number) => string;
    static startsWith: (attribute: string, value: string) => string;
    static endsWith: (attribute: string, value: string) => string;
    static select: (attributes: string[]) => string;
    static search: (attribute: string, value: string) => string;
    static orderDesc: (attribute: string) => string;
    static orderAsc: (attribute: string) => string;
    static cursorAfter: (documentId: string) => string;
    static cursorBefore: (documentId: string) => string;
    static limit: (limit: number) => string;
    static offset: (offset: number) => string;
    static contains: (attribute: string, value: string | string[]) => string;
    static or: (queries: string[]) => string;
    static and: (queries: string[]) => string;
}

export { Query, QueryTypes, QueryTypesList };
