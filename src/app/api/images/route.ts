import { NextRequest, NextResponse } from 'next/server';
import { databases, config } from '@/lib/appwrite';
import { Query } from 'appwrite';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Get images from database
    const response = await databases.listDocuments(
      config.appwrite.databaseId,
      config.appwrite.collections.images,
      [
        Query.orderDesc('uploaded_at'),
        Query.limit(limit),
        Query.offset(offset)
      ]
    );

    const images = response.documents.map(doc => ({
      id: doc.$id,
      name: doc.name,
      url: doc.url,
      size: doc.size,
      file_type: doc.file_type,
      uploaded_at: doc.uploaded_at,
      user_id: doc.user_id
    }));

    return NextResponse.json({
      success: true,
      images,
      total: response.total,
      page,
      limit
    });

  } catch (error: any) {
    console.error('Fetch images error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
