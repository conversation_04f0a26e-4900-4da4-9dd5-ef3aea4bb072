{"version": 3, "sources": ["../../src/enums/password-hash.ts"], "names": ["PasswordHash"], "mappings": ";AAAO,IAAK,eAAL,kBAAKA,kBAAL;AACH,EAAAA,cAAA,UAAO;AACP,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,eAAY;AACZ,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,aAAU;AAXF,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum PasswordHash {\n    Sha1 = 'sha1',\n    Sha224 = 'sha224',\n    Sha256 = 'sha256',\n    Sha384 = 'sha384',\n    Sha512224 = 'sha512/224',\n    Sha512256 = 'sha512/256',\n    Sha512 = 'sha512',\n    Sha3224 = 'sha3-224',\n    Sha3256 = 'sha3-256',\n    Sha3384 = 'sha3-384',\n    Sha3512 = 'sha3-512',\n}"]}