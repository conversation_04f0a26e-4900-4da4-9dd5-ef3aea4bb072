{"version": 3, "sources": ["../../src/enums/name.ts"], "names": ["Name"], "mappings": "AAAO,IAAK,OAAL,kBAAKA,UAAL;AACH,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,eAAY;AACZ,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,aAAU;AACV,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,gBAAa;AACb,EAAAA,MAAA,oBAAiB;AACjB,EAAAA,MAAA,cAAW;AACX,EAAAA,MAAA,iBAAc;AACd,EAAAA,MAAA,kBAAe;AAZP,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum Name {\n    V1database = 'v1-database',\n    V1deletes = 'v1-deletes',\n    V1audits = 'v1-audits',\n    V1mails = 'v1-mails',\n    V1functions = 'v1-functions',\n    V1usage = 'v1-usage',\n    V1usagedump = 'v1-usage-dump',\n    Webhooksv1 = 'webhooksv1',\n    V1certificates = 'v1-certificates',\n    V1builds = 'v1-builds',\n    V1messaging = 'v1-messaging',\n    V1migrations = 'v1-migrations',\n}"]}