# AveImgCloud Database Setup Guide

This guide will help you set up the required Appwrite collections for the AveImgCloud project.

## Prerequisites

1. Appwrite instance running (cloud or self-hosted)
2. Admin access to Appwrite Console
3. Project already created in Appwrite

## Database Collections Setup

### 1. Users Profile Collection

**Collection ID:** `users_profile`

**Attributes:**
- `userId` (String, Required) - References Appwrite user ID
- `credits` (Integer, Required, Default: 500) - User's available credits
- `storageUsed` (Integer, Required, Default: 0) - Storage used in bytes
- `storageLimit` (Integer, Required, Default: 52428800) - Storage limit in bytes (50MB)
- `isAdmin` (<PERSON><PERSON>an, Required, Default: false) - Admin status
- `canCustomizeFooter` (Boolean, Required, Default: false) - Footer customization permission
- `embedFooter` (String, Required, Default: "Powered by <PERSON>hu<PERSON> ⭐") - Custom footer text
- `embedColor` (String, Required, Default: "#7C3AED") - Embed color
- `createdAt` (DateTime, Required) - Creation timestamp
- `updatedAt` (DateTime, Required) - Last update timestamp

**Indexes:**
- `userId` (Key: userId, Type: key, Attributes: [userId])

**Permissions:**
- Read: `users`
- Create: `users`
- Update: `users`
- Delete: `users`

### 2. Images Collection

**Collection ID:** `images`

**Attributes:**
- `file_id` (String, Required) - Appwrite file ID
- `user_id` (String, Required) - User who uploaded the image
- `uuid` (String, Required) - Unique identifier for URL
- `image_url` (String, Required) - Image URL path (/i/uuid)
- `filename` (String, Required) - Original filename
- `size` (Integer, Required) - File size in bytes
- `mimetype` (String, Required) - MIME type
- `embed_title` (String, Required) - Title for embed
- `embed_description` (String, Optional) - Description for embed
- `embed_color` (String, Required, Default: "#7c3aed") - Embed color
- `embed_footer` (String, Optional) - Custom footer text
- `sharex_enabled` (Boolean, Required, Default: false) - ShareX upload flag
- `views` (Integer, Required, Default: 0) - View count
- `uploaded_at` (DateTime, Required) - Upload timestamp

**Indexes:**
- `user_id` (Key: user_id, Type: key, Attributes: [user_id])
- `uuid` (Key: uuid, Type: unique, Attributes: [uuid])
- `uploaded_at` (Key: uploaded_at, Type: key, Attributes: [uploaded_at])

**Permissions:**
- Read: `any` (for public image access)
- Create: `users`
- Update: `users`
- Delete: `users`

### 3. Storage Plans Collection

**Collection ID:** `storage_plans`

**Attributes:**
- `name` (String, Required) - Plan name
- `description` (String, Optional) - Plan description
- `storageAmountMB` (Integer, Required) - Storage amount in MB
- `creditCost` (Integer, Required) - Cost in credits
- `includesFooterCustomization` (Boolean, Required, Default: false) - Footer customization included
- `isActive` (Boolean, Required, Default: true) - Plan availability
- `createdAt` (DateTime, Required) - Creation timestamp
- `updatedAt` (DateTime, Required) - Last update timestamp

**Indexes:**
- `isActive` (Key: isActive, Type: key, Attributes: [isActive])
- `createdAt` (Key: createdAt, Type: key, Attributes: [createdAt])

**Permissions:**
- Read: `any` (for public plan viewing)
- Create: `role:admin`
- Update: `role:admin`
- Delete: `role:admin`

### 4. Transactions Collection

**Collection ID:** `transactions`

**Attributes:**
- `userId` (String, Required) - User who made the transaction
- `type` (String, Required) - Transaction type (e.g., "storage_purchase")
- `planId` (String, Required) - Storage plan ID
- `planName` (String, Required) - Plan name at time of purchase
- `creditCost` (Integer, Required) - Credits spent
- `storageAmountMB` (Integer, Required) - Storage amount purchased
- `includesFooterCustomization` (Boolean, Required) - Footer customization included
- `status` (String, Required, Default: "completed") - Transaction status
- `createdAt` (DateTime, Required) - Transaction timestamp

**Indexes:**
- `userId` (Key: userId, Type: key, Attributes: [userId])
- `createdAt` (Key: createdAt, Type: key, Attributes: [createdAt])
- `type` (Key: type, Type: key, Attributes: [type])

**Permissions:**
- Read: `users` (users can read their own transactions)
- Create: `users`
- Update: `role:admin`
- Delete: `role:admin`

## Setup Instructions

### Step 1: Create Database
1. Go to your Appwrite Console
2. Navigate to Databases
3. Create a new database with ID: `684c947d00139a00f671` (or update the ID in your .env file)

### Step 2: Create Collections
For each collection above:

1. Click "Create Collection"
2. Set the Collection ID as specified
3. Add all attributes with the exact names and types listed
4. Create the indexes as specified
5. Set the permissions as listed

### Step 3: Create Sample Storage Plans
After setting up the collections, create some sample storage plans:

```json
[
  {
    "name": "Basic Plan",
    "description": "Perfect for casual users",
    "storageAmountMB": 500,
    "creditCost": 100,
    "includesFooterCustomization": false,
    "isActive": true
  },
  {
    "name": "Pro Plan",
    "description": "Great for regular uploaders",
    "storageAmountMB": 2048,
    "creditCost": 300,
    "includesFooterCustomization": true,
    "isActive": true
  },
  {
    "name": "Premium Plan",
    "description": "For power users",
    "storageAmountMB": 10240,
    "creditCost": 1000,
    "includesFooterCustomization": true,
    "isActive": true
  }
]
```

### Step 4: Set Admin User
1. Create a user account with the email specified in `ADMIN_EMAIL` in your .env file
2. In the users_profile collection, set `isAdmin: true` for this user

### Step 5: Update Environment Variables
Make sure your `.env` file has the correct collection IDs:

```env
APPWRITE_DATABASE_ID=684c947d00139a00f671
APPWRITE_USERS_COLLECTION_ID=users_profile
APPWRITE_IMAGES_COLLECTION_ID=images
APPWRITE_TRANSACTIONS_COLLECTION_ID=transactions
APPWRITE_STORAGE_PLANS_COLLECTION_ID=storage_plans
```

## Verification

After setup, verify everything works by:

1. Starting the backend server
2. Registering a new user
3. Checking that a user profile is created automatically
4. Testing image upload functionality
5. Accessing the admin panel (if you're an admin user)
6. Testing storage plan purchases

## Troubleshooting

**Common Issues:**

1. **Permission Errors**: Make sure collection permissions are set correctly
2. **Missing Attributes**: Double-check all attribute names and types
3. **Index Errors**: Ensure indexes are created with correct attribute names
4. **Admin Access**: Verify the admin user has `isAdmin: true` in users_profile collection

**Database Migration:**
If you need to update existing collections, you can:
1. Export existing data
2. Update collection schema
3. Re-import data with new structure

For any issues, check the Appwrite console logs and your application logs for detailed error messages.
