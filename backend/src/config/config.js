import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server Configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:5173',

  // Appwrite Configuration
  appwrite: {
    endpoint: process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1',
    projectId: process.env.APPWRITE_PROJECT_ID,
    apiKey: process.env.APPWRITE_API_KEY,
    bucketId: process.env.APPWRITE_BUCKET_ID,
    databaseId: process.env.APPWRITE_DATABASE_ID,
    
    // Collections
    collections: {
      images: process.env.APPWRITE_IMAGES_COLLECTION_ID || 'images',
      users: process.env.APPWRITE_USERS_COLLECTION_ID || 'users',
      apiKeys: process.env.APPWRITE_API_KEYS_COLLECTION_ID || 'api_keys'
    }
  },

  // File Upload Configuration
  upload: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'image/bmp',
      'image/tiff'
    ]
  },

  // Rate Limiting
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // requests per window
    upload: {
      windowMs: 60 * 1000, // 1 minute
      max: 10 // uploads per minute
    }
  },

  // JWT Configuration (if needed for custom sessions)
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret-here',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  }
};

export default config;
