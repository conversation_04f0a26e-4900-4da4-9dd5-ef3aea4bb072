import express from 'express';
import {
  getAdminOverview,
  getAllUsers,
  updateUserByAdmin,
  deleteUserByAdmin,
  toggleAdminStatus,
  addCreditsToUser,
  updateUserStorageLimit,
  getAllStoragePlans,
  createStoragePlan,
  updateStoragePlan,
  deleteStoragePlan,
  getStorageStats,
  getAllTransactions
} from '../controllers/adminController.js';
import { authenticateUser, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// All admin routes require authentication and admin privileges
router.use(authenticateUser);
router.use(requireAdmin);

// Admin dashboard overview
router.get('/overview', getAdminOverview);

// User management
router.get('/users', getAllUsers);
router.put('/users/:userId', updateUserByAdmin);
router.delete('/users/:userId', deleteUserByAdmin);

// Admin privileges
router.put('/users/:userId/admin', toggleAdminStatus);

// Credits management
router.post('/users/:userId/credits', addCreditsToUser);

// Storage management
router.put('/users/:userId/storage', updateUserStorageLimit);

// Storage plans management
router.get('/storage/plans', getAllStoragePlans);
router.post('/storage/plans', createStoragePlan);
router.put('/storage/plans/:planId', updateStoragePlan);
router.delete('/storage/plans/:planId', deleteStoragePlan);

// Storage statistics
router.get('/storage/stats', getStorageStats);

// Transactions
router.get('/transactions', getAllTransactions);

export default router;
