import imageService from '../services/imageService.js';
import { UserService } from '../services/userService.js';
import multer from 'multer';
import path from 'path';

// Configure multer for file upload
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'));
        }
    }
}).single('image');

class ImageController {
    // Upload image
    async uploadImage(req, res) {
        try {
            upload(req, res, async (err) => {
                if (err) {
                    return res.status(400).json({
                        success: false,
                        error: err.message
                    });
                }

                if (!req.file) {
                    return res.status(400).json({
                        success: false,
                        error: 'No file uploaded'
                    });
                }

                const userId = req.user.$id;
                const customSettings = {
                    title: req.body.title || req.file.originalname,
                    description: req.body.description || '',
                    color: req.body.color || '#7c3aed',
                    footer: req.body.footer || '',
                    sharex_enabled: req.body.sharex_enabled === 'true'
                };

                const result = await imageService.uploadImage(req.file, userId, customSettings);
                
                res.json(result);
            });
        } catch (error) {
            console.error('Upload error:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Failed to upload image'
            });
        }
    }

    // ShareX compatible upload
    async shareXUpload(req, res) {
        try {
            upload(req, res, async (err) => {
                if (err) {
                    return res.status(400).send('Upload error: ' + err.message);
                }

                if (!req.file) {
                    return res.status(400).send('No file uploaded');
                }

                const userId = req.user ? req.user.$id : null;
                const customSettings = {
                    title: req.file.originalname,
                    description: 'Uploaded via ShareX',
                    color: '#7c3aed',
                    footer: 'ShareX Upload',
                    sharex_enabled: true
                };

                const result = await imageService.uploadImage(req.file, userId, customSettings);

                // ShareX expects the direct image URL
                const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
                const imageUrl = `${baseUrl}${result.image.image_url}`;
                res.send(imageUrl);
            });
        } catch (error) {
            console.error('ShareX upload error:', error);
            res.status(500).send('Upload failed');
        }
    }

    // Get user's images
    async getUserImages(req, res) {
        try {
            const userId = req.user.$id;
            const limit = parseInt(req.query.limit) || 20;
            const offset = parseInt(req.query.offset) || 0;

            const result = await imageService.getUserImages(userId, limit, offset);
            res.json(result);
        } catch (error) {
            console.error('Get user images error:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Failed to get images'
            });
        }
    }

    // Get image by ID
    async getImageById(req, res) {
        try {
            const { imageId } = req.params;
            const result = await imageService.getImageById(imageId);
            res.json(result);
        } catch (error) {
            console.error('Get image error:', error);
            res.status(404).json({
                success: false,
                error: 'Image not found'
            });
        }
    }

    // Delete image
    async deleteImage(req, res) {
        try {
            const { imageId } = req.params;
            const userId = req.user.$id;

            const result = await imageService.deleteImage(imageId, userId);
            res.json(result);
        } catch (error) {
            console.error('Delete image error:', error);
            res.status(error.message === 'Unauthorized' ? 403 : 500).json({
                success: false,
                error: error.message || 'Failed to delete image'
            });
        }
    }

    // Update image settings
    async updateImageSettings(req, res) {
        try {
            const { imageId } = req.params;
            const userId = req.user.$id;
            const settings = req.body;

            const result = await imageService.updateImageSettings(imageId, userId, settings);
            res.json(result);
        } catch (error) {
            console.error('Update image settings error:', error);
            res.status(error.message === 'Unauthorized' ? 403 : 500).json({
                success: false,
                error: error.message || 'Failed to update image settings'
            });
        }
    }

    // Get image embed data by UUID (for embed page)
    async getImageEmbed(req, res) {
        try {
            const { uuid } = req.params;
            const { width, height, quality, format } = req.query;

            const image = await imageService.getImageWithParams(uuid, {
                width,
                height,
                quality,
                format
            });

            if (!image) {
                return res.status(404).json({
                    success: false,
                    error: 'Image not found'
                });
            }

            // Get user profile for footer customization
            let userProfile = null;
            try {
                userProfile = await UserService.getUserProfile(image.user_id);
            } catch (error) {
                console.warn('Could not get user profile for image:', error);
            }

            // Determine footer text
            let footerText = "Powered by Avehub ⭐"; // Mandatory footer
            if (userProfile && userProfile.canCustomizeFooter && image.embed_footer) {
                footerText += ` • ${image.embed_footer}`;
            }

            // Return embed-specific data
            res.json({
                success: true,
                embed: {
                    title: image.embed_title || image.filename,
                    description: image.embed_description || '',
                    color: image.embed_color || '#7C3AED',
                    footer: footerText,
                    image_url: image.file_url,
                    filename: image.filename,
                    size: image.size,
                    views: image.views,
                    uploaded_at: image.uploaded_at,
                    uuid: image.uuid
                }
            });
        } catch (error) {
            console.error('Get image embed error:', error);
            res.status(404).json({
                success: false,
                error: 'Image not found'
            });
        }
    }

    // Serve image directly (for /i/uuid route)
    async serveImage(req, res) {
        try {
            const { uuid } = req.params;
            const { width, height, quality, format } = req.query;

            const image = await imageService.getImageWithParams(uuid, {
                width,
                height,
                quality,
                format
            });

            if (!image) {
                return res.status(404).send('Image not found');
            }

            // Redirect to the actual image URL with parameters
            res.redirect(image.file_url);
        } catch (error) {
            console.error('Serve image error:', error);
            res.status(404).send('Image not found');
        }
    }
}

export default new ImageController();
