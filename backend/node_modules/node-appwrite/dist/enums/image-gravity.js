'use strict';

var ImageGravity = /* @__PURE__ */ ((ImageGravity2) => {
  ImageGravity2["Center"] = "center";
  ImageGravity2["Topleft"] = "top-left";
  ImageGravity2["Top"] = "top";
  ImageGravity2["Topright"] = "top-right";
  ImageGravity2["Left"] = "left";
  ImageGravity2["Right"] = "right";
  ImageGravity2["Bottomleft"] = "bottom-left";
  ImageGravity2["Bottom"] = "bottom";
  ImageGravity2["Bottomright"] = "bottom-right";
  return ImageGravity2;
})(ImageGravity || {});

exports.ImageGravity = ImageGravity;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=image-gravity.js.map