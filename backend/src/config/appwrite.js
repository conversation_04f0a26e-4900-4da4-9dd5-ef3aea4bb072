import { Client, Account, Storage, Databases, Users } from 'node-appwrite';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Appwrite Client
const client = new Client()
  .setEndpoint(process.env.APPWRITE_ENDPOINT)
  .setProject(process.env.APPWRITE_PROJECT_ID)
  .setKey(process.env.APPWRITE_API_KEY);

// Initialize services
export const account = new Account(client);
export const storage = new Storage(client);
export const databases = new Databases(client);
export const users = new Users(client);

export default client;
