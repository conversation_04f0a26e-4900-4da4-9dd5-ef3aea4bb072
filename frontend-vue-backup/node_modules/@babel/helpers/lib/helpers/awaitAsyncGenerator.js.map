{"version": 3, "names": ["_OverloadYield", "require", "_awaitAsyncGenerator", "value", "OverloadYield"], "sources": ["../../src/helpers/awaitAsyncGenerator.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport OverloadYield from \"./OverloadYield.ts\";\n\nexport default function _awaitAsyncGenerator<T>(value: T) {\n  return new OverloadYield<T>(value, /* kind: await */ 0);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAEe,SAASC,oBAAoBA,CAAIC,KAAQ,EAAE;EACxD,OAAO,IAAIC,sBAAa,CAAID,KAAK,EAAoB,CAAC,CAAC;AACzD", "ignoreList": []}