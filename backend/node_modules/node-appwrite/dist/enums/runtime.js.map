{"version": 3, "sources": ["../../src/enums/runtime.ts"], "names": ["Runtime"], "mappings": "AAAO,IAAK,UAAL,kBAAKA,aAAL;AACH,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,eAAY;AACZ,EAAAA,SAAA,eAAY;AACZ,EAAAA,SAAA,eAAY;AACZ,EAAAA,SAAA,iBAAc;AACd,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,YAAS;AACT,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,aAAU;AACV,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,cAAW;AACX,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,WAAQ;AACR,EAAAA,SAAA,WAAQ;AA7CA,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum Runtime {\n    Node145 = 'node-14.5',\n    Node160 = 'node-16.0',\n    Node180 = 'node-18.0',\n    Node190 = 'node-19.0',\n    Node200 = 'node-20.0',\n    Node210 = 'node-21.0',\n    Php80 = 'php-8.0',\n    Php81 = 'php-8.1',\n    Php82 = 'php-8.2',\n    Php83 = 'php-8.3',\n    Ruby30 = 'ruby-3.0',\n    <PERSON><PERSON> = 'ruby-3.1',\n    Ruby32 = 'ruby-3.2',\n    <PERSON>33 = 'ruby-3.3',\n    Python38 = 'python-3.8',\n    Python39 = 'python-3.9',\n    Python310 = 'python-3.10',\n    Python311 = 'python-3.11',\n    Python312 = 'python-3.12',\n    Pythonml311 = 'python-ml-3.11',\n    Deno140 = 'deno-1.40',\n    Dart215 = 'dart-2.15',\n    Dart216 = 'dart-2.16',\n    Dart217 = 'dart-2.17',\n    Dart218 = 'dart-2.18',\n    Dart30 = 'dart-3.0',\n    Dart31 = 'dart-3.1',\n    Dart33 = 'dart-3.3',\n    <PERSON><PERSON>31 = 'dotnet-3.1',\n    Dotnet60 = 'dotnet-6.0',\n    Dotnet70 = 'dotnet-7.0',\n    Java80 = 'java-8.0',\n    Java110 = 'java-11.0',\n    Java170 = 'java-17.0',\n    Java180 = 'java-18.0',\n    Java210 = 'java-21.0',\n    Swift55 = 'swift-5.5',\n    Swift58 = 'swift-5.8',\n    Swift59 = 'swift-5.9',\n    Kotlin16 = 'kotlin-1.6',\n    Kotlin18 = 'kotlin-1.8',\n    Kotlin19 = 'kotlin-1.9',\n    Cpp17 = 'cpp-17',\n    Cpp20 = 'cpp-20',\n    Bun10 = 'bun-1.0',\n}"]}