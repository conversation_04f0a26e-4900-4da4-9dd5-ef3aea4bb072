import { databases } from '../config/appwrite.js';
import { ID, Query } from 'node-appwrite';

const DATABASE_ID = process.env.APPWRITE_DATABASE_ID || 'main';
const STORAGE_PLANS_COLLECTION = process.env.APPWRITE_STORAGE_PLANS_COLLECTION_ID || 'storage_plans';
const TRANSACTIONS_COLLECTION = process.env.APPWRITE_TRANSACTIONS_COLLECTION_ID || 'transactions';

export class StorageService {
  // Create a new storage plan
  static async createStoragePlan(planData) {
    try {
      const plan = await databases.createDocument(
        DATABASE_ID,
        STORAGE_PLANS_COLLECTION,
        ID.unique(),
        {
          name: planData.name,
          description: planData.description || '',
          storageAmountMB: planData.storageAmountMB,
          creditCost: planData.creditCost,
          includesFooterCustomization: planData.includesFooterCustomization || false,
          isActive: planData.isActive !== false, // Default to true
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      );
      return plan;
    } catch (error) {
      console.error('Error creating storage plan:', error);
      throw error;
    }
  }

  // Get all storage plans
  static async getAllStoragePlans(activeOnly = false) {
    try {
      const queries = [Query.orderDesc('createdAt')];
      if (activeOnly) {
        queries.push(Query.equal('isActive', true));
      }

      const plans = await databases.listDocuments(
        DATABASE_ID,
        STORAGE_PLANS_COLLECTION,
        queries
      );

      return plans.documents;
    } catch (error) {
      console.error('Error getting storage plans:', error);
      throw error;
    }
  }

  // Get storage plan by ID
  static async getStoragePlan(planId) {
    try {
      const plan = await databases.getDocument(
        DATABASE_ID,
        STORAGE_PLANS_COLLECTION,
        planId
      );
      return plan;
    } catch (error) {
      console.error('Error getting storage plan:', error);
      throw error;
    }
  }

  // Update storage plan
  static async updateStoragePlan(planId, updates) {
    try {
      const updatedPlan = await databases.updateDocument(
        DATABASE_ID,
        STORAGE_PLANS_COLLECTION,
        planId,
        {
          ...updates,
          updatedAt: new Date().toISOString()
        }
      );
      return updatedPlan;
    } catch (error) {
      console.error('Error updating storage plan:', error);
      throw error;
    }
  }

  // Delete storage plan
  static async deleteStoragePlan(planId) {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        STORAGE_PLANS_COLLECTION,
        planId
      );
      return { success: true };
    } catch (error) {
      console.error('Error deleting storage plan:', error);
      throw error;
    }
  }

  // Purchase storage plan
  static async purchaseStoragePlan(userId, planId) {
    try {
      // Get the plan details
      const plan = await this.getStoragePlan(planId);
      
      // Get user profile to check credits and current storage
      const { UserService } = await import('./userService.js');
      const userProfile = await UserService.getUserProfile(userId);

      // Check if user has enough credits
      if (userProfile.credits < plan.creditCost) {
        throw new Error('Insufficient credits');
      }

      // Calculate new storage limit
      const newStorageLimit = userProfile.storageLimit + (plan.storageAmountMB * 1024 * 1024);

      // Create transaction record
      const transaction = await databases.createDocument(
        DATABASE_ID,
        TRANSACTIONS_COLLECTION,
        ID.unique(),
        {
          userId,
          type: 'storage_purchase',
          planId,
          planName: plan.name,
          creditCost: plan.creditCost,
          storageAmountMB: plan.storageAmountMB,
          includesFooterCustomization: plan.includesFooterCustomization,
          status: 'completed',
          createdAt: new Date().toISOString()
        }
      );

      // Update user profile
      const updates = {
        credits: userProfile.credits - plan.creditCost,
        storageLimit: newStorageLimit
      };

      // If plan includes footer customization, grant permission
      if (plan.includesFooterCustomization) {
        updates.canCustomizeFooter = true;
      }

      const updatedProfile = await UserService.updateUserProfile(userId, updates);

      return {
        success: true,
        transaction,
        updatedProfile,
        message: `Successfully purchased ${plan.name} plan`
      };
    } catch (error) {
      console.error('Error purchasing storage plan:', error);
      throw error;
    }
  }

  // Get user transactions
  static async getUserTransactions(userId, limit = 20, offset = 0) {
    try {
      const transactions = await databases.listDocuments(
        DATABASE_ID,
        TRANSACTIONS_COLLECTION,
        [
          Query.equal('userId', userId),
          Query.orderDesc('createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return {
        transactions: transactions.documents,
        total: transactions.total
      };
    } catch (error) {
      console.error('Error getting user transactions:', error);
      throw error;
    }
  }

  // Get all transactions (admin)
  static async getAllTransactions(limit = 50, offset = 0) {
    try {
      const transactions = await databases.listDocuments(
        DATABASE_ID,
        TRANSACTIONS_COLLECTION,
        [
          Query.orderDesc('createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return {
        transactions: transactions.documents,
        total: transactions.total
      };
    } catch (error) {
      console.error('Error getting all transactions:', error);
      throw error;
    }
  }

  // Get storage statistics (admin)
  static async getStorageStats() {
    try {
      const plans = await this.getAllStoragePlans();
      const transactions = await this.getAllTransactions(1000); // Get more for stats

      const stats = {
        totalPlans: plans.length,
        activePlans: plans.filter(plan => plan.isActive).length,
        totalTransactions: transactions.total,
        totalRevenue: transactions.transactions.reduce((sum, t) => sum + (t.creditCost || 0), 0),
        recentTransactions: transactions.transactions.slice(0, 10)
      };

      return stats;
    } catch (error) {
      console.error('Error getting storage stats:', error);
      throw error;
    }
  }
}
