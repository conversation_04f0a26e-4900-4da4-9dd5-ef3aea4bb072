{"version": 3, "sources": ["../../src/services/graphql.ts"], "names": [], "mappings": "AAAA,SAAS,yBAA+D;AAGjE,MAAM,QAAQ;AAAA,EAGjB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,MAAM,OAA4B;AACpC,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,SAAS,OAA4B;AACvC,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\n\nexport class Graphql {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async query(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * GraphQL endpoint\n     *\n     * Execute a GraphQL mutation.\n     *\n     * @param {object} query\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async mutation(query: object): Promise<{}> {\n        if (typeof query === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"query\"');\n        }\n        const apiPath = '/graphql/mutation';\n        const payload: Payload = {};\n        if (typeof query !== 'undefined') {\n            payload['query'] = query;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'x-sdk-graphql': 'true',\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}