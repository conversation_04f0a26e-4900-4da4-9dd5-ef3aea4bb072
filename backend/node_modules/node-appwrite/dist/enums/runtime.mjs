// src/enums/runtime.ts
var Runtime = /* @__PURE__ */ ((Runtime2) => {
  Runtime2["Node145"] = "node-14.5";
  Runtime2["Node160"] = "node-16.0";
  Runtime2["Node180"] = "node-18.0";
  Runtime2["Node190"] = "node-19.0";
  Runtime2["Node200"] = "node-20.0";
  Runtime2["Node210"] = "node-21.0";
  Runtime2["Php80"] = "php-8.0";
  Runtime2["Php81"] = "php-8.1";
  Runtime2["Php82"] = "php-8.2";
  Runtime2["Php83"] = "php-8.3";
  Runtime2["Ruby30"] = "ruby-3.0";
  Runtime2["Ruby31"] = "ruby-3.1";
  Runtime2["Ruby32"] = "ruby-3.2";
  Runtime2["Ruby33"] = "ruby-3.3";
  Runtime2["Python38"] = "python-3.8";
  Runtime2["Python39"] = "python-3.9";
  Runtime2["Python310"] = "python-3.10";
  Runtime2["Python311"] = "python-3.11";
  Runtime2["Python312"] = "python-3.12";
  Runtime2["Pythonml311"] = "python-ml-3.11";
  Runtime2["Deno140"] = "deno-1.40";
  Runtime2["Dart215"] = "dart-2.15";
  Runtime2["Dart216"] = "dart-2.16";
  Runtime2["Dart217"] = "dart-2.17";
  Runtime2["Dart218"] = "dart-2.18";
  Runtime2["Dart30"] = "dart-3.0";
  Runtime2["Dart31"] = "dart-3.1";
  Runtime2["Dart33"] = "dart-3.3";
  Runtime2["Dotnet31"] = "dotnet-3.1";
  Runtime2["Dotnet60"] = "dotnet-6.0";
  Runtime2["Dotnet70"] = "dotnet-7.0";
  Runtime2["Java80"] = "java-8.0";
  Runtime2["Java110"] = "java-11.0";
  Runtime2["Java170"] = "java-17.0";
  Runtime2["Java180"] = "java-18.0";
  Runtime2["Java210"] = "java-21.0";
  Runtime2["Swift55"] = "swift-5.5";
  Runtime2["Swift58"] = "swift-5.8";
  Runtime2["Swift59"] = "swift-5.9";
  Runtime2["Kotlin16"] = "kotlin-1.6";
  Runtime2["Kotlin18"] = "kotlin-1.8";
  Runtime2["Kotlin19"] = "kotlin-1.9";
  Runtime2["Cpp17"] = "cpp-17";
  Runtime2["Cpp20"] = "cpp-20";
  Runtime2["Bun10"] = "bun-1.0";
  return Runtime2;
})(Runtime || {});

export { Runtime };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=runtime.mjs.map