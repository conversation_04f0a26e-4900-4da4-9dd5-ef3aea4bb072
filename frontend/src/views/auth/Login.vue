<template>
  <Layout>
    <FormCard title="Login to Continue" @submit="handleSubmit">
      <FormInput id="email" :label="$t('auth.pages.login.page.form.email.label')" v-model="form.email"
        :placeholder="$t('auth.pages.login.page.form.email.placeholder')" required />
      <div class="flex items-center justify-between mb-2">
        <label class="block text-sm text-gray-400">{{ $t("auth.pages.login.page.form.password.label") }}</label>
        <router-link to="/auth/forgot-password" class="text-sm text-purple-400 hover:text-purple-300">
          {{ $t("auth.pages.login.page.form.forgot_password") }}
        </router-link>
      </div>

      <FormInput id="password" type="password" v-model="form.password"
        :placeholder="t('auth.pages.login.page.form.password.placeholder')" required />

      <button type="submit"
        class="w-full mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
        :disabled="authStore.isLoading">
        {{ authStore.isLoading ? $t('auth.pages.login.page.form.login_button.loading') :
          $t('auth.pages.login.page.form.login_button.label') }}
      </button>

      <!-- OAuth buttons -->
      <div class="mt-4 space-y-2">
        <button @click="loginWithOAuth('google')" type="button"
          class="w-full px-4 py-2 bg-white text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center">
          <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Continue with Google
        </button>
        
        <button @click="loginWithOAuth('discord')" type="button"
          class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
          </svg>
          Continue with Discord
        </button>
      </div>

      <p class="mt-4 text-center text-sm text-gray-400">
        {{ $t('auth.pages.login.page.form.register.label') }}
        <router-link to="/auth/register" class="text-purple-400 hover:text-purple-300">
          {{ $t('auth.pages.login.page.form.register.link') }}
        </router-link>
      </p>
    </FormCard>
  </Layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import Layout from './../../components/Layout.vue'
import FormCard from './../../components/Auth/FormCard.vue'
import FormInput from './../../components/Auth/FormInput.vue'
import { useAuthStore } from '../../stores/auth.js'
import Swal from 'sweetalert2'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()

document.title = t('auth.pages.login.page.title')

const form = reactive({
  email: '',
  password: ''
})

const handleSubmit = async () => {
  try {
    if (!form.email || !form.password) {
      Swal.fire({
        icon: 'error',
        title: t('auth.pages.login.alerts.error.title'),
        text: t('auth.pages.login.alerts.error.missing_fields')
      })
      return
    }

    const result = await authStore.login({
      email: form.email,
      password: form.password
    })

    if (result.success) {
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: result.message,
        timer: 1500,
        showConfirmButton: false
      })
      
      // Redirect to intended page or dashboard
      const redirectTo = route.query.redirect || '/dashboard'
      router.push(redirectTo)
    } else {
      Swal.fire({
        icon: 'error',
        title: t('auth.pages.login.alerts.error.title'),
        text: result.error
      })
    }
  } catch (error) {
    Swal.fire({
      icon: 'error',
      title: t('auth.pages.login.alerts.error.title'),
      text: t('auth.pages.login.alerts.error.generic')
    })
  }
}

const loginWithOAuth = async (provider) => {
  try {
    const result = await authStore.loginWithOAuth(provider)
    if (result && !result.success) {
      Swal.fire({
        icon: 'error',
        title: 'OAuth Error',
        text: result.error
      })
    }
  } catch (error) {
    Swal.fire({
      icon: 'error',
      title: 'OAuth Error',
      text: 'Failed to initialize OAuth login'
    })
  }
}
</script>