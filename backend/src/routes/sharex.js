import express from 'express';
import sharexController from '../controllers/sharexController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All ShareX routes require authentication
router.use(authenticateUser);

// Get ShareX configuration
router.get('/config', sharexController.generateConfig);

// Download ShareX configuration file
router.get('/config/download', sharexController.downloadConfig);

// Get ShareX documentation
router.get('/docs', sharexController.getDocumentation);

export default router;
