'use strict';

class Permission {
}
Permission.read = (role) => {
  return `read("${role}")`;
};
Permission.write = (role) => {
  return `write("${role}")`;
};
Permission.create = (role) => {
  return `create("${role}")`;
};
Permission.update = (role) => {
  return `update("${role}")`;
};
Permission.delete = (role) => {
  return `delete("${role}")`;
};

exports.Permission = Permission;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=permission.js.map