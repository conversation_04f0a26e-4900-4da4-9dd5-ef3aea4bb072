var a=Object.defineProperty;var t=(e,r)=>a(e,"name",{value:r,configurable:!0});import{Blob as i,File as b,FormData as m,Headers as p,Request as n,Response as c,AbortController as h,fetch as F}from"./node.mjs";export{AbortError,FetchError,blobFrom,blobFromSync,fileFrom,fileFromSync,isRedirect}from"./node.mjs";import"node:http";import"node:https";import"node:zlib";import"node:stream";import"node:buffer";import"node:util";import"./shared/node-fetch-native-with-agent.1a4a356d.mjs";import"node:url";import"node:net";import"node:fs";import"node:path";var f=Object.defineProperty,g=t((e,r)=>f(e,"name",{value:r,configurable:!0}),"e");const o=!!globalThis.process?.env?.FORCE_NODE_FETCH;function l(){return!o&&globalThis.fetch?globalThis.fetch:F}t(l,"p"),g(l,"_getFetch");const s=l(),T=!o&&globalThis.Blob||i,R=!o&&globalThis.File||b,u=!o&&globalThis.FormData||m,d=!o&&globalThis.Headers||p,$=!o&&globalThis.Request||n,C=!o&&globalThis.Response||c,A=!o&&globalThis.AbortController||h;export{A as AbortController,T as Blob,R as File,u as FormData,d as Headers,$ as Request,C as Response,s as default,s as fetch};
