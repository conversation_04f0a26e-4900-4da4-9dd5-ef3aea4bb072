{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "VITE_APP_DEBUG=true vite --host", "build": "vite build ", "watch": "VITE_APP_DEBUG=true vite preview --host"}, "dependencies": {"@tanstack/vue-table": "^8.20.5", "@vueuse/core": "^11.2.0", "appwrite": "^15.0.0", "axios": "^1.6.2", "date-fns": "^4.1.0", "lucide-vue": "^0.454.0", "lucide-vue-next": "^0.454.0", "pinia": "^3.0.3", "vue": "^3.5.12", "vue-i18n": "^10.0.4", "vue-router": "4", "vue-sweetalert2": "^5.0.11"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.6.3"}}