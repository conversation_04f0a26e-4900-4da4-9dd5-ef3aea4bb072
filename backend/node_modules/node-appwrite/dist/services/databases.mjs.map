{"version": 3, "sources": ["../../src/services/databases.ts"], "names": [], "mappings": ";AAAA,SAAS,yBAA+D;AAMjE,IAAM,YAAN,MAAgB;AAAA,EAGnB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,KAAK,SAAoB,QAA+C;AAC1E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,OAAO,YAAoB,MAAc,SAA6C;AACxF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,IAAI,YAA8C;AACpD,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,OAAO,YAAoB,MAAc,SAA6C;AACxF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,YAAiC;AAC1C,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,0BAA0B,QAAQ,gBAAgB,UAAU;AAC5E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,gBAAgB,YAAoB,SAAoB,QAAiD;AAC3G,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,sCAAsC,QAAQ,gBAAgB,UAAU;AACxF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,iBAAiB,YAAoB,cAAsB,MAAc,aAAwB,kBAA4B,SAA+C;AAC9K,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,sCAAsC,QAAQ,gBAAgB,UAAU;AACxF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,qBAAqB,aAAa;AACzC,cAAQ,kBAAkB,IAAI;AAAA,IAClC;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,YAAoB,cAAkD;AACtF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,iBAAiB,YAAoB,cAAsB,MAAc,aAAwB,kBAA4B,SAA+C;AAC9K,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,qBAAqB,aAAa;AACzC,cAAQ,kBAAkB,IAAI;AAAA,IAClC;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,YAAoB,cAAmC;AAC1E,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,qDAAqD,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,YAAoB,cAAsB,SAAmD;AAC9G,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,gEAAgE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC1J,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,uBAAuB,YAAoB,cAAsB,KAAa,UAAmB,UAAoB,OAAmD;AAC1K,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,wEAAwE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAClK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,uBAAuB,YAAoB,cAAsB,KAAa,UAAmB,UAAsD;AACzJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,8EAA8E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC9L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,wBAAwB,YAAoB,cAAsB,KAAa,UAAmB,UAAmB,OAAoD;AAC3K,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,yEAAyE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACnK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,wBAAwB,YAAoB,cAAsB,KAAa,UAAmB,UAAsD;AAC1J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,+EAA+E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC/L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,qBAAqB,YAAoB,cAAsB,KAAa,UAAmB,UAAmB,OAAiD;AACrK,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,sEAAsE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAChK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,qBAAqB,YAAoB,cAAsB,KAAa,UAAmB,UAAmD;AACpJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC5L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,oBAAoB,YAAoB,cAAsB,KAAa,UAAoB,UAAmB,UAAmB,OAAgD;AACvL,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,qEAAqE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC/J,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,oBAAoB,YAAoB,cAAsB,KAAa,UAAoB,UAAmB,UAAkD;AACtK,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,2EAA2E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC3L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,qBAAqB,YAAoB,cAAsB,KAAa,UAAmB,KAAc,KAAc,UAAmB,OAAiD;AACjM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,sEAAsE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAChK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,qBAAqB,YAAoB,cAAsB,KAAa,UAAmB,KAAa,KAAa,UAAmD;AAC9K,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC5L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,uBAAuB,YAAoB,cAAsB,KAAa,UAAmB,KAAc,KAAc,UAAmB,OAAmD;AACrM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,wEAAwE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAClK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,uBAAuB,YAAoB,cAAsB,KAAa,UAAmB,KAAa,KAAa,UAAqD;AAClL,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,8EAA8E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC9L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,kBAAkB,YAAoB,cAAsB,KAAa,UAAmB,UAAmB,OAA8C;AAC/J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,mEAAmE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC7J,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,kBAAkB,YAAoB,cAAsB,KAAa,UAAmB,UAAgD;AAC9I,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,yEAAyE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACzL,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,4BAA4B,YAAoB,cAAsB,qBAA6B,MAAwB,QAAkB,KAAc,WAAoB,UAAkE;AACnP,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,wBAAwB,aAAa;AAC5C,YAAM,IAAI,kBAAkB,mDAAmD;AAAA,IACnF;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,6EAA6E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACvK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,wBAAwB,aAAa;AAC5C,cAAQ,qBAAqB,IAAI;AAAA,IACrC;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,sBAAsB,YAAoB,cAAsB,KAAa,MAAc,UAAmB,UAAmB,OAAiB,SAAoD;AACxM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,uEAAuE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACjK,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,sBAAsB,YAAoB,cAAsB,KAAa,UAAmB,UAAoD;AACtJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,6EAA6E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC7L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,mBAAmB,YAAoB,cAAsB,KAAa,UAAmB,UAAmB,OAA+C;AACjK,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,oEAAoE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AAC9J,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,mBAAmB,YAAoB,cAAsB,KAAa,UAAmB,UAAiD;AAChJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,0EAA0E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AAC1L,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,aAAa,YAAoB,cAAsB,KAA0B;AACnF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,sEAAsE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACtL,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,gBAAgB,YAAoB,cAAsB,KAA0B;AACtF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,sEAAsE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACtL,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,4BAA4B,YAAoB,cAAsB,KAAa,UAAkE;AACvJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,mFAAmF,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACnM,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAgD,YAAoB,cAAsB,SAA4D;AACxJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,eAAiD,YAAoB,cAAsB,YAAoB,MAA6C,aAA2C;AACzM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,+DAA+D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACzJ,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,YAA8C,YAAoB,cAAsB,YAAoB,SAAuC;AACrJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,eAAiD,YAAoB,cAAsB,YAAoB,MAAuD,aAA2C;AACnN,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,eAAe,YAAoB,cAAsB,YAAiC;AAC5F,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,4EAA4E,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,gBAAgB,UAAU;AAC1M,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,YAAoB,cAAsB,SAA+C;AACvG,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,6DAA6D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACvJ,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,YAAY,YAAoB,cAAsB,KAAa,MAAiB,YAAsB,QAA0C;AACtJ,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,6DAA6D,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY;AACvJ,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS,YAAoB,cAAsB,KAAoC;AACzF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,mEAAmE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACnL,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,YAAoB,cAAsB,KAA0B;AAClF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,mEAAmE,QAAQ,gBAAgB,UAAU,EAAE,QAAQ,kBAAkB,YAAY,EAAE,QAAQ,SAAS,GAAG;AACnL,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { RelationshipType } from '../enums/relationship-type';\nimport { RelationMutate } from '../enums/relation-mutate';\nimport { IndexType } from '../enums/index-type';\n\nexport class Databases {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List databases\n     *\n     * Get a list of all databases from the current Appwrite project. You can use the search parameter to filter your results.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DatabaseList>}\n     */\n    async list(queries?: string[], search?: string): Promise<Models.DatabaseList> {\n        const apiPath = '/databases';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create database\n     *\n     * Create a new Database.\n\n     *\n     * @param {string} databaseId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Database>}\n     */\n    async create(databaseId: string, name: string, enabled?: boolean): Promise<Models.Database> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/databases';\n        const payload: Payload = {};\n        if (typeof databaseId !== 'undefined') {\n            payload['databaseId'] = databaseId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get database\n     *\n     * Get a database by its unique ID. This endpoint response returns a JSON object with the database metadata.\n     *\n     * @param {string} databaseId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Database>}\n     */\n    async get(databaseId: string): Promise<Models.Database> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        const apiPath = '/databases/{databaseId}'.replace('{databaseId}', databaseId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update database\n     *\n     * Update a database by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Database>}\n     */\n    async update(databaseId: string, name: string, enabled?: boolean): Promise<Models.Database> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/databases/{databaseId}'.replace('{databaseId}', databaseId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete database\n     *\n     * Delete a database by its unique ID. Only API keys with with databases.write scope can delete a database.\n     *\n     * @param {string} databaseId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async delete(databaseId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        const apiPath = '/databases/{databaseId}'.replace('{databaseId}', databaseId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List collections\n     *\n     * Get a list of all collections that belong to the provided databaseId. You can use the search parameter to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.CollectionList>}\n     */\n    async listCollections(databaseId: string, queries?: string[], search?: string): Promise<Models.CollectionList> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections'.replace('{databaseId}', databaseId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create collection\n     *\n     * Create a new Collection. Before using this route, you should create a new database resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @param {boolean} documentSecurity\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Collection>}\n     */\n    async createCollection(databaseId: string, collectionId: string, name: string, permissions?: string[], documentSecurity?: boolean, enabled?: boolean): Promise<Models.Collection> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections'.replace('{databaseId}', databaseId);\n        const payload: Payload = {};\n        if (typeof collectionId !== 'undefined') {\n            payload['collectionId'] = collectionId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        if (typeof documentSecurity !== 'undefined') {\n            payload['documentSecurity'] = documentSecurity;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get collection\n     *\n     * Get a collection by its unique ID. This endpoint response returns a JSON object with the collection metadata.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Collection>}\n     */\n    async getCollection(databaseId: string, collectionId: string): Promise<Models.Collection> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update collection\n     *\n     * Update a collection by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} name\n     * @param {string[]} permissions\n     * @param {boolean} documentSecurity\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Collection>}\n     */\n    async updateCollection(databaseId: string, collectionId: string, name: string, permissions?: string[], documentSecurity?: boolean, enabled?: boolean): Promise<Models.Collection> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        if (typeof documentSecurity !== 'undefined') {\n            payload['documentSecurity'] = documentSecurity;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete collection\n     *\n     * Delete a collection by its unique ID. Only users with write permissions have access to delete this resource.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteCollection(databaseId: string, collectionId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List attributes\n     *\n     * List attributes in the collection.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeList>}\n     */\n    async listAttributes(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.AttributeList> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create boolean attribute\n     *\n     * Create a boolean attribute.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {boolean} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeBoolean>}\n     */\n    async createBooleanAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: boolean, array?: boolean): Promise<Models.AttributeBoolean> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/boolean'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update boolean attribute\n     *\n     * Update a boolean attribute. Changing the `default` value will not update already existing documents.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {boolean} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeBoolean>}\n     */\n    async updateBooleanAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: boolean): Promise<Models.AttributeBoolean> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/boolean/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create datetime attribute\n     *\n     * Create a date time attribute according to the ISO 8601 standard.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeDatetime>}\n     */\n    async createDatetimeAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string, array?: boolean): Promise<Models.AttributeDatetime> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/datetime'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update dateTime attribute\n     *\n     * Update a date time attribute. Changing the `default` value will not update already existing documents.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeDatetime>}\n     */\n    async updateDatetimeAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string): Promise<Models.AttributeDatetime> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/datetime/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email attribute\n     *\n     * Create an email attribute.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeEmail>}\n     */\n    async createEmailAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string, array?: boolean): Promise<Models.AttributeEmail> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/email'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update email attribute\n     *\n     * Update an email attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeEmail>}\n     */\n    async updateEmailAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string): Promise<Models.AttributeEmail> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/email/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create enum attribute\n     *\n     * Create an enumeration attribute. The `elements` param acts as a white-list of accepted values for this attribute. \n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {string[]} elements\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeEnum>}\n     */\n    async createEnumAttribute(databaseId: string, collectionId: string, key: string, elements: string[], required: boolean, xdefault?: string, array?: boolean): Promise<Models.AttributeEnum> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof elements === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"elements\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/enum'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof elements !== 'undefined') {\n            payload['elements'] = elements;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update enum attribute\n     *\n     * Update an enum attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {string[]} elements\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeEnum>}\n     */\n    async updateEnumAttribute(databaseId: string, collectionId: string, key: string, elements: string[], required: boolean, xdefault?: string): Promise<Models.AttributeEnum> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof elements === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"elements\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/enum/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof elements !== 'undefined') {\n            payload['elements'] = elements;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create float attribute\n     *\n     * Create a float attribute. Optionally, minimum and maximum values can be provided.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {number} min\n     * @param {number} max\n     * @param {number} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeFloat>}\n     */\n    async createFloatAttribute(databaseId: string, collectionId: string, key: string, required: boolean, min?: number, max?: number, xdefault?: number, array?: boolean): Promise<Models.AttributeFloat> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/float'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update float attribute\n     *\n     * Update a float attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {number} min\n     * @param {number} max\n     * @param {number} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeFloat>}\n     */\n    async updateFloatAttribute(databaseId: string, collectionId: string, key: string, required: boolean, min: number, max: number, xdefault?: number): Promise<Models.AttributeFloat> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof min === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"min\"');\n        }\n        if (typeof max === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"max\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/float/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create integer attribute\n     *\n     * Create an integer attribute. Optionally, minimum and maximum values can be provided.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {number} min\n     * @param {number} max\n     * @param {number} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeInteger>}\n     */\n    async createIntegerAttribute(databaseId: string, collectionId: string, key: string, required: boolean, min?: number, max?: number, xdefault?: number, array?: boolean): Promise<Models.AttributeInteger> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/integer'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update integer attribute\n     *\n     * Update an integer attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {number} min\n     * @param {number} max\n     * @param {number} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeInteger>}\n     */\n    async updateIntegerAttribute(databaseId: string, collectionId: string, key: string, required: boolean, min: number, max: number, xdefault?: number): Promise<Models.AttributeInteger> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof min === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"min\"');\n        }\n        if (typeof max === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"max\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/integer/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof min !== 'undefined') {\n            payload['min'] = min;\n        }\n        if (typeof max !== 'undefined') {\n            payload['max'] = max;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create IP address attribute\n     *\n     * Create IP address attribute.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeIp>}\n     */\n    async createIpAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string, array?: boolean): Promise<Models.AttributeIp> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/ip'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update IP address attribute\n     *\n     * Update an ip attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeIp>}\n     */\n    async updateIpAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string): Promise<Models.AttributeIp> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/ip/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create relationship attribute\n     *\n     * Create relationship attribute. [Learn more about relationship attributes](https://appwrite.io/docs/databases-relationships#relationship-attributes).\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} relatedCollectionId\n     * @param {RelationshipType} type\n     * @param {boolean} twoWay\n     * @param {string} key\n     * @param {string} twoWayKey\n     * @param {RelationMutate} onDelete\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeRelationship>}\n     */\n    async createRelationshipAttribute(databaseId: string, collectionId: string, relatedCollectionId: string, type: RelationshipType, twoWay?: boolean, key?: string, twoWayKey?: string, onDelete?: RelationMutate): Promise<Models.AttributeRelationship> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof relatedCollectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"relatedCollectionId\"');\n        }\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/relationship'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof relatedCollectionId !== 'undefined') {\n            payload['relatedCollectionId'] = relatedCollectionId;\n        }\n        if (typeof type !== 'undefined') {\n            payload['type'] = type;\n        }\n        if (typeof twoWay !== 'undefined') {\n            payload['twoWay'] = twoWay;\n        }\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof twoWayKey !== 'undefined') {\n            payload['twoWayKey'] = twoWayKey;\n        }\n        if (typeof onDelete !== 'undefined') {\n            payload['onDelete'] = onDelete;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create string attribute\n     *\n     * Create a string attribute.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {number} size\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @param {boolean} encrypt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeString>}\n     */\n    async createStringAttribute(databaseId: string, collectionId: string, key: string, size: number, required: boolean, xdefault?: string, array?: boolean, encrypt?: boolean): Promise<Models.AttributeString> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof size === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"size\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/string'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        if (typeof encrypt !== 'undefined') {\n            payload['encrypt'] = encrypt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update string attribute\n     *\n     * Update a string attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeString>}\n     */\n    async updateStringAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string): Promise<Models.AttributeString> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/string/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create URL attribute\n     *\n     * Create a URL attribute.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @param {boolean} array\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeUrl>}\n     */\n    async createUrlAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string, array?: boolean): Promise<Models.AttributeUrl> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/url'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        if (typeof array !== 'undefined') {\n            payload['array'] = array;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update URL attribute\n     *\n     * Update an url attribute. Changing the `default` value will not update already existing documents.\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {boolean} required\n     * @param {string} xdefault\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeUrl>}\n     */\n    async updateUrlAttribute(databaseId: string, collectionId: string, key: string, required: boolean, xdefault?: string): Promise<Models.AttributeUrl> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof required === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"required\"');\n        }\n        if (typeof xdefault === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"xdefault\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/url/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof required !== 'undefined') {\n            payload['required'] = required;\n        }\n        if (typeof xdefault !== 'undefined') {\n            payload['default'] = xdefault;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get attribute\n     *\n     * Get attribute by ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async getAttribute(databaseId: string, collectionId: string, key: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete attribute\n     *\n     * Deletes an attribute.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteAttribute(databaseId: string, collectionId: string, key: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update relationship attribute\n     *\n     * Update relationship attribute. [Learn more about relationship attributes](https://appwrite.io/docs/databases-relationships#relationship-attributes).\n\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {RelationMutate} onDelete\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.AttributeRelationship>}\n     */\n    async updateRelationshipAttribute(databaseId: string, collectionId: string, key: string, onDelete?: RelationMutate): Promise<Models.AttributeRelationship> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/attributes/{key}/relationship'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        if (typeof onDelete !== 'undefined') {\n            payload['onDelete'] = onDelete;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List documents\n     *\n     * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.DocumentList<Document>>}\n     */\n    async listDocuments<Document extends Models.Document>(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.DocumentList<Document>> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create document\n     *\n     * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Omit<Document, keyof Models.Document>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async createDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data: Omit<Document, keyof Models.Document>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        if (typeof data === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"data\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof documentId !== 'undefined') {\n            payload['documentId'] = documentId;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get document\n     *\n     * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async getDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, queries?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update document\n     *\n     * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @param {Partial<Omit<Document, keyof Models.Document>>} data\n     * @param {string[]} permissions\n     * @throws {AppwriteException}\n     * @returns {Promise<Document>}\n     */\n    async updateDocument<Document extends Models.Document>(databaseId: string, collectionId: string, documentId: string, data?: Partial<Omit<Document, keyof Models.Document>>, permissions?: string[]): Promise<Document> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof permissions !== 'undefined') {\n            payload['permissions'] = permissions;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete document\n     *\n     * Delete a document by its unique ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} documentId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteDocument(databaseId: string, collectionId: string, documentId: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof documentId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"documentId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/documents/{documentId}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{documentId}', documentId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List indexes\n     *\n     * List indexes in the collection.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IndexList>}\n     */\n    async listIndexes(databaseId: string, collectionId: string, queries?: string[]): Promise<Models.IndexList> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/indexes'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create index\n     *\n     * Creates an index on the attributes listed. Your index should include all the attributes you will query in a single request.\nAttributes can be `key`, `fulltext`, and `unique`.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @param {IndexType} type\n     * @param {string[]} attributes\n     * @param {string[]} orders\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Index>}\n     */\n    async createIndex(databaseId: string, collectionId: string, key: string, type: IndexType, attributes: string[], orders?: string[]): Promise<Models.Index> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof attributes === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"attributes\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/indexes'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId);\n        const payload: Payload = {};\n        if (typeof key !== 'undefined') {\n            payload['key'] = key;\n        }\n        if (typeof type !== 'undefined') {\n            payload['type'] = type;\n        }\n        if (typeof attributes !== 'undefined') {\n            payload['attributes'] = attributes;\n        }\n        if (typeof orders !== 'undefined') {\n            payload['orders'] = orders;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get index\n     *\n     * Get index by ID.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Index>}\n     */\n    async getIndex(databaseId: string, collectionId: string, key: string): Promise<Models.Index> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/indexes/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete index\n     *\n     * Delete an index.\n     *\n     * @param {string} databaseId\n     * @param {string} collectionId\n     * @param {string} key\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteIndex(databaseId: string, collectionId: string, key: string): Promise<{}> {\n        if (typeof databaseId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"databaseId\"');\n        }\n        if (typeof collectionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"collectionId\"');\n        }\n        if (typeof key === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"key\"');\n        }\n        const apiPath = '/databases/{databaseId}/collections/{collectionId}/indexes/{key}'.replace('{databaseId}', databaseId).replace('{collectionId}', collectionId).replace('{key}', key);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}