import { NextRequest, NextResponse } from 'next/server';
import { storage, databases, config } from '@/lib/appwrite';
import { ID, Permission, Role } from 'appwrite';

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authorization required' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, error: 'File must be an image' },
        { status: 400 }
      );
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File size must be less than 50MB' },
        { status: 400 }
      );
    }

    // Convert File to Buffer for Appwrite
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to Appwrite Storage
    const uploadedFile = await storage.createFile(
      config.appwrite.bucketId,
      ID.unique(),
      buffer,
      [Permission.read(Role.any())]
    );

    // Get file URL
    const fileUrl = storage.getFileView(config.appwrite.bucketId, uploadedFile.$id);

    // Save image metadata to database
    const imageDoc = await databases.createDocument(
      config.appwrite.databaseId,
      config.appwrite.collections.images,
      ID.unique(),
      {
        name: file.name,
        file_id: uploadedFile.$id,
        url: fileUrl.href,
        size: file.size,
        file_type: file.type,
        uploaded_at: new Date().toISOString(),
        user_id: 'current_user' // This should be extracted from auth
      }
    );

    const imageData = {
      id: imageDoc.$id,
      name: imageDoc.name,
      url: imageDoc.url,
      size: imageDoc.size,
      file_type: imageDoc.file_type,
      uploaded_at: imageDoc.uploaded_at,
      user_id: imageDoc.user_id
    };

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully',
      image: imageData
    });

  } catch (error: any) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Upload failed' },
      { status: 500 }
    );
  }
}
