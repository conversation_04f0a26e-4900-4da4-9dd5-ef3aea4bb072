<template>
  <div class="image-gallery">
    <!-- Gallery Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
          Your Images
        </h2>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {{ imageStore.totalImages }} images • {{ formatFileSize(imageStore.totalSize) }}
        </p>
      </div>
      
      <div class="flex items-center space-x-2 mt-4 sm:mt-0">
        <button
          @click="refreshImages"
          :disabled="imageStore.isLoading"
          class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50"
        >
          <RefreshCw :class="['w-4 h-4', imageStore.isLoading && 'animate-spin']" />
        </button>
        
        <select
          v-model="sortBy"
          @change="sortImages"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
        >
          <option value="newest">Newest First</option>
          <option value="oldest">Oldest First</option>
          <option value="name">Name A-Z</option>
          <option value="size">Size (Largest)</option>
        </select>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="imageStore.isLoading && imageStore.images.length === 0" class="text-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
      <p class="text-gray-500 dark:text-gray-400">Loading images...</p>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="imageStore.images.length === 0" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
        <Image class="w-8 h-8 text-gray-400" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        No images yet
      </h3>
      <p class="text-gray-500 dark:text-gray-400">
        Upload your first image to get started
      </p>
    </div>
    
    <!-- Image Grid -->
    <div v-else>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <div
          v-for="image in sortedImages"
          :key="image.id"
          class="group relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow"
        >
          <!-- Image -->
          <div class="aspect-square bg-gray-100 dark:bg-gray-700">
            <img
              :src="image.url"
              :alt="image.name"
              class="w-full h-full object-cover cursor-pointer"
              @click="openImageModal(image)"
              @error="handleImageError"
            />
          </div>
          
          <!-- Overlay -->
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div class="flex space-x-2">
              <button
                @click="copyImageUrl(image.url)"
                class="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                title="Copy URL"
              >
                <Copy class="w-4 h-4 text-gray-700" />
              </button>
              
              <button
                @click="openImageModal(image)"
                class="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                title="View Details"
              >
                <Eye class="w-4 h-4 text-gray-700" />
              </button>
              
              <button
                @click="deleteImage(image)"
                class="p-2 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                title="Delete"
              >
                <Trash2 class="w-4 h-4 text-white" />
              </button>
            </div>
          </div>
          
          <!-- Image Info -->
          <div class="p-3">
            <h4 class="font-medium text-gray-900 dark:text-white text-sm truncate" :title="image.name">
              {{ image.name }}
            </h4>
            <div class="flex items-center justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
              <span>{{ formatFileSize(image.size) }}</span>
              <span>{{ formatDate(image.uploadedAt) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Load More Button -->
      <div v-if="imageStore.hasMore" class="text-center mt-8">
        <button
          @click="loadMoreImages"
          :disabled="imageStore.isLoading"
          class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ imageStore.isLoading ? 'Loading...' : 'Load More' }}
        </button>
      </div>
    </div>
    
    <!-- Image Modal -->
    <Modal v-if="selectedImage" @close="closeImageModal">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <!-- Modal Header -->
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              {{ selectedImage.name }}
            </h3>
            <button
              @click="closeImageModal"
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X class="w-6 h-6" />
            </button>
          </div>
          
          <!-- Modal Body -->
          <div class="p-6">
            <div class="flex flex-col lg:flex-row gap-6">
              <!-- Image Preview -->
              <div class="flex-1">
                <img
                  :src="selectedImage.url"
                  :alt="selectedImage.name"
                  class="w-full max-h-96 object-contain rounded-lg bg-gray-100 dark:bg-gray-700"
                />
              </div>
              
              <!-- Image Details -->
              <div class="lg:w-80 space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Direct Link
                  </label>
                  <div class="flex">
                    <input
                      :value="selectedImage.url"
                      readonly
                      class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                    />
                    <button
                      @click="copyImageUrl(selectedImage.url)"
                      class="px-3 py-2 bg-purple-600 text-white rounded-r-lg hover:bg-purple-700 transition-colors"
                    >
                      <Copy class="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Size
                    </label>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {{ formatFileSize(selectedImage.size) }}
                    </p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Type
                    </label>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {{ selectedImage.type }}
                    </p>
                  </div>
                  
                  <div class="col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Uploaded
                    </label>
                    <p class="text-sm text-gray-900 dark:text-white">
                      {{ formatDate(selectedImage.uploadedAt, true) }}
                    </p>
                  </div>
                </div>
                
                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    @click="deleteImage(selectedImage)"
                    class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center"
                  >
                    <Trash2 class="w-4 h-4 mr-2" />
                    Delete Image
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Image, RefreshCw, Copy, Eye, Trash2, X } from 'lucide-vue-next'
import { useImagesStore } from '../stores/images.js'
import Modal from './ui/Modal.vue'
import { format } from 'date-fns'
import Swal from 'sweetalert2'

const imageStore = useImagesStore()

const selectedImage = ref(null)
const sortBy = ref('newest')

const sortedImages = computed(() => {
  const images = [...imageStore.images]
  
  switch (sortBy.value) {
    case 'oldest':
      return images.sort((a, b) => new Date(a.uploadedAt) - new Date(b.uploadedAt))
    case 'name':
      return images.sort((a, b) => a.name.localeCompare(b.name))
    case 'size':
      return images.sort((a, b) => b.size - a.size)
    case 'newest':
    default:
      return images.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt))
  }
})

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString, includeTime = false) => {
  const date = new Date(dateString)
  if (includeTime) {
    return format(date, 'MMM dd, yyyy \'at\' h:mm a')
  }
  return format(date, 'MMM dd')
}

const openImageModal = (image) => {
  selectedImage.value = image
}

const closeImageModal = () => {
  selectedImage.value = null
}

const copyImageUrl = async (url) => {
  const result = await imageStore.copyImageUrl(url)
  if (result.success) {
    Swal.fire({
      icon: 'success',
      title: 'Copied!',
      text: result.message,
      timer: 1500,
      showConfirmButton: false
    })
  } else {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: result.error
    })
  }
}

const deleteImage = async (image) => {
  const result = await Swal.fire({
    title: 'Delete Image?',
    text: `Are you sure you want to delete "${image.name}"? This action cannot be undone.`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#dc2626',
    cancelButtonColor: '#6b7280',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel'
  })
  
  if (result.isConfirmed) {
    const deleteResult = await imageStore.deleteImage(image.id)
    if (deleteResult.success) {
      Swal.fire({
        icon: 'success',
        title: 'Deleted!',
        text: deleteResult.message,
        timer: 1500,
        showConfirmButton: false
      })
      
      // Close modal if the deleted image was selected
      if (selectedImage.value?.id === image.id) {
        closeImageModal()
      }
    } else {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: deleteResult.error
      })
    }
  }
}

const refreshImages = async () => {
  await imageStore.refreshImages()
}

const loadMoreImages = async () => {
  await imageStore.loadMoreImages()
}

const sortImages = () => {
  // The computed property will automatically update
}

const handleImageError = (event) => {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDNWMjFIMzYwTDIxIDNaIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiAxNkMxMC44OTU0IDE2IDEwIDEwLjkwNDYgMTAgMTBDMTAgOS4wOTU0MyAxMC44OTU0IDggMTIgOEMxMy4xMDQ2IDggMTQgOC4wOTU0MyAxNCA5VjEwQzE0IDEwLjkwNDYgMTMuMTA0NiAxMiAxMiAxMloiIGZpbGw9IiM5CA0MTEzQiUyQV9fX19fX19fX19fX19fX19fX19fX18nfS8+Cjwvc3ZnPgo='
}

onMounted(() => {
  imageStore.fetchImages()
})
</script>
