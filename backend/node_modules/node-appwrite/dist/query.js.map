{"version": 3, "sources": ["../src/query.ts"], "names": [], "mappings": "AAKO,MAAM,SAAN,MAAM,OAAM;AAAA,EAKjB,YACE,QACA,WACA,QACA;AACA,SAAK,SAAS;AACd,SAAK,YAAY;AAEjB,QAAI,WAAW,QAAW;AACxB,UAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,SAAS,CAAC,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAmB;AACjB,WAAO,KAAK,UAAU;AAAA,MACpB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAmEF;AA/Fa,OA8BJ,QAAQ,CAAC,WAAmB,UACjC,IAAI,OAAM,SAAS,WAAW,KAAK,EAAE,SAAS;AA/BrC,OAiCJ,WAAW,CAAC,WAAmB,UACpC,IAAI,OAAM,YAAY,WAAW,KAAK,EAAE,SAAS;AAlCxC,OAoCJ,WAAW,CAAC,WAAmB,UACpC,IAAI,OAAM,YAAY,WAAW,KAAK,EAAE,SAAS;AArCxC,OAuCJ,gBAAgB,CAAC,WAAmB,UACzC,IAAI,OAAM,iBAAiB,WAAW,KAAK,EAAE,SAAS;AAxC7C,OA0CJ,cAAc,CAAC,WAAmB,UACvC,IAAI,OAAM,eAAe,WAAW,KAAK,EAAE,SAAS;AA3C3C,OA6CJ,mBAAmB,CAAC,WAAmB,UAC5C,IAAI,OAAM,oBAAoB,WAAW,KAAK,EAAE,SAAS;AA9ChD,OAgDJ,SAAS,CAAC,cACf,IAAI,OAAM,UAAU,SAAS,EAAE,SAAS;AAjD/B,OAmDJ,YAAY,CAAC,cAClB,IAAI,OAAM,aAAa,SAAS,EAAE,SAAS;AApDlC,OAsDJ,UAAU,CAAC,WAAmB,OAAwB,QAC3D,IAAI,OAAM,WAAW,WAAW,CAAC,OAAO,GAAG,CAAmB,EAAE,SAAS;AAvDhE,OAyDJ,aAAa,CAAC,WAAmB,UACtC,IAAI,OAAM,cAAc,WAAW,KAAK,EAAE,SAAS;AA1D1C,OA4DJ,WAAW,CAAC,WAAmB,UACpC,IAAI,OAAM,YAAY,WAAW,KAAK,EAAE,SAAS;AA7DxC,OA+DJ,SAAS,CAAC,eACf,IAAI,OAAM,UAAU,QAAW,UAAU,EAAE,SAAS;AAhE3C,OAkEJ,SAAS,CAAC,WAAmB,UAClC,IAAI,OAAM,UAAU,WAAW,KAAK,EAAE,SAAS;AAnEtC,OAqEJ,YAAY,CAAC,cAClB,IAAI,OAAM,aAAa,SAAS,EAAE,SAAS;AAtElC,OAwEJ,WAAW,CAAC,cACjB,IAAI,OAAM,YAAY,SAAS,EAAE,SAAS;AAzEjC,OA2EJ,cAAc,CAAC,eACpB,IAAI,OAAM,eAAe,QAAW,UAAU,EAAE,SAAS;AA5EhD,OA8EJ,eAAe,CAAC,eACrB,IAAI,OAAM,gBAAgB,QAAW,UAAU,EAAE,SAAS;AA/EjD,OAiFJ,QAAQ,CAAC,UACd,IAAI,OAAM,SAAS,QAAW,KAAK,EAAE,SAAS;AAlFrC,OAoFJ,SAAS,CAAC,WACf,IAAI,OAAM,UAAU,QAAW,MAAM,EAAE,SAAS;AArFvC,OAuFJ,WAAW,CAAC,WAAmB,UACpC,IAAI,OAAM,YAAY,WAAW,KAAK,EAAE,SAAS;AAxFxC,OA0FJ,KAAK,CAAC,YACX,IAAI,OAAM,MAAM,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS;AA3FtE,OA6FJ,MAAM,CAAC,YACZ,IAAI,OAAM,OAAO,QAAW,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS;AA9F7E,IAAM,QAAN", "sourcesContent": ["type QueryTypesSingle = string | number | boolean;\nexport type QueryTypesList = string[] | number[] | boolean[] | Query[];\nexport type QueryTypes = QueryTypesSingle | QueryTypesList;\ntype AttributesTypes = string | string[];\n\nexport class Query {\n  method: string;\n  attribute: AttributesTypes | undefined;\n  values: QueryTypesList | undefined;\n\n  constructor(\n    method: string,\n    attribute?: AttributesTypes,\n    values?: QueryTypes\n  ) {\n    this.method = method;\n    this.attribute = attribute;\n\n    if (values !== undefined) {\n      if (Array.isArray(values)) {\n        this.values = values;\n      } else {\n        this.values = [values] as QueryTypesList;\n      }\n    }\n  }\n\n  toString(): string {\n    return JSON.stringify({\n      method: this.method,\n      attribute: this.attribute,\n      values: this.values,\n    });\n  }\n\n  static equal = (attribute: string, value: QueryTypes): string =>\n    new Query(\"equal\", attribute, value).toString();\n\n  static notEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"notEqual\", attribute, value).toString();\n\n  static lessThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThan\", attribute, value).toString();\n\n  static lessThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"lessThanEqual\", attribute, value).toString();\n\n  static greaterThan = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThan\", attribute, value).toString();\n\n  static greaterThanEqual = (attribute: string, value: QueryTypes): string =>\n    new Query(\"greaterThanEqual\", attribute, value).toString();\n\n  static isNull = (attribute: string): string =>\n    new Query(\"isNull\", attribute).toString();\n\n  static isNotNull = (attribute: string): string =>\n    new Query(\"isNotNull\", attribute).toString();\n\n  static between = (attribute: string, start: string | number, end: string | number) =>\n    new Query(\"between\", attribute, [start, end] as QueryTypesList).toString();\n\n  static startsWith = (attribute: string, value: string): string =>\n    new Query(\"startsWith\", attribute, value).toString();\n\n  static endsWith = (attribute: string, value: string): string =>\n    new Query(\"endsWith\", attribute, value).toString();\n\n  static select = (attributes: string[]): string =>\n    new Query(\"select\", undefined, attributes).toString();\n\n  static search = (attribute: string, value: string): string =>\n    new Query(\"search\", attribute, value).toString();\n\n  static orderDesc = (attribute: string): string =>\n    new Query(\"orderDesc\", attribute).toString();\n\n  static orderAsc = (attribute: string): string =>\n    new Query(\"orderAsc\", attribute).toString();\n\n  static cursorAfter = (documentId: string): string =>\n    new Query(\"cursorAfter\", undefined, documentId).toString();\n\n  static cursorBefore = (documentId: string): string =>\n    new Query(\"cursorBefore\", undefined, documentId).toString();\n\n  static limit = (limit: number): string =>\n    new Query(\"limit\", undefined, limit).toString();\n\n  static offset = (offset: number): string =>\n    new Query(\"offset\", undefined, offset).toString();\n\n  static contains = (attribute: string, value: string | string[]): string =>\n    new Query(\"contains\", attribute, value).toString();\n\n  static or = (queries: string[]) =>\n    new Query(\"or\", undefined, queries.map((query) => JSON.parse(query))).toString();\n\n  static and = (queries: string[]) =>\n    new Query(\"and\", undefined, queries.map((query) => JSON.parse(query))).toString();\n}\n"]}