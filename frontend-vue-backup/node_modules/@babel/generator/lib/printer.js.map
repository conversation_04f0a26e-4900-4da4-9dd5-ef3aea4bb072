{"version": 3, "names": ["_buffer", "require", "n", "_t", "_tokenMap", "generatorFunctions", "isExpression", "isFunction", "isStatement", "isClassBody", "isTSInterfaceBody", "isTSEnumDeclaration", "SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "HAS_NEWLINE", "HAS_NEWLINE_OR_BlOCK_COMMENT_END", "commentIsNewline", "c", "type", "test", "value", "needsParens", "Printer", "constructor", "format", "map", "tokens", "originalCode", "inForStatementInit", "tokenContext", "_tokens", "_originalCode", "_currentNode", "_indent", "_indentRepeat", "_insideAux", "_noLineTerminator", "_noLineTerminatorAfterNode", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_endsWithDiv", "_lastCommentLine", "_endsWithInnerRaw", "_indentInnerComments", "tokenMap", "_boundGetRawIdentifier", "_getRawIdentifier", "bind", "indent", "style", "length", "_inputMap", "_buf", "<PERSON><PERSON><PERSON>", "enterForStatementInit", "enterDelimited", "oldInForStatementInit", "oldNoLineTerminatorAfterNode", "generate", "ast", "preserveFormat", "TokenMap", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "node", "start", "end", "endMatches", "indexes", "getIndexes", "_catchUpTo", "loc", "_queue", "rightBrace", "minified", "removeLastSemicolon", "sourceWithOffset", "token", "rightParens", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "noLineTerminatorAfter", "_maybePrintInnerComments", "charCodeAt", "_append", "number", "isNonDecimalLiteral", "secondChar", "Number", "isInteger", "maybeNewline", "occurrenceCount", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "String", "fromCharCode", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWith", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "cb", "_catchUp", "source", "prop", "columnOffset", "sourceIdentifierName", "identifierName", "pos", "_canMarkIdName", "sourcePosition", "_sourcePosition", "identifierNamePos", "findMatching", "_maybeIndent", "append", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "catchUp", "line", "count", "getCurrentLine", "column", "index", "spacesCount", "getCurrentColumn", "spaces", "slice", "replace", "repeat", "printTerminatorless", "trailingCommentsLineOffset", "_node$extra", "_node$leadingComments", "_node$leadingComments2", "nodeType", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "parent", "oldInAux", "parenthesized", "extra", "shouldPrintParens", "retainFunctionParens", "leadingComments", "parentType", "callee", "indentParenthesized", "some", "oldInForStatementInitWasTrue", "isLastChild", "_node$trailingComment", "trailingComments", "_printLeadingComments", "_printTrailingComments", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "_nodes$0$loc", "startLine", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "len", "statement", "_printNewline", "iterator", "printTrailingSeparator", "_node$trailingComment2", "_nextNode$loc", "nextNode", "printAndIndentOnComments", "printBlock", "body", "lineOffset", "innerComments", "_printComments", "comments", "nextTokenStr", "nextTokenOccurrenceCount", "_this$tokenMap", "printInnerComments", "nextToken", "hasSpace", "printedCommentsCount", "size", "noIndentInnerCommentsHere", "printSequence", "_opts$indent", "printList", "items", "commaSeparator", "shouldPrintTrailingComma", "listEnd", "listEndIndex", "findLastIndex", "matchesOriginal", "newLine", "lastCommentLine", "offset", "_should<PERSON>rintComment", "ignore", "has", "commentTok", "find", "add", "shouldPrintComment", "skipNewLines", "noLineTerminator", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "_comment$loc", "newlineRegex", "RegExp", "indentSize", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "lastLine", "leadingCommentNewline", "should<PERSON><PERSON>t", "commentStartLine", "commentEndLine", "Math", "max", "min", "singleLine", "shouldSkipNewline", "properties", "Object", "assign", "prototype", "Noop", "_default", "exports", "default", "last"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer, { type Po<PERSON> } from \"./buffer.ts\";\nimport type { Loc } from \"./buffer.ts\";\nimport * as n from \"./node/index.ts\";\nimport type * as t from \"@babel/types\";\nimport {\n  isExpression,\n  isFunction,\n  isStatement,\n  isClassBody,\n  isTSInterfaceBody,\n  isTSEnumDeclaration,\n} from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport { TokenMap } from \"./token-map.ts\";\nimport type { GeneratorOptions } from \"./index.ts\";\nimport * as generatorFunctions from \"./generators/index.ts\";\nimport type SourceMap from \"./source-map.ts\";\nimport type { TraceMap } from \"@jridgewell/trace-mapping\";\nimport type { Token } from \"@babel/parser\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst HAS_NEWLINE = /[\\n\\r\\u2028\\u2029]/;\nconst HAS_NEWLINE_OR_BlOCK_COMMENT_END = /[\\n\\r\\u2028\\u2029]|\\*\\//;\n\nfunction commentIsNewline(c: t.Comment) {\n  return c.type === \"CommentLine\" || HAS_NEWLINE.test(c.value);\n}\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  ALL,\n  LEADING,\n  TRAILING,\n}\n\nconst enum PRINT_COMMENT_HINT {\n  SKIP,\n  ALLOW,\n  DEFER,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  preserveFormat: boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  /**\n   * @deprecated Removed in Babel 8, syntax type is always 'hash'\n   */\n  recordAndTupleSyntaxType?: GeneratorOptions[\"recordAndTupleSyntaxType\"];\n  jsescOption: jsescOptions;\n  /**\n   * @deprecated Removed in Babel 8, use `jsescOption` instead\n   */\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: GeneratorOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer, occurrenceCount: number, last: boolean) => void;\n  iterator?: (node: t.Node, index: number) => void;\n  statement?: boolean;\n  indent?: boolean;\n  printTrailingSeparator?: boolean;\n}\n\nexport type PrintJoinOptions = PrintListOptions & PrintSequenceOptions;\nclass Printer {\n  constructor(\n    format: Format,\n    map: SourceMap,\n    tokens?: Token[],\n    originalCode?: string,\n  ) {\n    this.format = format;\n\n    this._tokens = tokens;\n    this._originalCode = originalCode;\n\n    this._indentRepeat = format.indent.style.length;\n\n    this._inputMap = map?._inputMap;\n\n    this._buf = new Buffer(map, format.indent.style[0]);\n  }\n  declare _inputMap: TraceMap;\n\n  declare format: Format;\n\n  inForStatementInit: boolean = false;\n  enterForStatementInit() {\n    if (this.inForStatementInit) return () => {};\n    this.inForStatementInit = true;\n    return () => {\n      this.inForStatementInit = false;\n    };\n  }\n\n  enterDelimited() {\n    const oldInForStatementInit = this.inForStatementInit;\n    const oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n    if (\n      oldInForStatementInit === false &&\n      oldNoLineTerminatorAfterNode === null\n    ) {\n      return () => {};\n    }\n    this.inForStatementInit = false;\n    this._noLineTerminatorAfterNode = null;\n    return () => {\n      this.inForStatementInit = oldInForStatementInit;\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    };\n  }\n\n  tokenContext: number = 0;\n\n  _tokens: Token[] = null;\n  _originalCode: string | null = null;\n\n  declare _buf: Buffer;\n  _currentNode: t.Node = null;\n  _indent: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _noLineTerminator: boolean = false;\n  _noLineTerminatorAfterNode: t.Node | null = null;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _endsWithDiv = false;\n  _lastCommentLine = 0;\n  _endsWithInnerRaw: boolean = false;\n  _indentInnerComments: boolean = true;\n  tokenMap: TokenMap = null;\n\n  _boundGetRawIdentifier = this._getRawIdentifier.bind(this);\n\n  generate(ast: t.Node) {\n    if (this.format.preserveFormat) {\n      this.tokenMap = new TokenMap(ast, this._tokens, this._originalCode);\n    }\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    const { format } = this;\n    if (format.preserveFormat || format.compact || format.concise) {\n      return;\n    }\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n      this._noLineTerminator = false;\n      return;\n    }\n    if (this.tokenMap) {\n      const node = this._currentNode;\n      if (node.start != null && node.end != null) {\n        if (!this.tokenMap.endMatches(node, \";\")) {\n          // no semicolon\n          return;\n        }\n        const indexes = this.tokenMap.getIndexes(this._currentNode);\n        this._catchUpTo(this._tokens[indexes[indexes.length - 1]].loc.start);\n      }\n    }\n    this._queue(charCodes.semicolon);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(node: t.Node): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\"}\");\n  }\n\n  rightParens(node: t.Node): void {\n    this.sourceWithOffset(\"end\", node.loc, -1);\n    this.token(\")\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    const { format } = this;\n    if (format.compact || format.preserveFormat) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string, noLineTerminatorAfter: boolean = false): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments(str);\n\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (this._endsWithDiv && str.charCodeAt(0) === charCodes.slash)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n    this._noLineTerminator = noLineTerminatorAfter;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string, number?: number): void {\n    // const NON_DECIMAL_LITERAL = /^0[box]/;\n    function isNonDecimalLiteral(str: string) {\n      if (str.length > 2 && str.charCodeAt(0) === charCodes.digit0) {\n        const secondChar = str.charCodeAt(1);\n        return (\n          secondChar === charCodes.lowercaseB ||\n          secondChar === charCodes.lowercaseO ||\n          secondChar === charCodes.lowercaseX\n        );\n      }\n      return false;\n    }\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(number) &&\n      !isNonDecimalLiteral(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   *\n   * @param {string} str The string to append.\n   * @param {boolean} [maybeNewline=false] Wether `str` might potentially\n   *    contain a line terminator or not.\n   * @param {number} [occurrenceCount=0] The occurrence count of this token in\n   *    the current node. This is used when printing in `preserveFormat` mode,\n   *    to know which token we should map to (for example, to disambiguate the\n   *    commas in an array literal).\n   */\n  token(str: string, maybeNewline = false, occurrenceCount = 0): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments(str, occurrenceCount);\n\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark &&\n        // space is mandatory to avoid outputting <!--\n        // http://javascript.spec.whatwg.org/#comment-syntax\n        (str === \"--\" ||\n          // Needs spaces to avoid changing a! == 0 to a!== 0\n          strFirst === charCodes.equalsTo)) ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline, occurrenceCount);\n    this._noLineTerminator = false;\n  }\n\n  tokenChar(char: number): void {\n    this.tokenContext = 0;\n\n    this._maybePrintInnerComments(String.fromCharCode(char));\n\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n    this._noLineTerminator = false;\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) {\n      cb();\n      return;\n    }\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!loc || this.format.preserveFormat) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, columnOffset);\n  }\n\n  sourceIdentifierName(identifierName: string, pos?: Pos): void {\n    if (!this._buf._canMarkIdName) return;\n\n    const sourcePosition = this._buf._sourcePosition;\n    sourcePosition.identifierNamePos = pos;\n    sourcePosition.identifierName = identifierName;\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(\n    str: string,\n    maybeNewline: boolean,\n    occurrenceCount: number = 0,\n  ): void {\n    if (this.tokenMap) {\n      const token = this.tokenMap.findMatching(\n        this._currentNode,\n        str,\n        occurrenceCount,\n      );\n      if (token) this._catchUpTo(token.loc.start);\n    }\n\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _appendChar(char: number): void {\n    if (this.tokenMap) {\n      const token = this.tokenMap.findMatching(\n        this._currentNode,\n        String.fromCharCode(char),\n      );\n      if (token) this._catchUpTo(token.loc.start);\n    }\n\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    // callers are expected to then set these to `true` when needed\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n    this._endsWithDiv = false;\n  }\n\n  _queue(char: number) {\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    const { format } = this;\n    if (!format.preserveFormat) {\n      if (format.retainLines && loc?.[prop]) {\n        this.catchUp(loc[prop].line);\n      }\n      return;\n    }\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc?.[prop];\n    if (pos != null) this._catchUpTo(pos);\n  }\n\n  _catchUpTo({ line, column, index }: Pos) {\n    const count = line - this._buf.getCurrentLine();\n    if (count > 0 && this._noLineTerminator) {\n      // We cannot inject new lines when _noLineTemrinator is set\n      // to `true`, or we would generate invalid code.\n      return;\n    }\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n\n    const spacesCount =\n      count > 0 ? column : column - this._buf.getCurrentColumn();\n    if (spacesCount > 0) {\n      const spaces = this._originalCode\n        ? this._originalCode\n            .slice(index - spacesCount, index)\n            // https://tc39.es/ecma262/#sec-white-space\n            .replace(/[^\\t\\v\\f\\uFEFF\\p{Space_Separator}]/gu, \" \")\n        : \" \".repeat(spacesCount);\n      this._append(spaces, false);\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    this._noLineTerminator = true;\n    this.print(node);\n  }\n\n  print(\n    node: t.Node | null,\n    noLineTerminatorAfter?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminatorAfter||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!node) return;\n\n    this._endsWithInnerRaw = false;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    const parent = this._currentNode;\n    this._currentNode = node;\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == null;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    const parenthesized = node.extra?.parenthesized as boolean | undefined;\n    let shouldPrintParens =\n      (parenthesized && format.preserveFormat) ||\n      (parenthesized &&\n        format.retainFunctionParens &&\n        nodeType === \"FunctionExpression\") ||\n      needsParens(\n        node,\n        parent,\n        this.tokenContext,\n        this.inForStatementInit,\n        format.preserveFormat ? this._boundGetRawIdentifier : undefined,\n      );\n\n    if (\n      !shouldPrintParens &&\n      parenthesized &&\n      node.leadingComments?.length &&\n      node.leadingComments[0].type === \"CommentBlock\"\n    ) {\n      const parentType = parent?.type;\n      switch (parentType) {\n        case \"ExpressionStatement\":\n        case \"VariableDeclarator\":\n        case \"AssignmentExpression\":\n        case \"ReturnStatement\":\n          break;\n        case \"CallExpression\":\n        case \"OptionalCallExpression\":\n        case \"NewExpression\":\n          if (parent.callee !== node) break;\n        // falls through\n        default:\n          shouldPrintParens = true;\n      }\n    }\n\n    let indentParenthesized = false;\n    if (\n      !shouldPrintParens &&\n      this._noLineTerminator &&\n      (node.leadingComments?.some(commentIsNewline) ||\n        (this.format.retainLines &&\n          node.loc &&\n          node.loc.start.line > this._buf.getCurrentLine()))\n    ) {\n      shouldPrintParens = true;\n      indentParenthesized = true;\n    }\n\n    let oldNoLineTerminatorAfterNode;\n    let oldInForStatementInitWasTrue;\n    if (!shouldPrintParens) {\n      noLineTerminatorAfter ||=\n        parent &&\n        this._noLineTerminatorAfterNode === parent &&\n        n.isLastChild(parent, node);\n      if (noLineTerminatorAfter) {\n        if (node.trailingComments?.some(commentIsNewline)) {\n          if (isExpression(node)) shouldPrintParens = true;\n        } else {\n          oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n          this._noLineTerminatorAfterNode = node;\n        }\n      }\n    }\n\n    if (shouldPrintParens) {\n      this.token(\"(\");\n      if (indentParenthesized) this.indent();\n      this._endsWithInnerRaw = false;\n      if (this.inForStatementInit) {\n        oldInForStatementInitWasTrue = true;\n        this.inForStatementInit = false;\n      }\n      oldNoLineTerminatorAfterNode = this._noLineTerminatorAfterNode;\n      this._noLineTerminatorAfterNode = null;\n    }\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node, parent);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(\n      loc,\n      // @ts-expect-error Expected 1 arguments, but got 3.\n      printMethod.bind(this, node, parent),\n    );\n\n    if (shouldPrintParens) {\n      this._printTrailingComments(node, parent);\n      if (indentParenthesized) {\n        this.dedent();\n        this.newline();\n      }\n      this.token(\")\");\n      this._noLineTerminator = noLineTerminatorAfter;\n      if (oldInForStatementInitWasTrue) this.inForStatementInit = true;\n    } else if (noLineTerminatorAfter && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node, parent);\n    } else {\n      this._printTrailingComments(node, parent, trailingCommentsLineOffset);\n    }\n\n    // end\n    this._currentNode = parent;\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n\n    if (oldNoLineTerminatorAfterNode !== undefined) {\n      this._noLineTerminatorAfterNode = oldNoLineTerminatorAfterNode;\n    }\n\n    this._endsWithInnerRaw = false;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra?.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    let { indent } = opts;\n\n    if (indent == null && this.format.retainLines) {\n      const startLine = nodes[0].loc?.start.line;\n      if (startLine != null && startLine !== this._buf.getCurrentLine()) {\n        indent = true;\n      }\n    }\n\n    if (indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (separator != null) {\n        if (i < len - 1) separator(i, false);\n        else if (opts.printTrailingSeparator) separator(i, true);\n      }\n\n      if (opts.statement) {\n        if (!node.trailingComments?.length) {\n          this._lastCommentLine = 0;\n        }\n\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node);\n  }\n\n  _printTrailingComments(node: t.Node, parent?: t.Node, lineOffset?: number) {\n    const { innerComments, trailingComments } = node;\n    // We print inner comments here, so that if for some reason they couldn't\n    // be printed in earlier locations they are still printed *somewhere*,\n    // even if at the end of the node.\n    if (innerComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        innerComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n    if (trailingComments?.length) {\n      this._printComments(\n        COMMENT_TYPE.TRAILING,\n        trailingComments,\n        node,\n        parent,\n        lineOffset,\n      );\n    }\n  }\n\n  _printLeadingComments(node: t.Node, parent: t.Node) {\n    const comments = node.leadingComments;\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node, parent);\n  }\n\n  _maybePrintInnerComments(\n    nextTokenStr: string,\n    nextTokenOccurrenceCount?: number,\n  ) {\n    if (this._endsWithInnerRaw) {\n      this.printInnerComments(\n        this.tokenMap?.findMatching(\n          this._currentNode,\n          nextTokenStr,\n          nextTokenOccurrenceCount,\n        ),\n      );\n    }\n    this._endsWithInnerRaw = true;\n    this._indentInnerComments = true;\n  }\n\n  printInnerComments(nextToken?: Token) {\n    const node = this._currentNode;\n    const comments = node.innerComments;\n    if (!comments?.length) return;\n\n    const hasSpace = this.endsWith(charCodes.space);\n    const indent = this._indentInnerComments;\n    const printedCommentsCount = this._printedComments.size;\n    if (indent) this.indent();\n    this._printComments(\n      COMMENT_TYPE.INNER,\n      comments,\n      node,\n      undefined,\n      undefined,\n      nextToken,\n    );\n    if (hasSpace && printedCommentsCount !== this._printedComments.size) {\n      this.space();\n    }\n    if (indent) this.dedent();\n  }\n\n  noIndentInnerCommentsHere() {\n    this._indentInnerComments = false;\n  }\n\n  printSequence(nodes: t.Node[], opts: PrintSequenceOptions = {}) {\n    opts.statement = true;\n    opts.indent ??= false;\n    this.printJoin(nodes, opts);\n  }\n\n  printList(items: t.Node[], opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    this.printJoin(items, opts);\n  }\n\n  shouldPrintTrailingComma(listEnd: string): boolean | null {\n    if (!this.tokenMap) return null;\n\n    const listEndIndex = this.tokenMap.findLastIndex(this._currentNode, token =>\n      this.tokenMap.matchesOriginal(token, listEnd),\n    );\n    if (listEndIndex <= 0) return null;\n    return this.tokenMap.matchesOriginal(this._tokens[listEndIndex - 1], \",\");\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    const format = this.format;\n\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (format.retainLines || format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  // Returns `PRINT_COMMENT_HINT.DEFER` if the comment cannot be printed in this position due to\n  // line terminators, signaling that the print comments loop can stop and\n  // resume printing comments at the next possible position. This happens when\n  // printing inner comments, since if we have an inner comment with a multiline\n  // there is at least one inner position where line terminators are allowed.\n  _shouldPrintComment(\n    comment: t.Comment,\n    nextToken?: Token,\n  ): PRINT_COMMENT_HINT {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return PRINT_COMMENT_HINT.SKIP;\n\n    if (this._printedComments.has(comment)) return PRINT_COMMENT_HINT.SKIP;\n\n    if (\n      this._noLineTerminator &&\n      HAS_NEWLINE_OR_BlOCK_COMMENT_END.test(comment.value)\n    ) {\n      return PRINT_COMMENT_HINT.DEFER;\n    }\n\n    if (nextToken && this.tokenMap) {\n      const commentTok = this.tokenMap.find(\n        this._currentNode,\n        token => token.value === comment.value,\n      );\n      if (commentTok && commentTok.start > nextToken.start) {\n        return PRINT_COMMENT_HINT.DEFER;\n      }\n    }\n\n    this._printedComments.add(comment);\n\n    if (!this.format.shouldPrintComment(comment.value)) {\n      return PRINT_COMMENT_HINT.SKIP;\n    }\n\n    return PRINT_COMMENT_HINT.ALLOW;\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    const noLineTerminator = this._noLineTerminator;\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace &&\n      lastCharCode !== charCodes.leftParenthesis\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n        if (this.format.concise) {\n          val = val.replace(/\\n(?!$)/g, `\\n`);\n        } else {\n          let indentSize = this.format.retainLines\n            ? 0\n            : this._buf.getCurrentColumn();\n\n          if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n            indentSize += this._getIndent();\n          }\n\n          val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n        }\n      }\n    } else if (!noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      // It was a single-line comment, so it's guaranteed to not\n      // contain newlines and it can be safely printed as a block\n      // comment.\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid converting a / operator into a line comment by appending /* to it\n    if (this._endsWithDiv) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    parent?: t.Node,\n    lineOffset: number = 0,\n    nextToken?: Token,\n  ) {\n    const nodeLoc = node.loc;\n    const len = comments.length;\n    let hasLoc = !!nodeLoc;\n    const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n    const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n    let lastLine = 0;\n    let leadingCommentNewline = 0;\n\n    const maybeNewline = this._noLineTerminator\n      ? function () {}\n      : this.newline.bind(this);\n\n    for (let i = 0; i < len; i++) {\n      const comment = comments[i];\n\n      const shouldPrint = this._shouldPrintComment(comment, nextToken);\n      if (shouldPrint === PRINT_COMMENT_HINT.DEFER) {\n        hasLoc = false;\n        break;\n      }\n      if (hasLoc && comment.loc && shouldPrint === PRINT_COMMENT_HINT.ALLOW) {\n        const commentStartLine = comment.loc.start.line;\n        const commentEndLine = comment.loc.end.line;\n        if (type === COMMENT_TYPE.LEADING) {\n          let offset = 0;\n          if (i === 0) {\n            // Because currently we cannot handle blank lines before leading comments,\n            // we always wrap before and after multi-line comments.\n            if (\n              this._buf.hasContent() &&\n              (comment.type === \"CommentLine\" ||\n                commentStartLine !== commentEndLine)\n            ) {\n              offset = leadingCommentNewline = 1;\n            }\n          } else {\n            offset = commentStartLine - lastLine;\n          }\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(\n              Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n            );\n            lastLine = nodeStartLine;\n          }\n        } else if (type === COMMENT_TYPE.INNER) {\n          const offset =\n            commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n\n          if (i + 1 === len) {\n            maybeNewline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n            lastLine = nodeEndLine;\n          }\n        } else {\n          const offset =\n            commentStartLine - (i === 0 ? nodeEndLine - lineOffset : lastLine);\n          lastLine = commentEndLine;\n\n          maybeNewline(offset);\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n        }\n      } else {\n        hasLoc = false;\n        if (shouldPrint !== PRINT_COMMENT_HINT.ALLOW) {\n          continue;\n        }\n\n        if (len === 1) {\n          const singleLine = comment.loc\n            ? comment.loc.start.line === comment.loc.end.line\n            : !HAS_NEWLINE.test(comment.value);\n\n          const shouldSkipNewline =\n            singleLine &&\n            !isStatement(node) &&\n            !isClassBody(parent) &&\n            !isTSInterfaceBody(parent) &&\n            !isTSEnumDeclaration(parent);\n\n          if (type === COMMENT_TYPE.LEADING) {\n            this._printComment(\n              comment,\n              (shouldSkipNewline && node.type !== \"ObjectExpression\") ||\n                (singleLine && isFunction(parent, { body: node }))\n                ? COMMENT_SKIP_NEWLINE.ALL\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else if (shouldSkipNewline && type === COMMENT_TYPE.TRAILING) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.ALL);\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        } else if (\n          type === COMMENT_TYPE.INNER &&\n          !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n          node.type !== \"ClassBody\" &&\n          node.type !== \"TSInterfaceBody\"\n        ) {\n          // class X {\n          //   /*:: a: number*/\n          //   /*:: b: ?string*/\n          // }\n\n          this._printComment(\n            comment,\n            i === 0\n              ? COMMENT_SKIP_NEWLINE.LEADING\n              : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n          );\n        } else {\n          this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n        }\n      }\n    }\n\n    if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n      this._lastCommentLine = lastLine;\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer, occurrenceCount: number, last: boolean) {\n  this.token(\",\", false, occurrenceCount);\n  if (!last) this.space();\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,CAAA,GAAAD,OAAA;AAEA,IAAAE,EAAA,GAAAF,OAAA;AAUA,IAAAG,SAAA,GAAAH,OAAA;AAEA,IAAAI,kBAAA,GAAAJ,OAAA;AAA4D;EAX1DK,YAAY;EACZC,UAAU;EACVC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC;AAAmB,IAAAR,EAAA;AAerB,MAAMS,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,OAAO;AACpC,MAAMC,WAAW,GAAG,oBAAoB;AACxC,MAAMC,gCAAgC,GAAG,yBAAyB;AAElE,SAASC,gBAAgBA,CAACC,CAAY,EAAE;EACtC,OAAOA,CAAC,CAACC,IAAI,KAAK,aAAa,IAAIJ,WAAW,CAACK,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;AAC9D;AAEA,MAAM;EAAEC;AAAY,CAAC,GAAGnB,CAAC;AAmFzB,MAAMoB,OAAO,CAAC;EACZC,WAAWA,CACTC,MAAc,EACdC,GAAc,EACdC,MAAgB,EAChBC,YAAqB,EACrB;IAAA,KAgBFC,kBAAkB,GAAY,KAAK;IAAA,KA0BnCC,YAAY,GAAW,CAAC;IAAA,KAExBC,OAAO,GAAY,IAAI;IAAA,KACvBC,aAAa,GAAkB,IAAI;IAAA,KAGnCC,YAAY,GAAW,IAAI;IAAA,KAC3BC,OAAO,GAAW,CAAC;IAAA,KACnBC,aAAa,GAAW,CAAC;IAAA,KACzBC,UAAU,GAAY,KAAK;IAAA,KAC3BC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,0BAA0B,GAAkB,IAAI;IAAA,KAChDC,4BAA4B,GAAY,KAAK;IAAA,KAC7CC,gBAAgB,GAAG,IAAIC,GAAG,CAAY,CAAC;IAAA,KACvCC,gBAAgB,GAAG,KAAK;IAAA,KACxBC,aAAa,GAAG,KAAK;IAAA,KACrBC,YAAY,GAAG,KAAK;IAAA,KACpBC,gBAAgB,GAAG,CAAC;IAAA,KACpBC,iBAAiB,GAAY,KAAK;IAAA,KAClCC,oBAAoB,GAAY,IAAI;IAAA,KACpCC,QAAQ,GAAa,IAAI;IAAA,KAEzBC,sBAAsB,GAAG,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;IA/DxD,IAAI,CAAC1B,MAAM,GAAGA,MAAM;IAEpB,IAAI,CAACM,OAAO,GAAGJ,MAAM;IACrB,IAAI,CAACK,aAAa,GAAGJ,YAAY;IAEjC,IAAI,CAACO,aAAa,GAAGV,MAAM,CAAC2B,MAAM,CAACC,KAAK,CAACC,MAAM;IAE/C,IAAI,CAACC,SAAS,GAAG7B,GAAG,oBAAHA,GAAG,CAAE6B,SAAS;IAE/B,IAAI,CAACC,IAAI,GAAG,IAAIC,eAAM,CAAC/B,GAAG,EAAED,MAAM,CAAC2B,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrD;EAMAK,qBAAqBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC7B,kBAAkB,EAAE,OAAO,MAAM,CAAC,CAAC;IAC5C,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAC9B,OAAO,MAAM;MACX,IAAI,CAACA,kBAAkB,GAAG,KAAK;IACjC,CAAC;EACH;EAEA8B,cAAcA,CAAA,EAAG;IACf,MAAMC,qBAAqB,GAAG,IAAI,CAAC/B,kBAAkB;IACrD,MAAMgC,4BAA4B,GAAG,IAAI,CAACvB,0BAA0B;IACpE,IACEsB,qBAAqB,KAAK,KAAK,IAC/BC,4BAA4B,KAAK,IAAI,EACrC;MACA,OAAO,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAChC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACS,0BAA0B,GAAG,IAAI;IACtC,OAAO,MAAM;MACX,IAAI,CAACT,kBAAkB,GAAG+B,qBAAqB;MAC/C,IAAI,CAACtB,0BAA0B,GAAGuB,4BAA4B;IAChE,CAAC;EACH;EA0BAC,QAAQA,CAACC,GAAW,EAAE;IACpB,IAAI,IAAI,CAACtC,MAAM,CAACuC,cAAc,EAAE;MAC9B,IAAI,CAAChB,QAAQ,GAAG,IAAIiB,kBAAQ,CAACF,GAAG,EAAE,IAAI,CAAChC,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC;IACrE;IACA,IAAI,CAACkC,KAAK,CAACH,GAAG,CAAC;IACf,IAAI,CAACI,mBAAmB,CAAC,CAAC;IAE1B,OAAO,IAAI,CAACX,IAAI,CAACY,GAAG,CAAC,CAAC;EACxB;EAMAhB,MAAMA,CAAA,EAAS;IACb,MAAM;MAAE3B;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACuC,cAAc,IAAIvC,MAAM,CAAC4C,OAAO,IAAI5C,MAAM,CAAC6C,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAACpC,OAAO,EAAE;EAChB;EAMAqC,MAAMA,CAAA,EAAS;IACb,MAAM;MAAE9C;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAACuC,cAAc,IAAIvC,MAAM,CAAC4C,OAAO,IAAI5C,MAAM,CAAC6C,OAAO,EAAE;MAC7D;IACF;IAEA,IAAI,CAACpC,OAAO,EAAE;EAChB;EAMAsC,SAASA,CAACC,KAAc,GAAG,KAAK,EAAQ;IACtC,IAAI,CAACN,mBAAmB,CAAC,CAAC;IAC1B,IAAIM,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,GAAoB,CAAC;MACrC,IAAI,CAACrC,iBAAiB,GAAG,KAAK;MAC9B;IACF;IACA,IAAI,IAAI,CAACW,QAAQ,EAAE;MACjB,MAAM2B,IAAI,GAAG,IAAI,CAAC1C,YAAY;MAC9B,IAAI0C,IAAI,CAACC,KAAK,IAAI,IAAI,IAAID,IAAI,CAACE,GAAG,IAAI,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,CAAC7B,QAAQ,CAAC8B,UAAU,CAACH,IAAI,EAAE,GAAG,CAAC,EAAE;UAExC;QACF;QACA,MAAMI,OAAO,GAAG,IAAI,CAAC/B,QAAQ,CAACgC,UAAU,CAAC,IAAI,CAAC/C,YAAY,CAAC;QAC3D,IAAI,CAACgD,UAAU,CAAC,IAAI,CAAClD,OAAO,CAACgD,OAAO,CAACA,OAAO,CAACzB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC4B,GAAG,CAACN,KAAK,CAAC;MACtE;IACF;IACA,IAAI,CAACO,MAAM,GAAoB,CAAC;IAChC,IAAI,CAAC9C,iBAAiB,GAAG,KAAK;EAChC;EAMA+C,UAAUA,CAACT,IAAY,EAAQ;IAC7B,IAAI,IAAI,CAAClD,MAAM,CAAC4D,QAAQ,EAAE;MACxB,IAAI,CAAC7B,IAAI,CAAC8B,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEZ,IAAI,CAACO,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,IAAI,CAAC;EACjB;EAEAC,WAAWA,CAACd,IAAY,EAAQ;IAC9B,IAAI,CAACY,gBAAgB,CAAC,KAAK,EAAEZ,IAAI,CAACO,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACM,SAAK,GAAI,CAAC;EACjB;EAMAE,KAAKA,CAACjB,KAAc,GAAG,KAAK,EAAQ;IAClC,MAAM;MAAEhD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,CAAC4C,OAAO,IAAI5C,MAAM,CAACuC,cAAc,EAAE;IAE7C,IAAIS,KAAK,EAAE;MACT,IAAI,CAACkB,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI,IAAI,CAACnC,IAAI,CAACoC,UAAU,CAAC,CAAC,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACjC,IAAID,MAAM,OAAoB,IAAIA,MAAM,OAAuB,EAAE;QAC/D,IAAI,CAACF,MAAM,CAAC,CAAC;MACf;IACF;EACF;EAMAI,IAAIA,CAACC,GAAW,EAAEC,qBAA8B,GAAG,KAAK,EAAQ;IAC9D,IAAI,CAACnE,YAAY,GAAG,CAAC;IAErB,IAAI,CAACoE,wBAAwB,CAACF,GAAG,CAAC;IAGlC,IACE,IAAI,CAACrD,aAAa,IACjB,IAAI,CAACC,YAAY,IAAIoD,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAC5D;MACA,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAACxB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACiC,OAAO,CAACJ,GAAG,EAAE,KAAK,CAAC;IAExB,IAAI,CAACrD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACN,iBAAiB,GAAG4D,qBAAqB;EAChD;EAMAI,MAAMA,CAACL,GAAW,EAAEK,MAAe,EAAQ;IAEzC,SAASC,mBAAmBA,CAACN,GAAW,EAAE;MACxC,IAAIA,GAAG,CAAC1C,MAAM,GAAG,CAAC,IAAI0C,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,OAAqB,EAAE;QAC5D,MAAMI,UAAU,GAAGP,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;QACpC,OACEI,UAAU,OAAyB,IACnCA,UAAU,QAAyB,IACnCA,UAAU,QAAyB;MAEvC;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACR,IAAI,CAACC,GAAG,CAAC;IAId,IAAI,CAACtD,gBAAgB,GACnB8D,MAAM,CAACC,SAAS,CAACJ,MAAM,CAAC,IACxB,CAACC,mBAAmB,CAACN,GAAG,CAAC,IACzB,CAACnF,mBAAmB,CAACO,IAAI,CAAC4E,GAAG,CAAC,IAC9B,CAAClF,oBAAoB,CAACM,IAAI,CAAC4E,GAAG,CAAC,IAC/BA,GAAG,CAACG,UAAU,CAACH,GAAG,CAAC1C,MAAM,GAAG,CAAC,CAAC,OAAkB;EACpD;EAaAkC,KAAKA,CAACQ,GAAW,EAAEU,YAAY,GAAG,KAAK,EAAEC,eAAe,GAAG,CAAC,EAAQ;IAClE,IAAI,CAAC7E,YAAY,GAAG,CAAC;IAErB,IAAI,CAACoE,wBAAwB,CAACF,GAAG,EAAEW,eAAe,CAAC;IAEnD,MAAMC,QAAQ,GAAG,IAAI,CAACd,WAAW,CAAC,CAAC;IACnC,MAAMe,QAAQ,GAAGb,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;IAClC,IACGS,QAAQ,OAA8B,KAGpCZ,GAAG,KAAK,IAAI,IAEXa,QAAQ,OAAuB,CAAC,IAEnCA,QAAQ,OAAuB,IAAID,QAAQ,OAAwB,IACnEC,QAAQ,OAAmB,IAAID,QAAQ,OAAoB,IAE3DC,QAAQ,OAAkB,IAAI,IAAI,CAACnE,gBAAiB,EACrD;MACA,IAAI,CAACiD,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAACxB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACiC,OAAO,CAACJ,GAAG,EAAEU,YAAY,EAAEC,eAAe,CAAC;IAChD,IAAI,CAACtE,iBAAiB,GAAG,KAAK;EAChC;EAEAyE,SAASA,CAACC,IAAY,EAAQ;IAC5B,IAAI,CAACjF,YAAY,GAAG,CAAC;IAErB,IAAI,CAACoE,wBAAwB,CAACc,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC,CAAC;IAExD,MAAMH,QAAQ,GAAG,IAAI,CAACd,WAAW,CAAC,CAAC;IACnC,IAEGiB,IAAI,OAAuB,IAAIH,QAAQ,OAAuB,IAC9DG,IAAI,OAAmB,IAAIH,QAAQ,OAAoB,IAEvDG,IAAI,OAAkB,IAAI,IAAI,CAACrE,gBAAiB,EACjD;MACA,IAAI,CAACiD,MAAM,CAAC,CAAC;IACf;IAEA,IAAI,CAACxB,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACO,WAAW,CAACqC,IAAI,CAAC;IACtB,IAAI,CAAC1E,iBAAiB,GAAG,KAAK;EAChC;EAQA6E,OAAOA,CAACC,CAAS,GAAG,CAAC,EAAE1C,KAAe,EAAQ;IAC5C,IAAI0C,CAAC,IAAI,CAAC,EAAE;IAEZ,IAAI,CAAC1C,KAAK,EAAE;MACV,IAAI,IAAI,CAAChD,MAAM,CAAC2F,WAAW,IAAI,IAAI,CAAC3F,MAAM,CAAC4C,OAAO,EAAE;MAEpD,IAAI,IAAI,CAAC5C,MAAM,CAAC6C,OAAO,EAAE;QACvB,IAAI,CAACoB,KAAK,CAAC,CAAC;QACZ;MACF;IACF;IAEA,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC;IAEhBA,CAAC,IAAI,IAAI,CAAC3D,IAAI,CAAC6D,eAAe,CAAC,CAAC;IAEhC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjB;IAEA;EACF;EAEAC,QAAQA,CAACT,IAAY,EAAW;IAC9B,OAAO,IAAI,CAACjB,WAAW,CAAC,CAAC,KAAKiB,IAAI;EACpC;EAEAjB,WAAWA,CAAA,EAAW;IACpB,OAAO,IAAI,CAACtC,IAAI,CAACsC,WAAW,CAAC,CAAC;EAChC;EAEA2B,sBAAsBA,CAAA,EAAW;IAC/B,OAAO,IAAI,CAACjE,IAAI,CAACiE,sBAAsB,CAAC,CAAC;EAC3C;EAEAC,qBAAqBA,CAAA,EAAS;IAC5B,IAAI,CAAClE,IAAI,CAACkE,qBAAqB,CAAC,CAAC;EACnC;EAEAC,WAAWA,CAACzC,GAAoB,EAAE0C,EAAc,EAAE;IAChD,IAAI,CAAC1C,GAAG,EAAE;MACR0C,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,QAAQ,CAAC,OAAO,EAAE3C,GAAG,CAAC;IAE3B,IAAI,CAAC1B,IAAI,CAACmE,WAAW,CAACzC,GAAG,EAAE0C,EAAE,CAAC;EAChC;EAEAE,MAAMA,CAACC,IAAqB,EAAE7C,GAAoB,EAAQ;IACxD,IAAI,CAACA,GAAG,EAAE;IAEV,IAAI,CAAC2C,QAAQ,CAACE,IAAI,EAAE7C,GAAG,CAAC;IAExB,IAAI,CAAC1B,IAAI,CAACsE,MAAM,CAACC,IAAI,EAAE7C,GAAG,CAAC;EAC7B;EAEAK,gBAAgBA,CACdwC,IAAqB,EACrB7C,GAAoB,EACpB8C,YAAoB,EACd;IACN,IAAI,CAAC9C,GAAG,IAAI,IAAI,CAACzD,MAAM,CAACuC,cAAc,EAAE;IAExC,IAAI,CAAC6D,QAAQ,CAACE,IAAI,EAAE7C,GAAG,CAAC;IAExB,IAAI,CAAC1B,IAAI,CAAC+B,gBAAgB,CAACwC,IAAI,EAAE7C,GAAG,EAAE8C,YAAY,CAAC;EACrD;EAEAC,oBAAoBA,CAACC,cAAsB,EAAEC,GAAS,EAAQ;IAC5D,IAAI,CAAC,IAAI,CAAC3E,IAAI,CAAC4E,cAAc,EAAE;IAE/B,MAAMC,cAAc,GAAG,IAAI,CAAC7E,IAAI,CAAC8E,eAAe;IAChDD,cAAc,CAACE,iBAAiB,GAAGJ,GAAG;IACtCE,cAAc,CAACH,cAAc,GAAGA,cAAc;EAChD;EAEAvC,MAAMA,CAAA,EAAS;IACb,IAAI,CAACR,MAAM,GAAgB,CAAC;EAC9B;EAEAoC,QAAQA,CAAA,EAAS;IACf,IAAI,CAACpC,MAAM,GAAmB,CAAC;EACjC;EAEAiB,OAAOA,CACLJ,GAAW,EACXU,YAAqB,EACrBC,eAAuB,GAAG,CAAC,EACrB;IACN,IAAI,IAAI,CAAC3D,QAAQ,EAAE;MACjB,MAAMwC,KAAK,GAAG,IAAI,CAACxC,QAAQ,CAACwF,YAAY,CACtC,IAAI,CAACvG,YAAY,EACjB+D,GAAG,EACHW,eACF,CAAC;MACD,IAAInB,KAAK,EAAE,IAAI,CAACP,UAAU,CAACO,KAAK,CAACN,GAAG,CAACN,KAAK,CAAC;IAC7C;IAEA,IAAI,CAAC6D,YAAY,CAACzC,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC;IAEpC,IAAI,CAAC3C,IAAI,CAACkF,MAAM,CAAC1C,GAAG,EAAEU,YAAY,CAAC;IAGnC,IAAI,CAAC/D,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEA8B,WAAWA,CAACqC,IAAY,EAAQ;IAC9B,IAAI,IAAI,CAAC/D,QAAQ,EAAE;MACjB,MAAMwC,KAAK,GAAG,IAAI,CAACxC,QAAQ,CAACwF,YAAY,CACtC,IAAI,CAACvG,YAAY,EACjB+E,MAAM,CAACC,YAAY,CAACF,IAAI,CAC1B,CAAC;MACD,IAAIvB,KAAK,EAAE,IAAI,CAACP,UAAU,CAACO,KAAK,CAACN,GAAG,CAACN,KAAK,CAAC;IAC7C;IAEA,IAAI,CAAC6D,YAAY,CAAC1B,IAAI,CAAC;IAEvB,IAAI,CAACvD,IAAI,CAACmF,UAAU,CAAC5B,IAAI,CAAC;IAG1B,IAAI,CAACpE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,YAAY,GAAG,KAAK;EAC3B;EAEAuC,MAAMA,CAAC4B,IAAY,EAAE;IACnB,IAAI,CAAC0B,YAAY,CAAC1B,IAAI,CAAC;IAEvB,IAAI,CAACvD,IAAI,CAACoF,KAAK,CAAC7B,IAAI,CAAC;IAErB,IAAI,CAACpE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,gBAAgB,GAAG,KAAK;EAC/B;EAEA+F,YAAYA,CAACI,SAAiB,EAAQ;IAEpC,IACE,IAAI,CAAC3G,OAAO,IACZ2G,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,IAAI,CAAChE,IAAI,CAACsF,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC/C;EACF;EAEAC,aAAaA,CAACH,SAAiB,EAAE;IAE/B,IACE,IAAI,CAAC3G,OAAO,IACZ2G,SAAS,OAAuB,IAChC,IAAI,CAACrB,QAAQ,GAAmB,CAAC,EACjC;MACA,OAAO,IAAI;IACb;EACF;EAEAyB,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAAC,IAAI,CAACzH,MAAM,CAAC2F,WAAW,EAAE;IAG9B,MAAM+B,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC1F,IAAI,CAAC4F,cAAc,CAAC,CAAC;IAE/C,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;EACF;EAEAM,QAAQA,CAACE,IAAqB,EAAE7C,GAAS,EAAE;IACzC,MAAM;MAAEzD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAI,CAACA,MAAM,CAACuC,cAAc,EAAE;MAC1B,IAAIvC,MAAM,CAAC2F,WAAW,IAAIlC,GAAG,YAAHA,GAAG,CAAG6C,IAAI,CAAC,EAAE;QACrC,IAAI,CAACkB,OAAO,CAAC/D,GAAG,CAAC6C,IAAI,CAAC,CAACmB,IAAI,CAAC;MAC9B;MACA;IACF;IAGA,MAAMf,GAAG,GAAGjD,GAAG,oBAAHA,GAAG,CAAG6C,IAAI,CAAC;IACvB,IAAII,GAAG,IAAI,IAAI,EAAE,IAAI,CAAClD,UAAU,CAACkD,GAAG,CAAC;EACvC;EAEAlD,UAAUA,CAAC;IAAEiE,IAAI;IAAEG,MAAM;IAAEC;EAAW,CAAC,EAAE;IACvC,MAAMH,KAAK,GAAGD,IAAI,GAAG,IAAI,CAAC1F,IAAI,CAAC4F,cAAc,CAAC,CAAC;IAC/C,IAAID,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC9G,iBAAiB,EAAE;MAGvC;IACF;IAEA,KAAK,IAAI8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,EAAEhC,CAAC,EAAE,EAAE;MAC9B,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjB;IAEA,MAAMgC,WAAW,GACfJ,KAAK,GAAG,CAAC,GAAGE,MAAM,GAAGA,MAAM,GAAG,IAAI,CAAC7F,IAAI,CAACgG,gBAAgB,CAAC,CAAC;IAC5D,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,MAAME,MAAM,GAAG,IAAI,CAACzH,aAAa,GAC7B,IAAI,CAACA,aAAa,CACf0H,KAAK,CAACJ,KAAK,GAAGC,WAAW,EAAED,KAAK,CAAC,CAEjCK,OAAO,CAAC,+DAAsC,EAAE,GAAG,CAAC,GACvD,GAAG,CAACC,MAAM,CAACL,WAAW,CAAC;MAC3B,IAAI,CAACnD,OAAO,CAACqD,MAAM,EAAE,KAAK,CAAC;IAC7B;EACF;EAMAV,UAAUA,CAAA,EAAW;IACnB,OAAO,IAAI,CAAC5G,aAAa,GAAG,IAAI,CAACD,OAAO;EAC1C;EAEA2H,mBAAmBA,CAAClF,IAAY,EAAE;IAgBhC,IAAI,CAACtC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC6B,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAT,KAAKA,CACHS,IAAmB,EACnBsB,qBAA+B,EAG/B6D,0BAAmC,EACnC;IAAA,IAAAC,WAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACA,IAAI,CAACtF,IAAI,EAAE;IAEX,IAAI,CAAC7B,iBAAiB,GAAG,KAAK;IAE9B,MAAMoH,QAAQ,GAAGvF,IAAI,CAACxD,IAAI;IAC1B,MAAMM,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,MAAM0I,UAAU,GAAG1I,MAAM,CAAC6C,OAAO;IACjC,IAEEK,IAAI,CAACyF,QAAQ,EACb;MACA3I,MAAM,CAAC6C,OAAO,GAAG,IAAI;IACvB;IAEA,MAAM+F,WAAW,GACf,IAAI,CACFH,QAAQ,CAOT;IACH,IAAIG,WAAW,KAAKC,SAAS,EAAE;MAC7B,MAAM,IAAIC,cAAc,CACtB,wBAAwBC,IAAI,CAACC,SAAS,CACpCP,QACF,CAAC,qBAAqBM,IAAI,CAACC,SAAS,CAAC9F,IAAI,CAACnD,WAAW,CAACkJ,IAAI,CAAC,EAC7D,CAAC;IACH;IAEA,MAAMC,MAAM,GAAG,IAAI,CAAC1I,YAAY;IAChC,IAAI,CAACA,YAAY,GAAG0C,IAAI;IAExB,MAAMiG,QAAQ,GAAG,IAAI,CAACxI,UAAU;IAChC,IAAI,CAACA,UAAU,GAAGuC,IAAI,CAACO,GAAG,IAAI,IAAI;IAClC,IAAI,CAACf,mBAAmB,CAAC,IAAI,CAAC/B,UAAU,IAAI,CAACwI,QAAQ,CAAC;IAEtD,MAAMC,aAAa,IAAAd,WAAA,GAAGpF,IAAI,CAACmG,KAAK,qBAAVf,WAAA,CAAYc,aAAoC;IACtE,IAAIE,iBAAiB,GAClBF,aAAa,IAAIpJ,MAAM,CAACuC,cAAc,IACtC6G,aAAa,IACZpJ,MAAM,CAACuJ,oBAAoB,IAC3Bd,QAAQ,KAAK,oBAAqB,IACpC5I,WAAW,CACTqD,IAAI,EACJgG,MAAM,EACN,IAAI,CAAC7I,YAAY,EACjB,IAAI,CAACD,kBAAkB,EACvBJ,MAAM,CAACuC,cAAc,GAAG,IAAI,CAACf,sBAAsB,GAAGqH,SACxD,CAAC;IAEH,IACE,CAACS,iBAAiB,IAClBF,aAAa,KAAAb,qBAAA,GACbrF,IAAI,CAACsG,eAAe,aAApBjB,qBAAA,CAAsB1G,MAAM,IAC5BqB,IAAI,CAACsG,eAAe,CAAC,CAAC,CAAC,CAAC9J,IAAI,KAAK,cAAc,EAC/C;MACA,MAAM+J,UAAU,GAAGP,MAAM,oBAANA,MAAM,CAAExJ,IAAI;MAC/B,QAAQ+J,UAAU;QAChB,KAAK,qBAAqB;QAC1B,KAAK,oBAAoB;QACzB,KAAK,sBAAsB;QAC3B,KAAK,iBAAiB;UACpB;QACF,KAAK,gBAAgB;QACrB,KAAK,wBAAwB;QAC7B,KAAK,eAAe;UAClB,IAAIP,MAAM,CAACQ,MAAM,KAAKxG,IAAI,EAAE;QAE9B;UACEoG,iBAAiB,GAAG,IAAI;MAC5B;IACF;IAEA,IAAIK,mBAAmB,GAAG,KAAK;IAC/B,IACE,CAACL,iBAAiB,IAClB,IAAI,CAAC1I,iBAAiB,KACrB,CAAA4H,sBAAA,GAAAtF,IAAI,CAACsG,eAAe,aAApBhB,sBAAA,CAAsBoB,IAAI,CAACpK,gBAAgB,CAAC,IAC1C,IAAI,CAACQ,MAAM,CAAC2F,WAAW,IACtBzC,IAAI,CAACO,GAAG,IACRP,IAAI,CAACO,GAAG,CAACN,KAAK,CAACsE,IAAI,GAAG,IAAI,CAAC1F,IAAI,CAAC4F,cAAc,CAAC,CAAE,CAAC,EACtD;MACA2B,iBAAiB,GAAG,IAAI;MACxBK,mBAAmB,GAAG,IAAI;IAC5B;IAEA,IAAIvH,4BAA4B;IAChC,IAAIyH,4BAA4B;IAChC,IAAI,CAACP,iBAAiB,EAAE;MACtB9E,qBAAqB,KAArBA,qBAAqB,GACnB0E,MAAM,IACN,IAAI,CAACrI,0BAA0B,KAAKqI,MAAM,IAC1CxK,CAAC,CAACoL,WAAW,CAACZ,MAAM,EAAEhG,IAAI,CAAC;MAC7B,IAAIsB,qBAAqB,EAAE;QAAA,IAAAuF,qBAAA;QACzB,KAAAA,qBAAA,GAAI7G,IAAI,CAAC8G,gBAAgB,aAArBD,qBAAA,CAAuBH,IAAI,CAACpK,gBAAgB,CAAC,EAAE;UACjD,IAAIV,YAAY,CAACoE,IAAI,CAAC,EAAEoG,iBAAiB,GAAG,IAAI;QAClD,CAAC,MAAM;UACLlH,4BAA4B,GAAG,IAAI,CAACvB,0BAA0B;UAC9D,IAAI,CAACA,0BAA0B,GAAGqC,IAAI;QACxC;MACF;IACF;IAEA,IAAIoG,iBAAiB,EAAE;MACrB,IAAI,CAACvF,SAAK,GAAI,CAAC;MACf,IAAI4F,mBAAmB,EAAE,IAAI,CAAChI,MAAM,CAAC,CAAC;MACtC,IAAI,CAACN,iBAAiB,GAAG,KAAK;MAC9B,IAAI,IAAI,CAACjB,kBAAkB,EAAE;QAC3ByJ,4BAA4B,GAAG,IAAI;QACnC,IAAI,CAACzJ,kBAAkB,GAAG,KAAK;MACjC;MACAgC,4BAA4B,GAAG,IAAI,CAACvB,0BAA0B;MAC9D,IAAI,CAACA,0BAA0B,GAAG,IAAI;IACxC;IAEA,IAAI,CAACO,gBAAgB,GAAG,CAAC;IAEzB,IAAI,CAAC6I,qBAAqB,CAAC/G,IAAI,EAAEgG,MAAM,CAAC;IAExC,MAAMzF,GAAG,GAAGgF,QAAQ,KAAK,SAAS,IAAIA,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAGvF,IAAI,CAACO,GAAG;IAE3E,IAAI,CAACyC,WAAW,CACdzC,GAAG,EAEHmF,WAAW,CAAClH,IAAI,CAAC,IAAI,EAAEwB,IAAI,EAAEgG,MAAM,CACrC,CAAC;IAED,IAAII,iBAAiB,EAAE;MACrB,IAAI,CAACY,sBAAsB,CAAChH,IAAI,EAAEgG,MAAM,CAAC;MACzC,IAAIS,mBAAmB,EAAE;QACvB,IAAI,CAAC7G,MAAM,CAAC,CAAC;QACb,IAAI,CAAC2C,OAAO,CAAC,CAAC;MAChB;MACA,IAAI,CAAC1B,SAAK,GAAI,CAAC;MACf,IAAI,CAACnD,iBAAiB,GAAG4D,qBAAqB;MAC9C,IAAIqF,4BAA4B,EAAE,IAAI,CAACzJ,kBAAkB,GAAG,IAAI;IAClE,CAAC,MAAM,IAAIoE,qBAAqB,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,EAAE;MAC3D,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACsJ,sBAAsB,CAAChH,IAAI,EAAEgG,MAAM,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACgB,sBAAsB,CAAChH,IAAI,EAAEgG,MAAM,EAAEb,0BAA0B,CAAC;IACvE;IAGA,IAAI,CAAC7H,YAAY,GAAG0I,MAAM;IAC1BlJ,MAAM,CAAC6C,OAAO,GAAG6F,UAAU;IAC3B,IAAI,CAAC/H,UAAU,GAAGwI,QAAQ;IAE1B,IAAI/G,4BAA4B,KAAKyG,SAAS,EAAE;MAC9C,IAAI,CAAChI,0BAA0B,GAAGuB,4BAA4B;IAChE;IAEA,IAAI,CAACf,iBAAiB,GAAG,KAAK;EAChC;EAEAqB,mBAAmBA,CAACyH,uBAAiC,EAAE;IACrD,IAAIA,uBAAuB,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACzJ,UAAU,EAAE,IAAI,CAAC0J,qBAAqB,CAAC,CAAC;EACpD;EAEAD,sBAAsBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACtJ,4BAA4B,EAAE;IACvC,IAAI,CAACA,4BAA4B,GAAG,IAAI;IAExC,MAAMwJ,OAAO,GAAG,IAAI,CAACtK,MAAM,CAACuK,sBAAsB;IAClD,IAAID,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACE9K,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE0K;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAD,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACvJ,4BAA4B,EAAE;IACxC,IAAI,CAACA,4BAA4B,GAAG,KAAK;IAEzC,MAAMwJ,OAAO,GAAG,IAAI,CAACtK,MAAM,CAACyK,qBAAqB;IACjD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACE,aAAa,CAChB;QACE9K,IAAI,EAAE,cAAc;QACpBE,KAAK,EAAE0K;MACT,CAAC,GAEH,CAAC;IACH;EACF;EAEAI,cAAcA,CACZxH,IAKa,EACO;IACpB,MAAMmG,KAAK,GAAGnG,IAAI,CAACmG,KAAK;IACxB,IACE,CAAAA,KAAK,oBAALA,KAAK,CAAEsB,GAAG,KAAI,IAAI,IAClBtB,KAAK,CAACuB,QAAQ,IAAI,IAAI,IACtB1H,IAAI,CAACtD,KAAK,KAAKyJ,KAAK,CAACuB,QAAQ,EAC7B;MAEA,OAAOvB,KAAK,CAACsB,GAAG;IAClB;EACF;EAEAE,SAASA,CACPC,KAAuC,EACvCC,IAAsB,GAAG,CAAC,CAAC,EAC3B;IACA,IAAI,EAACD,KAAK,YAALA,KAAK,CAAEjJ,MAAM,GAAE;IAEpB,IAAI;MAAEF;IAAO,CAAC,GAAGoJ,IAAI;IAErB,IAAIpJ,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC3B,MAAM,CAAC2F,WAAW,EAAE;MAAA,IAAAqF,YAAA;MAC7C,MAAMC,SAAS,IAAAD,YAAA,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACrH,GAAG,qBAAZuH,YAAA,CAAc7H,KAAK,CAACsE,IAAI;MAC1C,IAAIwD,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,IAAI,CAAClJ,IAAI,CAAC4F,cAAc,CAAC,CAAC,EAAE;QACjEhG,MAAM,GAAG,IAAI;MACf;IACF;IAEA,IAAIA,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IAEzB,MAAMuJ,WAA+B,GAAG;MACtCC,WAAW,EAAEJ,IAAI,CAACI,WAAW;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;IAED,MAAMC,SAAS,GAAGN,IAAI,CAACM,SAAS,GAAGN,IAAI,CAACM,SAAS,CAAC3J,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;IAEnE,MAAM4J,GAAG,GAAGR,KAAK,CAACjJ,MAAM;IACxB,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,GAAG,EAAE5F,CAAC,EAAE,EAAE;MAC5B,MAAMxC,IAAI,GAAG4H,KAAK,CAACpF,CAAC,CAAC;MACrB,IAAI,CAACxC,IAAI,EAAE;MAEX,IAAI6H,IAAI,CAACQ,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC9F,CAAC,KAAK,CAAC,EAAEwF,WAAW,CAAC;MAE5D,IAAI,CAACzI,KAAK,CAACS,IAAI,EAAE2F,SAAS,EAAEkC,IAAI,CAAC1C,0BAA0B,IAAI,CAAC,CAAC;MAEjE0C,IAAI,CAACU,QAAQ,YAAbV,IAAI,CAACU,QAAQ,CAAGvI,IAAI,EAAEwC,CAAC,CAAC;MAExB,IAAI2F,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI3F,CAAC,GAAG4F,GAAG,GAAG,CAAC,EAAED,SAAS,CAAC3F,CAAC,EAAE,KAAK,CAAC,CAAC,KAChC,IAAIqF,IAAI,CAACW,sBAAsB,EAAEL,SAAS,CAAC3F,CAAC,EAAE,IAAI,CAAC;MAC1D;MAEA,IAAIqF,IAAI,CAACQ,SAAS,EAAE;QAAA,IAAAI,sBAAA;QAClB,IAAI,GAAAA,sBAAA,GAACzI,IAAI,CAAC8G,gBAAgB,aAArB2B,sBAAA,CAAuB9J,MAAM,GAAE;UAClC,IAAI,CAACT,gBAAgB,GAAG,CAAC;QAC3B;QAEA,IAAIsE,CAAC,GAAG,CAAC,KAAK4F,GAAG,EAAE;UACjB,IAAI,CAAC7F,OAAO,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAAmG,aAAA;UACL,MAAMC,QAAQ,GAAGf,KAAK,CAACpF,CAAC,GAAG,CAAC,CAAC;UAC7BwF,WAAW,CAACE,iBAAiB,GAAG,EAAAQ,aAAA,GAAAC,QAAQ,CAACpI,GAAG,qBAAZmI,aAAA,CAAczI,KAAK,CAACsE,IAAI,KAAI,CAAC;UAE7D,IAAI,CAAC+D,aAAa,CAAC,IAAI,EAAEN,WAAW,CAAC;QACvC;MACF;IACF;IAEA,IAAIvJ,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEAgJ,wBAAwBA,CAAC5I,IAAY,EAAE;IACrC,MAAMvB,MAAM,GAAGuB,IAAI,CAACsG,eAAe,IAAItG,IAAI,CAACsG,eAAe,CAAC3H,MAAM,GAAG,CAAC;IACtE,IAAIF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACc,KAAK,CAACS,IAAI,CAAC;IAChB,IAAIvB,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEAiJ,UAAUA,CAAC7C,MAA8C,EAAE;IACzD,MAAMhG,IAAI,GAAGgG,MAAM,CAAC8C,IAAI;IAExB,IAAI9I,IAAI,CAACxD,IAAI,KAAK,gBAAgB,EAAE;MAClC,IAAI,CAACuE,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAACxB,KAAK,CAACS,IAAI,CAAC;EAClB;EAEAgH,sBAAsBA,CAAChH,IAAY,EAAEgG,MAAe,EAAE+C,UAAmB,EAAE;IACzE,MAAM;MAAEC,aAAa;MAAElC;IAAiB,CAAC,GAAG9G,IAAI;IAIhD,IAAIgJ,aAAa,YAAbA,aAAa,CAAErK,MAAM,EAAE;MACzB,IAAI,CAACsK,cAAc,IAEjBD,aAAa,EACbhJ,IAAI,EACJgG,MAAM,EACN+C,UACF,CAAC;IACH;IACA,IAAIjC,gBAAgB,YAAhBA,gBAAgB,CAAEnI,MAAM,EAAE;MAC5B,IAAI,CAACsK,cAAc,IAEjBnC,gBAAgB,EAChB9G,IAAI,EACJgG,MAAM,EACN+C,UACF,CAAC;IACH;EACF;EAEAhC,qBAAqBA,CAAC/G,IAAY,EAAEgG,MAAc,EAAE;IAClD,MAAMkD,QAAQ,GAAGlJ,IAAI,CAACsG,eAAe;IACrC,IAAI,EAAC4C,QAAQ,YAARA,QAAQ,CAAEvK,MAAM,GAAE;IACvB,IAAI,CAACsK,cAAc,IAAuBC,QAAQ,EAAElJ,IAAI,EAAEgG,MAAM,CAAC;EACnE;EAEAzE,wBAAwBA,CACtB4H,YAAoB,EACpBC,wBAAiC,EACjC;IACA,IAAI,IAAI,CAACjL,iBAAiB,EAAE;MAAA,IAAAkL,cAAA;MAC1B,IAAI,CAACC,kBAAkB,EAAAD,cAAA,GACrB,IAAI,CAAChL,QAAQ,qBAAbgL,cAAA,CAAexF,YAAY,CACzB,IAAI,CAACvG,YAAY,EACjB6L,YAAY,EACZC,wBACF,CACF,CAAC;IACH;IACA,IAAI,CAACjL,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,oBAAoB,GAAG,IAAI;EAClC;EAEAkL,kBAAkBA,CAACC,SAAiB,EAAE;IACpC,MAAMvJ,IAAI,GAAG,IAAI,CAAC1C,YAAY;IAC9B,MAAM4L,QAAQ,GAAGlJ,IAAI,CAACgJ,aAAa;IACnC,IAAI,EAACE,QAAQ,YAARA,QAAQ,CAAEvK,MAAM,GAAE;IAEvB,MAAM6K,QAAQ,GAAG,IAAI,CAAC3G,QAAQ,GAAgB,CAAC;IAC/C,MAAMpE,MAAM,GAAG,IAAI,CAACL,oBAAoB;IACxC,MAAMqL,oBAAoB,GAAG,IAAI,CAAC5L,gBAAgB,CAAC6L,IAAI;IACvD,IAAIjL,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC;IACzB,IAAI,CAACwK,cAAc,IAEjBC,QAAQ,EACRlJ,IAAI,EACJ2F,SAAS,EACTA,SAAS,EACT4D,SACF,CAAC;IACD,IAAIC,QAAQ,IAAIC,oBAAoB,KAAK,IAAI,CAAC5L,gBAAgB,CAAC6L,IAAI,EAAE;MACnE,IAAI,CAAC3I,KAAK,CAAC,CAAC;IACd;IACA,IAAItC,MAAM,EAAE,IAAI,CAACmB,MAAM,CAAC,CAAC;EAC3B;EAEA+J,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAACvL,oBAAoB,GAAG,KAAK;EACnC;EAEAwL,aAAaA,CAAChC,KAAe,EAAEC,IAA0B,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAgC,YAAA;IAC9DhC,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,CAAAwB,YAAA,GAAAhC,IAAI,CAACpJ,MAAM,YAAAoL,YAAA,GAAXhC,IAAI,CAACpJ,MAAM,GAAK,KAAK;IACrB,IAAI,CAACkJ,SAAS,CAACC,KAAK,EAAEC,IAAI,CAAC;EAC7B;EAEAiC,SAASA,CAACC,KAAe,EAAElC,IAAsB,GAAG,CAAC,CAAC,EAAE;IACtD,IAAIA,IAAI,CAACM,SAAS,IAAI,IAAI,EAAE;MAC1BN,IAAI,CAACM,SAAS,GAAG6B,cAAc;IACjC;IAEA,IAAI,CAACrC,SAAS,CAACoC,KAAK,EAAElC,IAAI,CAAC;EAC7B;EAEAoC,wBAAwBA,CAACC,OAAe,EAAkB;IACxD,IAAI,CAAC,IAAI,CAAC7L,QAAQ,EAAE,OAAO,IAAI;IAE/B,MAAM8L,YAAY,GAAG,IAAI,CAAC9L,QAAQ,CAAC+L,aAAa,CAAC,IAAI,CAAC9M,YAAY,EAAEuD,KAAK,IACvE,IAAI,CAACxC,QAAQ,CAACgM,eAAe,CAACxJ,KAAK,EAAEqJ,OAAO,CAC9C,CAAC;IACD,IAAIC,YAAY,IAAI,CAAC,EAAE,OAAO,IAAI;IAClC,OAAO,IAAI,CAAC9L,QAAQ,CAACgM,eAAe,CAAC,IAAI,CAACjN,OAAO,CAAC+M,YAAY,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAC3E;EAEA7B,aAAaA,CAACgC,OAAgB,EAAEzC,IAAwB,EAAE;IACxD,MAAM/K,MAAM,GAAG,IAAI,CAACA,MAAM;IAG1B,IAAIA,MAAM,CAAC2F,WAAW,IAAI3F,MAAM,CAAC4C,OAAO,EAAE;IAI1C,IAAI5C,MAAM,CAAC6C,OAAO,EAAE;MAClB,IAAI,CAACoB,KAAK,CAAC,CAAC;MACZ;IACF;IAEA,IAAI,CAACuJ,OAAO,EAAE;MACZ;IACF;IAEA,MAAMvC,SAAS,GAAGF,IAAI,CAACK,iBAAiB;IACxC,MAAMqC,eAAe,GAAG,IAAI,CAACrM,gBAAgB;IAC7C,IAAI6J,SAAS,GAAG,CAAC,IAAIwC,eAAe,GAAG,CAAC,EAAE;MACxC,MAAMC,MAAM,GAAGzC,SAAS,GAAGwC,eAAe;MAC1C,IAAIC,MAAM,IAAI,CAAC,EAAE;QACf,IAAI,CAACjI,OAAO,CAACiI,MAAM,IAAI,CAAC,CAAC;QACzB;MACF;IACF;IAGA,IAAI,IAAI,CAAC3L,IAAI,CAACoC,UAAU,CAAC,CAAC,EAAE;MAa1B,IAAI,CAACsB,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAOAkI,mBAAmBA,CACjBrD,OAAkB,EAClBmC,SAAiB,EACG;IAGpB,IAAInC,OAAO,CAACsD,MAAM,EAAE;IAEpB,IAAI,IAAI,CAAC7M,gBAAgB,CAAC8M,GAAG,CAACvD,OAAO,CAAC,EAAE;IAExC,IACE,IAAI,CAAC1J,iBAAiB,IACtBrB,gCAAgC,CAACI,IAAI,CAAC2K,OAAO,CAAC1K,KAAK,CAAC,EACpD;MACA;IACF;IAEA,IAAI6M,SAAS,IAAI,IAAI,CAAClL,QAAQ,EAAE;MAC9B,MAAMuM,UAAU,GAAG,IAAI,CAACvM,QAAQ,CAACwM,IAAI,CACnC,IAAI,CAACvN,YAAY,EACjBuD,KAAK,IAAIA,KAAK,CAACnE,KAAK,KAAK0K,OAAO,CAAC1K,KACnC,CAAC;MACD,IAAIkO,UAAU,IAAIA,UAAU,CAAC3K,KAAK,GAAGsJ,SAAS,CAACtJ,KAAK,EAAE;QACpD;MACF;IACF;IAEA,IAAI,CAACpC,gBAAgB,CAACiN,GAAG,CAAC1D,OAAO,CAAC;IAElC,IAAI,CAAC,IAAI,CAACtK,MAAM,CAACiO,kBAAkB,CAAC3D,OAAO,CAAC1K,KAAK,CAAC,EAAE;MAClD;IACF;IAEA;EACF;EAEA4K,aAAaA,CAACF,OAAkB,EAAE4D,YAAkC,EAAE;IACpE,MAAMC,gBAAgB,GAAG,IAAI,CAACvN,iBAAiB;IAC/C,MAAMwN,cAAc,GAAG9D,OAAO,CAAC5K,IAAI,KAAK,cAAc;IAItD,MAAM2O,aAAa,GACjBD,cAAc,IACdF,YAAY,MAA6B,IACzC,CAAC,IAAI,CAACtN,iBAAiB;IAEzB,IACEyN,aAAa,IACb,IAAI,CAACtM,IAAI,CAACoC,UAAU,CAAC,CAAC,IACtB+J,YAAY,MAAiC,EAC7C;MACA,IAAI,CAACzI,OAAO,CAAC,CAAC,CAAC;IACjB;IAEA,MAAM6I,YAAY,GAAG,IAAI,CAACjK,WAAW,CAAC,CAAC;IACvC,IACEiK,YAAY,OAAgC,IAC5CA,YAAY,QAA6B,IACzCA,YAAY,OAA8B,EAC1C;MACA,IAAI,CAACrK,KAAK,CAAC,CAAC;IACd;IAEA,IAAIsK,GAAG;IACP,IAAIH,cAAc,EAAE;MAClBG,GAAG,GAAG,KAAKjE,OAAO,CAAC1K,KAAK,IAAI;MAC5B,IAAI,IAAI,CAACI,MAAM,CAAC2B,MAAM,CAAC6M,sBAAsB,EAAE;QAAA,IAAAC,YAAA;QAC7C,MAAMf,MAAM,IAAAe,YAAA,GAAGnE,OAAO,CAAC7G,GAAG,qBAAXgL,YAAA,CAAatL,KAAK,CAACyE,MAAM;QACxC,IAAI8F,MAAM,EAAE;UACV,MAAMgB,YAAY,GAAG,IAAIC,MAAM,CAAC,WAAW,GAAGjB,MAAM,GAAG,GAAG,EAAE,GAAG,CAAC;UAChEa,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAACwG,YAAY,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,IAAI,CAAC1O,MAAM,CAAC6C,OAAO,EAAE;UACvB0L,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QACrC,CAAC,MAAM;UACL,IAAI0G,UAAU,GAAG,IAAI,CAAC5O,MAAM,CAAC2F,WAAW,GACpC,CAAC,GACD,IAAI,CAAC5D,IAAI,CAACgG,gBAAgB,CAAC,CAAC;UAEhC,IAAI,IAAI,CAACR,aAAa,GAAgB,CAAC,IAAI,IAAI,CAACvH,MAAM,CAAC2F,WAAW,EAAE;YAClEiJ,UAAU,IAAI,IAAI,CAACtH,UAAU,CAAC,CAAC;UACjC;UAEAiH,GAAG,GAAGA,GAAG,CAACrG,OAAO,CAAC,UAAU,EAAE,KAAK,GAAG,CAACC,MAAM,CAACyG,UAAU,CAAC,EAAE,CAAC;QAC9D;MACF;IACF,CAAC,MAAM,IAAI,CAACT,gBAAgB,EAAE;MAC5BI,GAAG,GAAG,KAAKjE,OAAO,CAAC1K,KAAK,EAAE;IAC5B,CAAC,MAAM;MAIL2O,GAAG,GAAG,KAAKjE,OAAO,CAAC1K,KAAK,IAAI;IAC9B;IAGA,IAAI,IAAI,CAACuB,YAAY,EAAE,IAAI,CAAC+C,MAAM,CAAC,CAAC;IAEpC,IAAI,CAACmC,MAAM,CAAC,OAAO,EAAEiE,OAAO,CAAC7G,GAAG,CAAC;IACjC,IAAI,CAACkB,OAAO,CAAC4J,GAAG,EAAEH,cAAc,CAAC;IAEjC,IAAI,CAACA,cAAc,IAAI,CAACD,gBAAgB,EAAE;MACxC,IAAI,CAAC1I,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACvB;IAEA,IAAI4I,aAAa,IAAIH,YAAY,MAAkC,EAAE;MACnE,IAAI,CAACzI,OAAO,CAAC,CAAC,CAAC;IACjB;EACF;EAEA0G,cAAcA,CACZzM,IAAkB,EAClB0M,QAA8B,EAC9BlJ,IAAY,EACZgG,MAAe,EACf+C,UAAkB,GAAG,CAAC,EACtBQ,SAAiB,EACjB;IACA,MAAMoC,OAAO,GAAG3L,IAAI,CAACO,GAAG;IACxB,MAAM6H,GAAG,GAAGc,QAAQ,CAACvK,MAAM;IAC3B,IAAIiN,MAAM,GAAG,CAAC,CAACD,OAAO;IACtB,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAAC1L,KAAK,CAACsE,IAAI,GAAG,CAAC;IACrD,MAAMuH,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACzL,GAAG,CAACqE,IAAI,GAAG,CAAC;IACjD,IAAIwH,QAAQ,GAAG,CAAC;IAChB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,MAAMjK,YAAY,GAAG,IAAI,CAACrE,iBAAiB,GACvC,YAAY,CAAC,CAAC,GACd,IAAI,CAAC6E,OAAO,CAAC/D,IAAI,CAAC,IAAI,CAAC;IAE3B,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,GAAG,EAAE5F,CAAC,EAAE,EAAE;MAC5B,MAAM4E,OAAO,GAAG8B,QAAQ,CAAC1G,CAAC,CAAC;MAE3B,MAAMyJ,WAAW,GAAG,IAAI,CAACxB,mBAAmB,CAACrD,OAAO,EAAEmC,SAAS,CAAC;MAChE,IAAI0C,WAAW,MAA6B,EAAE;QAC5CL,MAAM,GAAG,KAAK;QACd;MACF;MACA,IAAIA,MAAM,IAAIxE,OAAO,CAAC7G,GAAG,IAAI0L,WAAW,MAA6B,EAAE;QACrE,MAAMC,gBAAgB,GAAG9E,OAAO,CAAC7G,GAAG,CAACN,KAAK,CAACsE,IAAI;QAC/C,MAAM4H,cAAc,GAAG/E,OAAO,CAAC7G,GAAG,CAACL,GAAG,CAACqE,IAAI;QAC3C,IAAI/H,IAAI,MAAyB,EAAE;UACjC,IAAIgO,MAAM,GAAG,CAAC;UACd,IAAIhI,CAAC,KAAK,CAAC,EAAE;YAGX,IACE,IAAI,CAAC3D,IAAI,CAACoC,UAAU,CAAC,CAAC,KACrBmG,OAAO,CAAC5K,IAAI,KAAK,aAAa,IAC7B0P,gBAAgB,KAAKC,cAAc,CAAC,EACtC;cACA3B,MAAM,GAAGwB,qBAAqB,GAAG,CAAC;YACpC;UACF,CAAC,MAAM;YACLxB,MAAM,GAAG0B,gBAAgB,GAAGH,QAAQ;UACtC;UACAA,QAAQ,GAAGI,cAAc;UAEzBpK,YAAY,CAACyI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI5E,CAAC,GAAG,CAAC,KAAK4F,GAAG,EAAE;YACjBrG,YAAY,CACVqK,IAAI,CAACC,GAAG,CAACR,aAAa,GAAGE,QAAQ,EAAEC,qBAAqB,CAC1D,CAAC;YACDD,QAAQ,GAAGF,aAAa;UAC1B;QACF,CAAC,MAAM,IAAIrP,IAAI,MAAuB,EAAE;UACtC,MAAMgO,MAAM,GACV0B,gBAAgB,IAAI1J,CAAC,KAAK,CAAC,GAAGqJ,aAAa,GAAGE,QAAQ,CAAC;UACzDA,QAAQ,GAAGI,cAAc;UAEzBpK,YAAY,CAACyI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;UAErD,IAAI5E,CAAC,GAAG,CAAC,KAAK4F,GAAG,EAAE;YACjBrG,YAAY,CAACqK,IAAI,CAACE,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGC,QAAQ,CAAC,CAAC;YACjDA,QAAQ,GAAGD,WAAW;UACxB;QACF,CAAC,MAAM;UACL,MAAMtB,MAAM,GACV0B,gBAAgB,IAAI1J,CAAC,KAAK,CAAC,GAAGsJ,WAAW,GAAG/C,UAAU,GAAGgD,QAAQ,CAAC;UACpEA,QAAQ,GAAGI,cAAc;UAEzBpK,YAAY,CAACyI,MAAM,CAAC;UACpB,IAAI,CAAClD,aAAa,CAACF,OAAO,GAA0B,CAAC;QACvD;MACF,CAAC,MAAM;QACLwE,MAAM,GAAG,KAAK;QACd,IAAIK,WAAW,MAA6B,EAAE;UAC5C;QACF;QAEA,IAAI7D,GAAG,KAAK,CAAC,EAAE;UACb,MAAMmE,UAAU,GAAGnF,OAAO,CAAC7G,GAAG,GAC1B6G,OAAO,CAAC7G,GAAG,CAACN,KAAK,CAACsE,IAAI,KAAK6C,OAAO,CAAC7G,GAAG,CAACL,GAAG,CAACqE,IAAI,GAC/C,CAACnI,WAAW,CAACK,IAAI,CAAC2K,OAAO,CAAC1K,KAAK,CAAC;UAEpC,MAAM8P,iBAAiB,GACrBD,UAAU,IACV,CAACzQ,WAAW,CAACkE,IAAI,CAAC,IAClB,CAACjE,WAAW,CAACiK,MAAM,CAAC,IACpB,CAAChK,iBAAiB,CAACgK,MAAM,CAAC,IAC1B,CAAC/J,mBAAmB,CAAC+J,MAAM,CAAC;UAE9B,IAAIxJ,IAAI,MAAyB,EAAE;YACjC,IAAI,CAAC8K,aAAa,CAChBF,OAAO,EACNoF,iBAAiB,IAAIxM,IAAI,CAACxD,IAAI,KAAK,kBAAkB,IACnD+P,UAAU,IAAI1Q,UAAU,CAACmK,MAAM,EAAE;cAAE8C,IAAI,EAAE9I;YAAK,CAAC,CAAE,QAGtD,CAAC;UACH,CAAC,MAAM,IAAIwM,iBAAiB,IAAIhQ,IAAI,MAA0B,EAAE;YAC9D,IAAI,CAAC8K,aAAa,CAACF,OAAO,GAA0B,CAAC;UACvD,CAAC,MAAM;YACL,IAAI,CAACE,aAAa,CAACF,OAAO,GAA8B,CAAC;UAC3D;QACF,CAAC,MAAM,IACL5K,IAAI,MAAuB,IAC3B,EAAEwD,IAAI,CAACxD,IAAI,KAAK,kBAAkB,IAAIwD,IAAI,CAACyM,UAAU,CAAC9N,MAAM,GAAG,CAAC,CAAC,IACjEqB,IAAI,CAACxD,IAAI,KAAK,WAAW,IACzBwD,IAAI,CAACxD,IAAI,KAAK,iBAAiB,EAC/B;UAMA,IAAI,CAAC8K,aAAa,CAChBF,OAAO,EACP5E,CAAC,KAAK,CAAC,OAEHA,CAAC,KAAK4F,GAAG,GAAG,CAAC,QAGnB,CAAC;QACH,CAAC,MAAM;UACL,IAAI,CAACd,aAAa,CAACF,OAAO,GAA8B,CAAC;QAC3D;MACF;IACF;IAEA,IAAI5K,IAAI,MAA0B,IAAIoP,MAAM,IAAIG,QAAQ,EAAE;MACxD,IAAI,CAAC7N,gBAAgB,GAAG6N,QAAQ;IAClC;EACF;AACF;AAGAW,MAAM,CAACC,MAAM,CAAC/P,OAAO,CAACgQ,SAAS,EAAEjR,kBAAkB,CAAC;AAEjB;EAEjCiB,OAAO,CAACgQ,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAgB,CAAC,CAAC;AAC1D;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKcpQ,OAAO;AAEtB,SAASoN,cAAcA,CAAgBhI,eAAuB,EAAEiL,IAAa,EAAE;EAC7E,IAAI,CAACpM,KAAK,CAAC,GAAG,EAAE,KAAK,EAAEmB,eAAe,CAAC;EACvC,IAAI,CAACiL,IAAI,EAAE,IAAI,CAAClM,KAAK,CAAC,CAAC;AACzB", "ignoreList": []}