<template>
    <Layout>
        <FormCard :title="t('auth.pages.register.page.title')" @submit="handleSubmit">
            <FormInput id="name" :label="t('auth.pages.register.page.form.name.label')"
                v-model="form.name" :placeholder="t('auth.pages.register.page.form.name.placeholder')"
                required />
            <FormInput id="email" :label="t('auth.pages.register.page.form.email.label')" v-model="form.email"
                :placeholder="t('auth.pages.register.page.form.email.placeholder')" required />

            <div class="flex items-center justify-between mb-2">
                <label class="block text-sm text-gray-400">{{ t('auth.pages.register.page.form.password.label')
                    }}</label>
            </div>

            <FormInput id="password" type="password" v-model="form.password"
                :placeholder="t('auth.pages.register.page.form.password.placeholder')" required />

            <FormInput id="confirmPassword" type="password" v-model="form.confirmPassword"
                placeholder="Confirm your password" required />

            <button type="submit"
                class="w-full mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                :disabled="authStore.isLoading">
                {{ authStore.isLoading ? t('auth.pages.register.page.form.register_button.loading') :
                    t('auth.pages.register.page.form.register_button.label') }}
            </button>

            <p class="mt-4 text-center text-sm text-gray-400">
                {{ t('auth.pages.register.page.form.login.label') }}
                <router-link to="/auth/login" class="text-purple-400 hover:text-purple-300">
                    {{ t('auth.pages.register.page.form.login.link') }}
                </router-link>
            </p>
        </FormCard>
    </Layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import Layout from './../../components/Layout.vue'
import FormCard from './../../components/Auth/FormCard.vue'
import FormInput from './../../components/Auth/FormInput.vue'
import { useAuthStore } from '../../stores/auth.js'
import Swal from 'sweetalert2'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()

const form = reactive({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
})

const handleSubmit = async () => {
    try {
        // Validate form
        if (!form.name || !form.email || !form.password || !form.confirmPassword) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'All fields are required'
            })
            return
        }

        if (form.password !== form.confirmPassword) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Passwords do not match'
            })
            return
        }

        if (form.password.length < 8) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Password must be at least 8 characters long'
            })
            return
        }

        const result = await authStore.register({
            name: form.name,
            email: form.email,
            password: form.password
        })

        if (result.success) {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Registration successful! Please login with your credentials.',
                timer: 2000,
                showConfirmButton: false
            })
            
            // Redirect to login page
            router.push('/auth/login')
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Registration Failed',
                text: result.error
            })
        }
    } catch (error) {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Registration failed. Please try again.'
        })
    }
}
</script>