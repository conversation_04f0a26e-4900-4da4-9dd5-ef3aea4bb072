{"version": 3, "sources": ["../../src/enums/image-gravity.ts"], "names": ["ImageGravity"], "mappings": ";AAAO,IAAK,eAAL,kBAAKA,kBAAL;AACH,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,aAAU;AACV,EAAAA,cAAA,SAAM;AACN,EAAAA,cAAA,cAAW;AACX,EAAAA,cAAA,UAAO;AACP,EAAAA,cAAA,WAAQ;AACR,EAAAA,cAAA,gBAAa;AACb,EAAAA,cAAA,YAAS;AACT,EAAAA,cAAA,iBAAc;AATN,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum ImageGravity {\n    Center = 'center',\n    Topleft = 'top-left',\n    Top = 'top',\n    Topright = 'top-right',\n    Left = 'left',\n    Right = 'right',\n    Bottomleft = 'bottom-left',\n    Bottom = 'bottom',\n    Bottomright = 'bottom-right',\n}"]}