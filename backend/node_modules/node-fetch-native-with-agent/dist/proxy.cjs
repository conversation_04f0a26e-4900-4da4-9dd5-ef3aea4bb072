"use strict";var z=Object.defineProperty;var p=(r,e)=>z(r,"name",{value:e,configurable:!0});var M,I;const http$1=require("node:http"),https$1=require("node:https"),require$$1$4=require("node:url"),index=require("./shared/node-fetch-native-with-agent.df7e6bd6.cjs"),_commonjsHelpers=require("./shared/node-fetch-native-with-agent.61758d11.cjs"),require$$0$1=require("net"),require$$0=require("http"),require$$1=require("https"),require$$1$3=require("tls"),require$$1$1=require("tty"),require$$1$2=require("util"),require$$0$2=require("os"),require$$3=require("events"),require$$5=require("url"),require$$2=require("assert"),nodeFetchNativeWithAgent=require("node-fetch-native-with-agent");require("node:assert"),require("node:net"),require("node:stream"),require("node:buffer"),require("node:util"),require("node:querystring"),require("node:diagnostics_channel"),require("node:events"),require("node:tls"),require("node:zlib"),require("node:perf_hooks"),require("node:util/types"),require("node:os"),require("node:async_hooks"),require("node:console"),require("string_decoder"),require("node:worker_threads");function _interopDefaultCompat(r){return r&&typeof r=="object"&&"default"in r?r.default:r}p(_interopDefaultCompat,"_interopDefaultCompat");function _interopNamespaceCompat(r){if(r&&typeof r=="object"&&"default"in r)return r;const e=Object.create(null);if(r)for(const o in r)e[o]=r[o];return e.default=r,e}p(_interopNamespaceCompat,"_interopNamespaceCompat");const http__namespace=_interopNamespaceCompat(http$1),https__namespace=_interopNamespaceCompat(https$1),require$$0__default$1=_interopDefaultCompat(require$$0$1),require$$0__default=_interopDefaultCompat(require$$0),require$$1__default=_interopDefaultCompat(require$$1),require$$1__default$3=_interopDefaultCompat(require$$1$3),require$$1__default$1=_interopDefaultCompat(require$$1$1),require$$1__default$2=_interopDefaultCompat(require$$1$2),require$$0__default$2=_interopDefaultCompat(require$$0$2),require$$3__default=_interopDefaultCompat(require$$3),require$$5__default=_interopDefaultCompat(require$$5),require$$2__default=_interopDefaultCompat(require$$2);var dist$2={},helpers={},__createBinding$2=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__createBinding||(Object.create?function(r,e,o,t){t===void 0&&(t=o);var l=Object.getOwnPropertyDescriptor(e,o);(!l||("get"in l?!e.__esModule:l.writable||l.configurable))&&(l={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(r,t,l)}:function(r,e,o,t){t===void 0&&(t=o),r[t]=e[o]}),__setModuleDefault$2=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),__importStar$2=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var o in r)o!=="default"&&Object.prototype.hasOwnProperty.call(r,o)&&__createBinding$2(e,r,o);return __setModuleDefault$2(e,r),e};Object.defineProperty(helpers,"__esModule",{value:!0}),helpers.req=helpers.json=helpers.toBuffer=void 0;const http=__importStar$2(require$$0__default),https=__importStar$2(require$$1__default);async function toBuffer(r){let e=0;const o=[];for await(const t of r)e+=t.length,o.push(t);return Buffer.concat(o,e)}p(toBuffer,"toBuffer"),helpers.toBuffer=toBuffer;async function json(r){const o=(await toBuffer(r)).toString("utf8");try{return JSON.parse(o)}catch(t){const l=t;throw l.message+=` (input: ${o})`,l}}p(json,"json"),helpers.json=json;function req(r,e={}){const t=((typeof r=="string"?r:r.href).startsWith("https:")?https:http).request(r,e),l=new Promise((m,w)=>{t.once("response",m).once("error",w).end()});return t.then=l.then.bind(l),t}p(req,"req"),helpers.req=req,function(r){var e=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__createBinding||(Object.create?function(c,n,f,_){_===void 0&&(_=f);var v=Object.getOwnPropertyDescriptor(n,f);(!v||("get"in v?!n.__esModule:v.writable||v.configurable))&&(v={enumerable:!0,get:function(){return n[f]}}),Object.defineProperty(c,_,v)}:function(c,n,f,_){_===void 0&&(_=f),c[_]=n[f]}),o=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__setModuleDefault||(Object.create?function(c,n){Object.defineProperty(c,"default",{enumerable:!0,value:n})}:function(c,n){c.default=n}),t=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importStar||function(c){if(c&&c.__esModule)return c;var n={};if(c!=null)for(var f in c)f!=="default"&&Object.prototype.hasOwnProperty.call(c,f)&&e(n,c,f);return o(n,c),n},l=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__exportStar||function(c,n){for(var f in c)f!=="default"&&!Object.prototype.hasOwnProperty.call(n,f)&&e(n,c,f)};Object.defineProperty(r,"__esModule",{value:!0}),r.Agent=void 0;const m=t(require$$0__default$1),w=t(require$$0__default),$=require$$1__default;l(helpers,r);const y=Symbol("AgentBaseInternalState"),C=class C extends w.Agent{constructor(n){super(n),this[y]={}}isSecureEndpoint(n){if(n){if(typeof n.secureEndpoint=="boolean")return n.secureEndpoint;if(typeof n.protocol=="string")return n.protocol==="https:"}const{stack:f}=new Error;return typeof f!="string"?!1:f.split(`
`).some(_=>_.indexOf("(https.js:")!==-1||_.indexOf("node:https:")!==-1)}incrementSockets(n){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;this.sockets[n]||(this.sockets[n]=[]);const f=new m.Socket({writable:!1});return this.sockets[n].push(f),this.totalSocketCount++,f}decrementSockets(n,f){if(!this.sockets[n]||f===null)return;const _=this.sockets[n],v=_.indexOf(f);v!==-1&&(_.splice(v,1),this.totalSocketCount--,_.length===0&&delete this.sockets[n])}getName(n){return(typeof n.secureEndpoint=="boolean"?n.secureEndpoint:this.isSecureEndpoint(n))?$.Agent.prototype.getName.call(this,n):super.getName(n)}createSocket(n,f,_){const v={...f,secureEndpoint:this.isSecureEndpoint(f)},F=this.getName(v),x=this.incrementSockets(F);Promise.resolve().then(()=>this.connect(n,v)).then(j=>{if(this.decrementSockets(F,x),j instanceof w.Agent)return j.addRequest(n,v);this[y].currentSocket=j,super.createSocket(n,f,_)},j=>{this.decrementSockets(F,x),_(j)})}createConnection(){const n=this[y].currentSocket;if(this[y].currentSocket=void 0,!n)throw new Error("No socket was returned in the `connect()` function");return n}get defaultPort(){return this[y].defaultPort??(this.protocol==="https:"?443:80)}set defaultPort(n){this[y]&&(this[y].defaultPort=n)}get protocol(){return this[y].protocol??(this.isSecureEndpoint()?"https:":"http:")}set protocol(n){this[y]&&(this[y].protocol=n)}};p(C,"Agent");let b=C;r.Agent=b}(dist$2);var dist$1={},src={exports:{}},browser={exports:{}},ms,hasRequiredMs;function requireMs(){if(hasRequiredMs)return ms;hasRequiredMs=1;var r=1e3,e=r*60,o=e*60,t=o*24,l=t*7,m=t*365.25;ms=p(function(C,c){c=c||{};var n=typeof C;if(n==="string"&&C.length>0)return w(C);if(n==="number"&&isFinite(C))return c.long?y(C):$(C);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(C))},"ms");function w(C){if(C=String(C),!(C.length>100)){var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(C);if(c){var n=parseFloat(c[1]),f=(c[2]||"ms").toLowerCase();switch(f){case"years":case"year":case"yrs":case"yr":case"y":return n*m;case"weeks":case"week":case"w":return n*l;case"days":case"day":case"d":return n*t;case"hours":case"hour":case"hrs":case"hr":case"h":return n*o;case"minutes":case"minute":case"mins":case"min":case"m":return n*e;case"seconds":case"second":case"secs":case"sec":case"s":return n*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}p(w,"parse");function $(C){var c=Math.abs(C);return c>=t?Math.round(C/t)+"d":c>=o?Math.round(C/o)+"h":c>=e?Math.round(C/e)+"m":c>=r?Math.round(C/r)+"s":C+"ms"}p($,"fmtShort");function y(C){var c=Math.abs(C);return c>=t?b(C,c,t,"day"):c>=o?b(C,c,o,"hour"):c>=e?b(C,c,e,"minute"):c>=r?b(C,c,r,"second"):C+" ms"}p(y,"fmtLong");function b(C,c,n,f){var _=c>=n*1.5;return Math.round(C/n)+" "+f+(_?"s":"")}return p(b,"plural"),ms}p(requireMs,"requireMs");var common,hasRequiredCommon;function requireCommon(){if(hasRequiredCommon)return common;hasRequiredCommon=1;function r(e){t.debug=t,t.default=t,t.coerce=b,t.disable=w,t.enable=m,t.enabled=$,t.humanize=requireMs(),t.destroy=C,Object.keys(e).forEach(c=>{t[c]=e[c]}),t.names=[],t.skips=[],t.formatters={};function o(c){let n=0;for(let f=0;f<c.length;f++)n=(n<<5)-n+c.charCodeAt(f),n|=0;return t.colors[Math.abs(n)%t.colors.length]}p(o,"selectColor"),t.selectColor=o;function t(c){let n,f=null,_,v;function F(...x){if(!F.enabled)return;const j=F,R=Number(new Date),q=R-(n||R);j.diff=q,j.prev=n,j.curr=R,n=R,x[0]=t.coerce(x[0]),typeof x[0]!="string"&&x.unshift("%O");let S=0;x[0]=x[0].replace(/%([a-zA-Z%])/g,(A,k)=>{if(A==="%%")return"%";S++;const B=t.formatters[k];if(typeof B=="function"){const U=x[S];A=B.call(j,U),x.splice(S,1),S--}return A}),t.formatArgs.call(j,x),(j.log||t.log).apply(j,x)}return p(F,"debug"),F.namespace=c,F.useColors=t.useColors(),F.color=t.selectColor(c),F.extend=l,F.destroy=t.destroy,Object.defineProperty(F,"enabled",{enumerable:!0,configurable:!1,get:()=>f!==null?f:(_!==t.namespaces&&(_=t.namespaces,v=t.enabled(c)),v),set:x=>{f=x}}),typeof t.init=="function"&&t.init(F),F}p(t,"createDebug");function l(c,n){const f=t(this.namespace+(typeof n>"u"?":":n)+c);return f.log=this.log,f}p(l,"extend");function m(c){t.save(c),t.namespaces=c,t.names=[],t.skips=[];let n;const f=(typeof c=="string"?c:"").split(/[\s,]+/),_=f.length;for(n=0;n<_;n++)f[n]&&(c=f[n].replace(/\*/g,".*?"),c[0]==="-"?t.skips.push(new RegExp("^"+c.slice(1)+"$")):t.names.push(new RegExp("^"+c+"$")))}p(m,"enable");function w(){const c=[...t.names.map(y),...t.skips.map(y).map(n=>"-"+n)].join(",");return t.enable(""),c}p(w,"disable");function $(c){if(c[c.length-1]==="*")return!0;let n,f;for(n=0,f=t.skips.length;n<f;n++)if(t.skips[n].test(c))return!1;for(n=0,f=t.names.length;n<f;n++)if(t.names[n].test(c))return!0;return!1}p($,"enabled");function y(c){return c.toString().substring(2,c.toString().length-2).replace(/\.\*\?$/,"*")}p(y,"toNamespace");function b(c){return c instanceof Error?c.stack||c.message:c}p(b,"coerce");function C(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return p(C,"destroy"),t.enable(t.load()),t}return p(r,"setup"),common=r,common}p(requireCommon,"requireCommon");var hasRequiredBrowser;function requireBrowser(){return hasRequiredBrowser||(hasRequiredBrowser=1,function(r,e){e.formatArgs=t,e.save=l,e.load=m,e.useColors=o,e.storage=w(),e.destroy=(()=>{let y=!1;return()=>{y||(y=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function o(){return typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}p(o,"useColors");function t(y){if(y[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+y[0]+(this.useColors?"%c ":" ")+"+"+r.exports.humanize(this.diff),!this.useColors)return;const b="color: "+this.color;y.splice(1,0,b,"color: inherit");let C=0,c=0;y[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(C++,n==="%c"&&(c=C))}),y.splice(c,0,b)}p(t,"formatArgs"),e.log=console.debug||console.log||(()=>{});function l(y){try{y?e.storage.setItem("debug",y):e.storage.removeItem("debug")}catch{}}p(l,"save");function m(){let y;try{y=e.storage.getItem("debug")}catch{}return!y&&typeof process<"u"&&"env"in process&&(y=process.env.DEBUG),y}p(m,"load");function w(){try{return localStorage}catch{}}p(w,"localstorage"),r.exports=requireCommon()(e);const{formatters:$}=r.exports;$.j=function(y){try{return JSON.stringify(y)}catch(b){return"[UnexpectedJSONParseError]: "+b.message}}}(browser,browser.exports)),browser.exports}p(requireBrowser,"requireBrowser");var node={exports:{}},hasFlag,hasRequiredHasFlag;function requireHasFlag(){return hasRequiredHasFlag||(hasRequiredHasFlag=1,hasFlag=p((r,e=process.argv)=>{const o=r.startsWith("-")?"":r.length===1?"-":"--",t=e.indexOf(o+r),l=e.indexOf("--");return t!==-1&&(l===-1||t<l)},"hasFlag")),hasFlag}p(requireHasFlag,"requireHasFlag");var supportsColor_1,hasRequiredSupportsColor;function requireSupportsColor(){if(hasRequiredSupportsColor)return supportsColor_1;hasRequiredSupportsColor=1;const r=require$$0__default$2,e=require$$1__default$1,o=requireHasFlag(),{env:t}=process;let l;o("no-color")||o("no-colors")||o("color=false")||o("color=never")?l=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(l=1),"FORCE_COLOR"in t&&(t.FORCE_COLOR==="true"?l=1:t.FORCE_COLOR==="false"?l=0:l=t.FORCE_COLOR.length===0?1:Math.min(parseInt(t.FORCE_COLOR,10),3));function m(y){return y===0?!1:{level:y,hasBasic:!0,has256:y>=2,has16m:y>=3}}p(m,"translateLevel");function w(y,b){if(l===0)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(y&&!b&&l===void 0)return 0;const C=l||0;if(t.TERM==="dumb")return C;if(process.platform==="win32"){const c=r.release().split(".");return Number(c[0])>=10&&Number(c[2])>=10586?Number(c[2])>=14931?3:2:1}if("CI"in t)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(c=>c in t)||t.CI_NAME==="codeship"?1:C;if("TEAMCITY_VERSION"in t)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(t.TEAMCITY_VERSION)?1:0;if(t.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in t){const c=parseInt((t.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(t.TERM_PROGRAM){case"iTerm.app":return c>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(t.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(t.TERM)||"COLORTERM"in t?1:C}p(w,"supportsColor");function $(y){const b=w(y,y&&y.isTTY);return m(b)}return p($,"getSupportLevel"),supportsColor_1={supportsColor:$,stdout:m(w(!0,e.isatty(1))),stderr:m(w(!0,e.isatty(2)))},supportsColor_1}p(requireSupportsColor,"requireSupportsColor");var hasRequiredNode;function requireNode(){return hasRequiredNode||(hasRequiredNode=1,function(r,e){const o=require$$1__default$1,t=require$$1__default$2;e.init=C,e.log=$,e.formatArgs=m,e.save=y,e.load=b,e.useColors=l,e.destroy=t.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),e.colors=[6,2,3,4,5,1];try{const n=requireSupportsColor();n&&(n.stderr||n).level>=2&&(e.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}e.inspectOpts=Object.keys(process.env).filter(n=>/^debug_/i.test(n)).reduce((n,f)=>{const _=f.substring(6).toLowerCase().replace(/_([a-z])/g,(F,x)=>x.toUpperCase());let v=process.env[f];return/^(yes|on|true|enabled)$/i.test(v)?v=!0:/^(no|off|false|disabled)$/i.test(v)?v=!1:v==="null"?v=null:v=Number(v),n[_]=v,n},{});function l(){return"colors"in e.inspectOpts?!!e.inspectOpts.colors:o.isatty(process.stderr.fd)}p(l,"useColors");function m(n){const{namespace:f,useColors:_}=this;if(_){const v=this.color,F="\x1B[3"+(v<8?v:"8;5;"+v),x=`  ${F};1m${f} \x1B[0m`;n[0]=x+n[0].split(`
`).join(`
`+x),n.push(F+"m+"+r.exports.humanize(this.diff)+"\x1B[0m")}else n[0]=w()+f+" "+n[0]}p(m,"formatArgs");function w(){return e.inspectOpts.hideDate?"":new Date().toISOString()+" "}p(w,"getDate");function $(...n){return process.stderr.write(t.format(...n)+`
`)}p($,"log");function y(n){n?process.env.DEBUG=n:delete process.env.DEBUG}p(y,"save");function b(){return process.env.DEBUG}p(b,"load");function C(n){n.inspectOpts={};const f=Object.keys(e.inspectOpts);for(let _=0;_<f.length;_++)n.inspectOpts[f[_]]=e.inspectOpts[f[_]]}p(C,"init"),r.exports=requireCommon()(e);const{formatters:c}=r.exports;c.o=function(n){return this.inspectOpts.colors=this.useColors,t.inspect(n,this.inspectOpts).split(`
`).map(f=>f.trim()).join(" ")},c.O=function(n){return this.inspectOpts.colors=this.useColors,t.inspect(n,this.inspectOpts)}}(node,node.exports)),node.exports}p(requireNode,"requireNode"),typeof process>"u"||process.type==="renderer"||process.browser===!0||process.__nwjs?src.exports=requireBrowser():src.exports=requireNode();var srcExports=src.exports,__createBinding$1=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__createBinding||(Object.create?function(r,e,o,t){t===void 0&&(t=o);var l=Object.getOwnPropertyDescriptor(e,o);(!l||("get"in l?!e.__esModule:l.writable||l.configurable))&&(l={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(r,t,l)}:function(r,e,o,t){t===void 0&&(t=o),r[t]=e[o]}),__setModuleDefault$1=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),__importStar$1=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var o in r)o!=="default"&&Object.prototype.hasOwnProperty.call(r,o)&&__createBinding$1(e,r,o);return __setModuleDefault$1(e,r),e},__importDefault$2=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(dist$1,"__esModule",{value:!0});var HttpProxyAgent_1=dist$1.HttpProxyAgent=void 0;const net$1=__importStar$1(require$$0__default$1),tls$1=__importStar$1(require$$1__default$3),debug_1$2=__importDefault$2(srcExports),events_1=require$$3__default,agent_base_1$1=dist$2,url_1$1=require$$5__default,debug$2=(0,debug_1$2.default)("http-proxy-agent"),N=class N extends agent_base_1$1.Agent{constructor(e,o){super(o),this.proxy=typeof e=="string"?new url_1$1.URL(e):e,this.proxyHeaders=o?.headers??{},debug$2("Creating new HttpProxyAgent instance: %o",this.proxy.href);const t=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),l=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={...o?omit$1(o,"headers"):null,host:t,port:l}}addRequest(e,o){e._header=null,this.setRequestProps(e,o),super.addRequest(e,o)}setRequestProps(e,o){const{proxy:t}=this,l=o.secureEndpoint?"https:":"http:",m=e.getHeader("host")||"localhost",w=`${l}//${m}`,$=new url_1$1.URL(e.path,w);o.port!==80&&($.port=String(o.port)),e.path=String($);const y=typeof this.proxyHeaders=="function"?this.proxyHeaders():{...this.proxyHeaders};if(t.username||t.password){const b=`${decodeURIComponent(t.username)}:${decodeURIComponent(t.password)}`;y["Proxy-Authorization"]=`Basic ${Buffer.from(b).toString("base64")}`}y["Proxy-Connection"]||(y["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(const b of Object.keys(y)){const C=y[b];C&&e.setHeader(b,C)}}async connect(e,o){e._header=null,e.path.includes("://")||this.setRequestProps(e,o);let t,l;debug$2("Regenerating stored HTTP header string for request"),e._implicitHeader(),e.outputData&&e.outputData.length>0&&(debug$2("Patching connection write() output buffer with updated header"),t=e.outputData[0].data,l=t.indexOf(`\r
\r
`)+4,e.outputData[0].data=e._header+t.substring(l),debug$2("Output buffer: %o",e.outputData[0].data));let m;return this.proxy.protocol==="https:"?(debug$2("Creating `tls.Socket`: %o",this.connectOpts),m=tls$1.connect(this.connectOpts)):(debug$2("Creating `net.Socket`: %o",this.connectOpts),m=net$1.connect(this.connectOpts)),await(0,events_1.once)(m,"connect"),m}};p(N,"HttpProxyAgent");let HttpProxyAgent=N;HttpProxyAgent.protocols=["http","https"],HttpProxyAgent_1=dist$1.HttpProxyAgent=HttpProxyAgent;function omit$1(r,...e){const o={};let t;for(t in r)e.includes(t)||(o[t]=r[t]);return o}p(omit$1,"omit$1");var dist={},parseProxyResponse$1={},__importDefault$1=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(parseProxyResponse$1,"__esModule",{value:!0}),parseProxyResponse$1.parseProxyResponse=void 0;const debug_1$1=__importDefault$1(srcExports),debug$1=(0,debug_1$1.default)("https-proxy-agent:parse-proxy-response");function parseProxyResponse(r){return new Promise((e,o)=>{let t=0;const l=[];function m(){const C=r.read();C?b(C):r.once("readable",m)}p(m,"read");function w(){r.removeListener("end",$),r.removeListener("error",y),r.removeListener("readable",m)}p(w,"cleanup");function $(){w(),debug$1("onend"),o(new Error("Proxy connection ended before receiving CONNECT response"))}p($,"onend");function y(C){w(),debug$1("onerror %o",C),o(C)}p(y,"onerror");function b(C){l.push(C),t+=C.length;const c=Buffer.concat(l,t),n=c.indexOf(`\r
\r
`);if(n===-1){debug$1("have not received end of HTTP headers yet..."),m();return}const f=c.slice(0,n).toString("ascii").split(`\r
`),_=f.shift();if(!_)return r.destroy(),o(new Error("No header received from proxy CONNECT response"));const v=_.split(" "),F=+v[1],x=v.slice(2).join(" "),j={};for(const R of f){if(!R)continue;const q=R.indexOf(":");if(q===-1)return r.destroy(),o(new Error(`Invalid header from proxy CONNECT response: "${R}"`));const S=R.slice(0,q).toLowerCase(),G=R.slice(q+1).trimStart(),A=j[S];typeof A=="string"?j[S]=[A,G]:Array.isArray(A)?A.push(G):j[S]=G}debug$1("got proxy server response: %o %o",_,j),w(),e({connect:{statusCode:F,statusText:x,headers:j},buffered:c})}p(b,"ondata"),r.on("error",y),r.on("end",$),m()})}p(parseProxyResponse,"parseProxyResponse"),parseProxyResponse$1.parseProxyResponse=parseProxyResponse;var __createBinding=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__createBinding||(Object.create?function(r,e,o,t){t===void 0&&(t=o);var l=Object.getOwnPropertyDescriptor(e,o);(!l||("get"in l?!e.__esModule:l.writable||l.configurable))&&(l={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(r,t,l)}:function(r,e,o,t){t===void 0&&(t=o),r[t]=e[o]}),__setModuleDefault=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),__importStar=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var o in r)o!=="default"&&Object.prototype.hasOwnProperty.call(r,o)&&__createBinding(e,r,o);return __setModuleDefault(e,r),e},__importDefault=_commonjsHelpers.commonjsGlobal&&_commonjsHelpers.commonjsGlobal.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(dist,"__esModule",{value:!0});var HttpsProxyAgent_1=dist.HttpsProxyAgent=void 0;const net=__importStar(require$$0__default$1),tls=__importStar(require$$1__default$3),assert_1=__importDefault(require$$2__default),debug_1=__importDefault(srcExports),agent_base_1=dist$2,url_1=require$$5__default,parse_proxy_response_1=parseProxyResponse$1,debug=(0,debug_1.default)("https-proxy-agent"),D=class D extends agent_base_1.Agent{constructor(e,o){super(o),this.options={path:void 0},this.proxy=typeof e=="string"?new url_1.URL(e):e,this.proxyHeaders=o?.headers??{},debug("Creating new HttpsProxyAgent instance: %o",this.proxy.href);const t=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),l=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...o?omit(o,"headers"):null,host:t,port:l}}async connect(e,o){const{proxy:t}=this;if(!o.host)throw new TypeError('No "host" provided');let l;if(t.protocol==="https:"){debug("Creating `tls.Socket`: %o",this.connectOpts);const n=this.connectOpts.servername||this.connectOpts.host;l=tls.connect({...this.connectOpts,servername:n&&net.isIP(n)?void 0:n})}else debug("Creating `net.Socket`: %o",this.connectOpts),l=net.connect(this.connectOpts);const m=typeof this.proxyHeaders=="function"?this.proxyHeaders():{...this.proxyHeaders},w=net.isIPv6(o.host)?`[${o.host}]`:o.host;let $=`CONNECT ${w}:${o.port} HTTP/1.1\r
`;if(t.username||t.password){const n=`${decodeURIComponent(t.username)}:${decodeURIComponent(t.password)}`;m["Proxy-Authorization"]=`Basic ${Buffer.from(n).toString("base64")}`}m.Host=`${w}:${o.port}`,m["Proxy-Connection"]||(m["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(const n of Object.keys(m))$+=`${n}: ${m[n]}\r
`;const y=(0,parse_proxy_response_1.parseProxyResponse)(l);l.write(`${$}\r
`);const{connect:b,buffered:C}=await y;if(e.emit("proxyConnect",b),this.emit("proxyConnect",b,e),b.statusCode===200){if(e.once("socket",resume),o.secureEndpoint){debug("Upgrading socket connection to TLS");const n=o.servername||o.host;return tls.connect({...omit(o,"host","path","port"),socket:l,servername:net.isIP(n)?void 0:n})}return l}l.destroy();const c=new net.Socket({writable:!1});return c.readable=!0,e.once("socket",n=>{debug("Replaying proxy buffer for failed request"),(0,assert_1.default)(n.listenerCount("data")>0),n.push(C),n.push(null)}),c}};p(D,"HttpsProxyAgent");let HttpsProxyAgent=D;HttpsProxyAgent.protocols=["http","https"],HttpsProxyAgent_1=dist.HttpsProxyAgent=HttpsProxyAgent;function resume(r){r.resume()}p(resume,"resume");function omit(r,...e){const o={};let t;for(t in r)e.includes(t)||(o[t]=r[t]);return o}p(omit,"omit");var d=Object.defineProperty,O=p((r,e,o)=>e in r?d(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,"O"),s=p((r,e)=>d(r,"name",{value:e,configurable:!0}),"s"),i=p((r,e,o)=>(O(r,typeof e!="symbol"?e+"":e,o),o),"i");function H(...r){process.env.DEBUG&&console.debug("[node-fetch-native] [proxy]",...r)}p(H,"H"),s(H,"debug");function P(r,e){if(!e)return!1;for(const o of e)if(o===r||o[0]==="."&&r.endsWith(o.slice(1)))return!0;return!1}p(P,"P"),s(P,"bypassProxy");const g=(M=class extends index.ProxyAgent_1{constructor(e){super(e),this._options=e,i(this,"_agent"),this._agent=new index.Agent_1}dispatch(e,o){const t=new require$$1$4.URL(e.origin).hostname;return P(t,this._options.noProxy)?(H(`Bypassing proxy for: ${t}`),this._agent.dispatch(e,o)):super.dispatch(e,o)}},p(M,"g"),M);s(g,"UndiciProxyAgent");let h=g;const T=["http","https"],E={http:[HttpProxyAgent_1,HttpsProxyAgent_1],https:[HttpProxyAgent_1,HttpsProxyAgent_1]};function L(r){return T.includes(r)}p(L,"L"),s(L,"isValidProtocol");const u=(I=class extends dist$2.Agent{constructor(e){super({}),this._options=e,i(this,"cache",new Map),i(this,"httpAgent"),i(this,"httpsAgent"),this.httpAgent=new http__namespace.Agent({}),this.httpsAgent=new https__namespace.Agent({})}connect(e,o){const t=e.getHeader("upgrade")==="websocket",l=o.secureEndpoint?t?"wss:":"https:":t?"ws:":"http:",m=e.getHeader("host");if(P(m,this._options.noProxy))return o.secureEndpoint?this.httpsAgent:this.httpAgent;const w=`${l}+${this._options.uri}`;let $=this.cache.get(w);if(!$){const y=new require$$1$4.URL(this._options.uri).protocol.replace(":","");if(!L(y))throw new Error(`Unsupported protocol for proxy URL: ${this._options.uri}`);const b=E[y][o.secureEndpoint||t?1:0];$=new b(this._options.uri,this._options),this.cache.set(w,$)}return $}destroy(){for(const e of this.cache.values())e.destroy();super.destroy()}},p(I,"u"),I);s(u,"NodeProxyAgent");let a=u;function createProxy(r={}){const e=r.url||process.env.https_proxy||process.env.http_proxy||process.env.HTTPS_PROXY||process.env.HTTP_PROXY;if(!e)return{agent:void 0,dispatcher:void 0};const o=r.noProxy||process.env.no_proxy||process.env.NO_PROXY,t=typeof o=="string"?o.split(","):o,l=new a({uri:e,noProxy:t}),m=new h({uri:e,noProxy:t});return{agent:l,dispatcher:m}}p(createProxy,"createProxy"),s(createProxy,"createProxy");function createFetch(r={}){const e=createProxy(r);return(o,t)=>nodeFetchNativeWithAgent.fetch(o,{...e,...t})}p(createFetch,"createFetch"),s(createFetch,"createFetch");const fetch=createFetch({});exports.createFetch=createFetch,exports.createProxy=createProxy,exports.fetch=fetch;
