import { account } from '../config/appwrite.js';
import { Client, Account } from 'node-appwrite';
import { ID } from 'node-appwrite';

// Register new user
export const register = async (req, res) => {
  try {
    const { email, password, name } = req.body;

    // Validate input
    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        error: 'Email, password, and name are required'
      });
    }

    // Create user account
    const user = await account.create(ID.unique(), email, password, name);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: {
        $id: user.$id,
        name: user.name,
        email: user.email,
        emailVerification: user.emailVerification
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Registration failed'
    });
  }
};

// Login user
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    // Create session
    const session = await account.createEmailPasswordSession(email, password);

    res.json({
      success: true,
      message: 'Login successful',
      session: {
        $id: session.$id,
        userId: session.userId,
        expire: session.expire,
        current: session.current
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Login failed'
    });
  }
};

// Logout user
export const logout = async (req, res) => {
  try {
    const { sessionId } = req.body;
    const sessionToDelete = sessionId || req.sessionId;

    if (sessionToDelete) {
      // Create a temporary client with the session to delete it
      const tempClient = new Client()
        .setEndpoint(process.env.APPWRITE_ENDPOINT)
        .setProject(process.env.APPWRITE_PROJECT_ID);
      
      const tempAccount = new Account(tempClient);
      tempClient.setSession(sessionToDelete);
      
      await tempAccount.deleteSession('current');
    }

    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Logout failed'
    });
  }
};

// Get current user
export const getCurrentUser = async (req, res) => {
  try {
    const user = req.user; // From auth middleware

    res.json({
      success: true,
      user: {
        $id: user.$id,
        name: user.name,
        email: user.email,
        emailVerification: user.emailVerification,
        registration: user.registration,
        status: user.status,
        labels: user.labels,
        prefs: user.prefs
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to get user'
    });
  }
};

// Update user profile
export const updateProfile = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Name is required'
      });
    }

    // Create a temporary client with the user's session
    const tempClient = new Client()
      .setEndpoint(process.env.APPWRITE_ENDPOINT)
      .setProject(process.env.APPWRITE_PROJECT_ID);
    
    const tempAccount = new Account(tempClient);
    tempClient.setSession(req.sessionId);

    const user = await tempAccount.updateName(name);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        $id: user.$id,
        name: user.name,
        email: user.email
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to update profile'
    });
  }
};

// Change password
export const changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;

    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Old password and new password are required'
      });
    }

    // Create a temporary client with the user's session
    const tempClient = new Client()
      .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
      .setProject(process.env.APPWRITE_PROJECT_ID);
    
    const tempAccount = new Account(tempClient);
    tempClient.setSession(req.sessionId);

    await tempAccount.updatePassword(newPassword, oldPassword);

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to change password'
    });
  }
};

// Request password recovery
export const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email is required'
      });
    }

    const resetUrl = `${process.env.FRONTEND_URL}/auth/reset-password`;
    await account.createRecovery(email, resetUrl);

    res.json({
      success: true,
      message: 'Password recovery email sent'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to send recovery email'
    });
  }
};

// Reset password
export const resetPassword = async (req, res) => {
  try {
    const { userId, secret, password } = req.body;

    if (!userId || !secret || !password) {
      return res.status(400).json({
        success: false,
        error: 'User ID, secret, and new password are required'
      });
    }

    await account.updateRecovery(userId, secret, password);

    res.json({
      success: true,
      message: 'Password reset successful'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to reset password'
    });
  }
};

// OAuth providers
export const createOAuthSession = async (req, res) => {
  try {
    const { provider } = req.params;
    
    // Validate provider
    const validProviders = ['google', 'discord', 'github'];
    if (!validProviders.includes(provider)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid OAuth provider'
      });
    }

    const successUrl = `${process.env.FRONTEND_URL}/auth/callback`;
    const failureUrl = `${process.env.FRONTEND_URL}/auth/failure`;

    // Create OAuth2 token URL - this returns a URL, not creates a session directly
    const redirectUrl = account.createOAuth2Token(provider, successUrl, failureUrl);

    res.json({
      success: true,
      redirectUrl
    });
  } catch (error) {
    console.error('OAuth error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'OAuth initialization failed'
    });
  }
};

// Handle OAuth callback
export const handleOAuthCallback = async (req, res) => {
  try {
    // For OAuth callback, Appwrite automatically creates the session
    // We just need to redirect to frontend with success
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback/success`);
  } catch (error) {
    console.error('OAuth callback error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback/failure`);
  }
};

// Handle OAuth failure
export const handleOAuthFailure = async (req, res) => {
  try {
    res.redirect(`${process.env.FRONTEND_URL}/auth/callback/failure`);
  } catch (error) {
    console.error('OAuth failure error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/auth/login`);
  }
};
