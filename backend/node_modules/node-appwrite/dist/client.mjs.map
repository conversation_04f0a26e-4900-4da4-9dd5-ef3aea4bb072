{"version": 3, "sources": ["../src/client.ts"], "names": [], "mappings": ";AAAA,SAAS,OAAO,UAAU,YAAY;AACtC,SAAS,mBAAmB;AA0W5B,SAAS,aAAa;AAvVtB,IAAM,oBAAN,cAAgC,MAAM;AAAA,EAIlC,YAAY,SAAiB,OAAe,GAAG,OAAe,IAAI,WAAmB,IAAI;AACrF,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,WAAW;AAAA,EACpB;AACJ;AAEA,SAAS,eAAe;AACpB,MAAI,KAAK;AAGT,QAAM,WAAqB,CAAC;AAC5B,MAAI,OAAO,YAAY,aAAa;AAChC,QAAI,OAAO,QAAQ,aAAa;AAAU,eAAS,KAAK,QAAQ,QAAQ;AACxE,QAAI,OAAO,QAAQ,SAAS;AAAU,eAAS,KAAK,QAAQ,IAAI;AAAA,EACpE;AACA,MAAI,SAAS,SAAS,GAAG;AACrB,UAAM,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,EAClC;AAMA,MAAI,OAAO,cAAc,eAAe,OAAO,UAAU,cAAc,UAAU;AAE7E,UAAM,IAAI,UAAU,SAAS;AAAA,EAGjC,WAAW,OAAO,WAAW,gBAAgB,UAAU;AACnD,UAAM;AAAA,EAGV,WAAW,OAAO,YAAY,eAAe,OAAO,QAAQ,YAAY,UAAU;AAC9E,UAAM,YAAY,QAAQ,OAAO;AAAA,EACrC;AAEA,SAAO;AACX;AAEA,IAAM,UAAN,MAAM,QAAO;AAAA,EAAb;AAGI,kBAAS;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,oBAAoB;AAAA,IACxB;AACA,mBAAmB;AAAA,MACf,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,cAAe,aAAa;AAAA,MAC5B,8BAA8B;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,UAAwB;AAChC,SAAK,OAAO,WAAW;AAEvB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,YAA2B;AAErC,QAAI,OAAO,WAAW,gBAAgB,aAAa;AAC/C,cAAQ,KAAK,kDAAkD;AAAA,IACnE;AAEA,SAAK,OAAO,aAAa;AAEzB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,QAAgB,OAAqB;AAC3C,SAAK,QAAQ,OAAO,YAAY,CAAC,IAAI;AAErC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,OAAqB;AAC5B,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAqB;AACxB,SAAK,QAAQ,gBAAgB,IAAI;AACjC,SAAK,OAAO,MAAM;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAqB;AACxB,SAAK,QAAQ,gBAAgB,IAAI;AACjC,SAAK,OAAO,MAAM;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,OAAqB;AAC3B,SAAK,QAAQ,mBAAmB,IAAI;AACpC,SAAK,OAAO,SAAS;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,OAAqB;AAC5B,SAAK,QAAQ,oBAAoB,IAAI;AACrC,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,OAAqB;AACvC,SAAK,QAAQ,wBAAwB,IAAI;AACzC,SAAK,OAAO,qBAAqB;AACjC,WAAO;AAAA,EACX;AAAA,EAEA,eAAe,QAAgB,KAAU,UAAmB,CAAC,GAAG,SAAkB,CAAC,GAA0C;AACzH,aAAS,OAAO,YAAY;AAE5B,cAAU,OAAO,OAAO,CAAC,GAAG,KAAK,SAAS,OAAO;AAEjD,QAAI,UAAuB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,GAAG,YAAY,KAAK,OAAO,UAAU,EAAE,oBAAoB,CAAC,KAAK,OAAO,WAAW,CAAC;AAAA,IACxF;AAEA,QAAI,WAAW,OAAO;AAClB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAO,QAAQ,MAAM,CAAC,GAAG;AAC/D,YAAI,aAAa,OAAO,KAAK,KAAK;AAAA,MACtC;AAAA,IACJ,OAAO;AACH,cAAQ,QAAQ,cAAc,GAAG;AAAA,QAC7B,KAAK;AACD,kBAAQ,OAAO,KAAK,UAAU,MAAM;AACpC;AAAA,QAEJ,KAAK;AACD,gBAAM,WAAW,IAAI,SAAS;AAE9B,qBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC/C,gBAAI,iBAAiB,MAAM;AACvB,uBAAS,OAAO,KAAK,OAAO,MAAM,IAAI;AAAA,YAC1C,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC7B,yBAAW,eAAe,OAAO;AAC7B,yBAAS,OAAO,GAAG,GAAG,MAAM,WAAW;AAAA,cAC3C;AAAA,YACJ,OAAO;AACH,uBAAS,OAAO,KAAK,KAAK;AAAA,YAC9B;AAAA,UACJ;AAEA,kBAAQ,OAAO;AACf,iBAAO,QAAQ,cAAc;AAC7B;AAAA,MACR;AAAA,IACJ;AAEA,WAAO,EAAE,KAAK,IAAI,SAAS,GAAG,QAAQ;AAAA,EAC1C;AAAA,EAEA,MAAM,cAAc,QAAgB,KAAU,UAAmB,CAAC,GAAG,kBAA2B,CAAC,GAAG,YAAgD;AAChJ,UAAM,OAAO,OAAO,OAAO,eAAe,EAAE,KAAK,CAAC,UAAU,iBAAiB,IAAI;AAEjF,QAAI,KAAK,QAAQ,QAAO,YAAY;AAChC,aAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,SAAS,eAAe;AAAA,IAChE;AAEA,QAAI,QAAQ;AACZ,QAAI,WAAW;AAEf,WAAO,QAAQ,KAAK,MAAM;AACtB,UAAI,MAAM,QAAQ,QAAO;AACzB,UAAI,OAAO,KAAK,MAAM;AAClB,cAAM,KAAK;AAAA,MACf;AAEA,cAAQ,eAAe,IAAI,SAAS,KAAK,IAAI,MAAI,CAAC,IAAI,KAAK,IAAI;AAC/D,YAAM,QAAQ,KAAK,MAAM,OAAO,GAAG;AAEnC,UAAI,UAAU,EAAE,GAAG,iBAAiB,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,EAAC;AAEtE,iBAAW,MAAM,KAAK,KAAK,QAAQ,KAAK,SAAS,OAAO;AAExD,UAAI,cAAc,OAAO,eAAe,YAAY;AAChD,mBAAW;AAAA,UACP,KAAK,SAAS;AAAA,UACd,UAAU,KAAK,MAAO,MAAM,KAAK,OAAQ,GAAG;AAAA,UAC5C,cAAc;AAAA,UACd,aAAa,KAAK,KAAK,KAAK,OAAO,QAAO,UAAU;AAAA,UACpD,gBAAgB,KAAK,KAAK,MAAM,QAAO,UAAU;AAAA,QACrD,CAAC;AAAA,MACL;AAEA,UAAI,YAAY,SAAS,KAAK;AAC1B,gBAAQ,eAAe,IAAI,SAAS;AAAA,MACxC;AAEA,cAAQ;AAAA,IACZ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,SAAS,QAAgB,KAAU,UAAmB,CAAC,GAAG,SAAkB,CAAC,GAAoB;AACnG,UAAM,EAAE,KAAK,QAAQ,IAAI,KAAK,eAAe,QAAQ,KAAK,SAAS,MAAM;AAEzE,UAAM,WAAW,MAAM,MAAM,KAAK;AAAA,MAC9B,GAAG;AAAA,MACH,UAAU;AAAA,IACd,CAAC;AAED,QAAI,SAAS,WAAW,OAAO,SAAS,WAAW,KAAK;AACpD,YAAM,IAAI,kBAAkB,oBAAoB,SAAS,MAAM;AAAA,IACnE;AAEA,WAAO,SAAS,QAAQ,IAAI,UAAU,KAAK;AAAA,EAC/C;AAAA,EAEA,MAAM,KAAK,QAAgB,KAAU,UAAmB,CAAC,GAAG,SAAkB,CAAC,GAAG,eAAe,QAAsB;AAlU3H;AAmUQ,UAAM,EAAE,KAAK,QAAQ,IAAI,KAAK,eAAe,QAAQ,KAAK,SAAS,MAAM;AAEzE,QAAI,OAAY;AAEhB,UAAM,WAAW,MAAM,MAAM,KAAK,OAAO;AAEzC,SAAI,cAAS,QAAQ,IAAI,cAAc,MAAnC,mBAAsC,SAAS,qBAAqB;AACpE,aAAO,MAAM,SAAS,KAAK;AAAA,IAC/B,WAAW,iBAAiB,eAAe;AACvC,aAAO,MAAM,SAAS,YAAY;AAAA,IACtC,OAAO;AACH,aAAO;AAAA,QACH,SAAS,MAAM,SAAS,KAAK;AAAA,MACjC;AAAA,IACJ;AAEA,QAAI,OAAO,SAAS,QAAQ;AACxB,YAAM,IAAI,kBAAkB,6BAAM,SAAS,SAAS,QAAQ,6BAAM,MAAM,IAAI;AAAA,IAChF;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,OAAO,QAAQ,MAAe,SAAS,IAAa;AAChD,QAAI,SAAkB,CAAC;AAEvB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,UAAI,WAAW,SAAS,SAAS,MAAM,MAAK,MAAM;AAClD,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,iBAAS,EAAE,GAAG,QAAQ,GAAG,QAAO,QAAQ,OAAO,QAAQ,EAAE;AAAA,MAC7D,OAAO;AACH,eAAO,QAAQ,IAAI;AAAA,MACvB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AACJ;AArSM,QACK,aAAa,OAAO,OAAO;AADtC,IAAM,SAAN", "sourcesContent": ["import { fetch, FormData, File } from 'node-fetch-native-with-agent';\nimport { createAgent } from 'node-fetch-native-with-agent/agent';\nimport { Models } from './models';\n\ntype Payload = {\n    [key: string]: any;\n}\n\ntype UploadProgress = {\n    $id: string;\n    progress: number;\n    sizeUploaded: number;\n    chunksTotal: number;\n    chunksUploaded: number;\n}\n\ntype Headers = {\n    [key: string]: string;\n}\n\nclass AppwriteException extends Error {\n    code: number;\n    response: string;\n    type: string;\n    constructor(message: string, code: number = 0, type: string = '', response: string = '') {\n        super(message);\n        this.name = 'AppwriteException';\n        this.message = message;\n        this.code = code;\n        this.type = type;\n        this.response = response;\n    }\n}\n\nfunction getUserAgent() {\n    let ua = 'AppwriteNodeJSSDK/13.0.0';\n\n    // `process` is a global in Node.js, but not fully available in all runtimes.\n    const platform: string[] = [];\n    if (typeof process !== 'undefined') {\n        if (typeof process.platform === 'string') platform.push(process.platform);\n        if (typeof process.arch === 'string') platform.push(process.arch);\n    } \n    if (platform.length > 0) {\n        ua += ` (${platform.join('; ')})`;\n    }\n\n    // `navigator.userAgent` is available in Node.js 21 and later.\n    // It's also part of the WinterCG spec, so many edge runtimes provide it.\n    // https://common-min-api.proposal.wintercg.org/#requirements-for-navigatoruseragent\n    // @ts-ignore\n    if (typeof navigator !== 'undefined' && typeof navigator.userAgent === 'string') {\n        // @ts-ignore\n        ua += ` ${navigator.userAgent}`;\n\n    // @ts-ignore\n    } else if (typeof globalThis.EdgeRuntime === 'string') {\n        ua += ` EdgeRuntime`;\n\n    // Older Node.js versions don't have `navigator.userAgent`, so we have to use `process.version`.\n    } else if (typeof process !== 'undefined' && typeof process.version === 'string') {\n        ua += ` Node.js/${process.version}`;\n    }\n\n    return ua;\n}\n\nclass Client {\n    static CHUNK_SIZE = 1024 * 1024 * 5;\n\n    config = {\n        endpoint: 'https://cloud.appwrite.io/v1',\n        selfSigned: false,\n        project: '',\n        key: '',\n        jwt: '',\n        locale: '',\n        session: '',\n        forwardeduseragent: '',\n    };\n    headers: Headers = {\n        'x-sdk-name': 'Node.js',\n        'x-sdk-platform': 'server',\n        'x-sdk-language': 'nodejs',\n        'x-sdk-version': '13.0.0',\n        'user-agent' : getUserAgent(),\n        'X-Appwrite-Response-Format': '1.5.0',\n    };\n\n    /**\n     * Set Endpoint\n     *\n     * Your project endpoint\n     *\n     * @param {string} endpoint\n     *\n     * @returns {this}\n     */\n    setEndpoint(endpoint: string): this {\n        this.config.endpoint = endpoint;\n\n        return this;\n    }\n\n    /**\n     * Set self-signed\n     *\n     * @param {boolean} selfSigned\n     *\n     * @returns {this}\n     */\n    setSelfSigned(selfSigned: boolean): this {\n        // @ts-ignore\n        if (typeof globalThis.EdgeRuntime !== 'undefined') {\n            console.warn('setSelfSigned is not supported in edge runtimes.');\n        }\n\n        this.config.selfSigned = selfSigned;\n\n        return this;\n    }\n\n    /**\n     * Add header\n     *\n     * @param {string} header\n     * @param {string} value\n     *\n     * @returns {this}\n     */\n    addHeader(header: string, value: string): this {\n        this.headers[header.toLowerCase()] = value;\n\n        return this;\n    }\n\n    /**\n     * Set Project\n     *\n     * Your project ID\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setProject(value: string): this {\n        this.headers['X-Appwrite-Project'] = value;\n        this.config.project = value;\n        return this;\n    }\n    /**\n     * Set Key\n     *\n     * Your secret API key\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setKey(value: string): this {\n        this.headers['X-Appwrite-Key'] = value;\n        this.config.key = value;\n        return this;\n    }\n    /**\n     * Set JWT\n     *\n     * Your secret JSON Web Token\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setJWT(value: string): this {\n        this.headers['X-Appwrite-JWT'] = value;\n        this.config.jwt = value;\n        return this;\n    }\n    /**\n     * Set Locale\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setLocale(value: string): this {\n        this.headers['X-Appwrite-Locale'] = value;\n        this.config.locale = value;\n        return this;\n    }\n    /**\n     * Set Session\n     *\n     * The user session to authenticate with\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setSession(value: string): this {\n        this.headers['X-Appwrite-Session'] = value;\n        this.config.session = value;\n        return this;\n    }\n    /**\n     * Set ForwardedUserAgent\n     *\n     * The user agent string of the client that made the request\n     *\n     * @param value string\n     *\n     * @return {this}\n     */\n    setForwardedUserAgent(value: string): this {\n        this.headers['X-Forwarded-User-Agent'] = value;\n        this.config.forwardeduseragent = value;\n        return this;\n    }\n\n    prepareRequest(method: string, url: URL, headers: Headers = {}, params: Payload = {}): { uri: string, options: RequestInit } {\n        method = method.toUpperCase();\n\n        headers = Object.assign({}, this.headers, headers);\n\n        let options: RequestInit = {\n            method,\n            headers,\n            ...createAgent(this.config.endpoint, { rejectUnauthorized: !this.config.selfSigned }),\n        };\n\n        if (method === 'GET') {\n            for (const [key, value] of Object.entries(Client.flatten(params))) {\n                url.searchParams.append(key, value);\n            }\n        } else {\n            switch (headers['content-type']) {\n                case 'application/json':\n                    options.body = JSON.stringify(params);\n                    break;\n\n                case 'multipart/form-data':\n                    const formData = new FormData();\n\n                    for (const [key, value] of Object.entries(params)) {\n                        if (value instanceof File) {\n                            formData.append(key, value, value.name);\n                        } else if (Array.isArray(value)) {\n                            for (const nestedValue of value) {\n                                formData.append(`${key}[]`, nestedValue);\n                            }\n                        } else {\n                            formData.append(key, value);\n                        }\n                    }\n\n                    options.body = formData;\n                    delete headers['content-type'];\n                    break;\n            }\n        }\n\n        return { uri: url.toString(), options };\n    }\n\n    async chunkedUpload(method: string, url: URL, headers: Headers = {}, originalPayload: Payload = {}, onProgress: (progress: UploadProgress) => void) {\n        const file = Object.values(originalPayload).find((value) => value instanceof File);\n\n        if (file.size <= Client.CHUNK_SIZE) {\n            return await this.call(method, url, headers, originalPayload);\n        }\n\n        let start = 0;\n        let response = null;\n\n        while (start < file.size) {\n            let end = start + Client.CHUNK_SIZE; // Prepare end for the next chunk\n            if (end >= file.size) {\n                end = file.size; // Adjust for the last chunk to include the last byte\n            }\n\n            headers['content-range'] = `bytes ${start}-${end-1}/${file.size}`;\n            const chunk = file.slice(start, end);\n\n            let payload = { ...originalPayload, file: new File([chunk], file.name)};\n\n            response = await this.call(method, url, headers, payload);\n\n            if (onProgress && typeof onProgress === 'function') {\n                onProgress({\n                    $id: response.$id,\n                    progress: Math.round((end / file.size) * 100),\n                    sizeUploaded: end,\n                    chunksTotal: Math.ceil(file.size / Client.CHUNK_SIZE),\n                    chunksUploaded: Math.ceil(end / Client.CHUNK_SIZE)\n                });\n            }\n\n            if (response && response.$id) {\n                headers['x-appwrite-id'] = response.$id;\n            }\n\n            start = end;\n        }\n\n        return response;\n    }\n\n    async redirect(method: string, url: URL, headers: Headers = {}, params: Payload = {}): Promise<string> {\n        const { uri, options } = this.prepareRequest(method, url, headers, params);\n        \n        const response = await fetch(uri, {\n            ...options,\n            redirect: 'manual'\n        });\n\n        if (response.status !== 301 && response.status !== 302) {\n            throw new AppwriteException('Invalid redirect', response.status);\n        }\n\n        return response.headers.get('location') || '';\n    }\n\n    async call(method: string, url: URL, headers: Headers = {}, params: Payload = {}, responseType = 'json'): Promise<any> {\n        const { uri, options } = this.prepareRequest(method, url, headers, params);\n\n        let data: any = null;\n\n        const response = await fetch(uri, options);\n\n        if (response.headers.get('content-type')?.includes('application/json')) {\n            data = await response.json();\n        } else if (responseType === 'arrayBuffer') {\n            data = await response.arrayBuffer();\n        } else {\n            data = {\n                message: await response.text()\n            };\n        }\n\n        if (400 <= response.status) {\n            throw new AppwriteException(data?.message, response.status, data?.type, data);\n        }\n\n        return data;\n    }\n\n    static flatten(data: Payload, prefix = ''): Payload {\n        let output: Payload = {};\n\n        for (const [key, value] of Object.entries(data)) {\n            let finalKey = prefix ? prefix + '[' + key +']' : key;\n            if (Array.isArray(value)) {\n                output = { ...output, ...Client.flatten(value, finalKey) };\n            } else {\n                output[finalKey] = value;\n            }\n        }\n\n        return output;\n    }\n}\n\nexport { Client, AppwriteException };\nexport { Query } from './query';\nexport type { Models, Payload, UploadProgress };\nexport type { QueryTypes, QueryTypesList } from './query';\n"]}