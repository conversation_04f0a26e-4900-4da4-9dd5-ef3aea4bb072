<template>
    <Layout>
        <FormCard title="Forgot Password" @submit="handleSubmit">
            <FormInput
                id="email"
                label="Email"
                v-model="form.email"
                placeholder="Enter your email"
                required
            />
            
            <button
                type="submit"
                class="w-full mt-6 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                :disabled="loading"
            >
                {{ loading ? 'Submitting...' : 'Submit' }}
            </button>
            
            <p class="mt-4 text-center text-sm text-gray-400">
                Remembered your password?
                <router-link to="/auth/login" class="text-purple-400 hover:text-purple-300">
                    Login
                </router-link>
            </p>
        </FormCard>
    </Layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import Layout from './../../components/Layout.vue'
import FormCard from './../../components/Auth/FormCard.vue'
import FormInput from './../../components/Auth/FormInput.vue'

const loading = ref(false)
const form = reactive({
    email: ''
})

const handleSubmit = async () => {
    loading.value = true
    try {
        // Implement your forgot password logic here
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('Forgot password submitted:', form)
    } catch (error) {
        console.error('Forgot password failed:', error)
    } finally {
        loading.value = false
    }
}
</script>
