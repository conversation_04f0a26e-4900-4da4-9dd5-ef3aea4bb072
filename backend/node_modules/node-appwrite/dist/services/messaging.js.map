{"version": 3, "sources": ["../../src/services/messaging.ts"], "names": [], "mappings": "AAAA,SAAS,yBAA+D;AAIjE,MAAM,UAAU;AAAA,EAGnB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,aAAa,SAAoB,QAA8C;AACjF,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,MAAM,YAAY,WAAmB,SAAiB,SAAiB,QAAmB,OAAkB,SAAoB,IAAe,KAAgB,aAAwB,OAAiB,MAAgB,aAA+C;AACnQ,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,OAAO,aAAa;AAC3B,cAAQ,IAAI,IAAI;AAAA,IACpB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,MAAM,YAAY,WAAmB,QAAmB,OAAkB,SAAoB,SAAkB,SAAkB,OAAiB,MAAgB,IAAe,KAAgB,aAAsB,aAAiD;AACrQ,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,wCAAwC,QAAQ,eAAe,SAAS;AACxF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,OAAO,aAAa;AAC3B,cAAQ,IAAI,IAAI;AAAA,IACpB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,MAAM,WAAW,WAAmB,OAAe,MAAc,QAAmB,OAAkB,SAAoB,MAAe,QAAiB,OAAgB,MAAe,OAAgB,OAAgB,KAAc,OAAgB,OAAiB,aAA+C;AACnT,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,MAAM,WAAW,WAAmB,QAAmB,OAAkB,SAAoB,OAAgB,MAAe,MAAe,QAAiB,OAAgB,MAAe,OAAgB,OAAgB,KAAc,OAAgB,OAAiB,aAA+C;AACrT,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,uCAAuC,QAAQ,eAAe,SAAS;AACvF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,UAAU,WAAmB,SAAiB,QAAmB,OAAkB,SAAoB,OAAiB,aAA+C;AACzK,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,UAAU,WAAmB,QAAmB,OAAkB,SAAoB,SAAkB,OAAiB,aAA+C;AAC1K,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,sCAAsC,QAAQ,eAAe,SAAS;AACtF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW,WAA4C;AACzD,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,kCAAkC,QAAQ,eAAe,SAAS;AAClF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,OAAO,WAAgC;AACzC,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,kCAAkC,QAAQ,eAAe,SAAS;AAClF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,gBAAgB,WAAmB,SAA6C;AAClF,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,uCAAuC,QAAQ,eAAe,SAAS;AACvF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY,WAAmB,SAAgD;AACjF,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,0CAA0C,QAAQ,eAAe,SAAS;AAC1F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,SAAoB,QAA+C;AACnF,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,mBAAmB,YAAoB,MAAc,SAAkB,WAAoB,QAAiB,UAAmB,SAAmB,SAA6C;AACjM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,mBAAmB,YAAoB,MAAe,SAAmB,SAAkB,WAAoB,QAAiB,UAAmB,SAA6C;AAClM,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,yCAAyC,QAAQ,gBAAgB,UAAU;AAC3F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,kBAAkB,YAAoB,MAAc,oBAA6B,SAA6C;AAChI,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,uBAAuB,aAAa;AAC3C,cAAQ,oBAAoB,IAAI;AAAA,IACpC;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,kBAAkB,YAAoB,MAAe,SAAmB,oBAAuD;AACjI,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,wCAAwC,QAAQ,gBAAgB,UAAU;AAC1F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,uBAAuB,aAAa;AAC3C,cAAQ,oBAAoB,IAAI;AAAA,IACpC;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,sBAAsB,YAAoB,MAAc,QAAiB,QAAiB,YAAsB,UAAmB,WAAoB,aAAsB,cAAuB,SAA6C;AACnP,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,sBAAsB,YAAoB,MAAe,QAAiB,QAAiB,YAAsB,SAAmB,UAAmB,WAAoB,aAAsB,cAAiD;AACpP,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,4CAA4C,QAAQ,gBAAgB,UAAU;AAC9F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,oBAAoB,YAAoB,MAAc,YAAqB,UAAmB,SAAkB,SAA6C;AAC/J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,oBAAoB,YAAoB,MAAe,SAAmB,YAAqB,UAAmB,SAA4C;AAChK,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,0CAA0C,QAAQ,gBAAgB,UAAU;AAC5F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,uBAAuB,YAAoB,MAAc,QAAiB,UAAmB,WAAoB,aAAsB,cAAuB,SAA6C;AAC7M,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,uBAAuB,YAAoB,MAAe,SAAmB,QAAiB,UAAmB,WAAoB,aAAsB,cAAiD;AAC9M,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,6CAA6C,QAAQ,gBAAgB,UAAU;AAC/F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM,mBAAmB,YAAoB,MAAc,MAAc,MAAe,UAAmB,UAAmB,YAA6B,SAAmB,QAAiB,UAAmB,WAAoB,aAAsB,cAAuB,SAA6C;AAC5T,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,MAAM,mBAAmB,YAAoB,MAAe,MAAe,MAAe,UAAmB,UAAmB,YAA6B,SAAmB,QAAiB,UAAmB,WAAoB,aAAsB,cAAuB,SAA6C;AAC9T,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,yCAAyC,QAAQ,gBAAgB,UAAU;AAC3F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,uBAAuB,YAAoB,MAAc,MAAe,YAAqB,QAAiB,SAA6C;AAC7J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,uBAAuB,YAAoB,MAAe,SAAmB,YAAqB,QAAiB,MAAyC;AAC9J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,6CAA6C,QAAQ,gBAAgB,UAAU;AAC/F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,wBAAwB,YAAoB,MAAc,MAAe,UAAmB,QAAiB,SAA6C;AAC5J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,wBAAwB,YAAoB,MAAe,SAAmB,UAAmB,QAAiB,MAAyC;AAC7J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,8CAA8C,QAAQ,gBAAgB,UAAU;AAChG,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,qBAAqB,YAAoB,MAAc,MAAe,YAAqB,WAAoB,SAA6C;AAC9J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,qBAAqB,YAAoB,MAAe,SAAmB,YAAqB,WAAoB,MAAyC;AAC/J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,2CAA2C,QAAQ,gBAAgB,UAAU;AAC7F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,qBAAqB,YAAoB,MAAc,MAAe,QAAiB,WAAoB,SAA6C;AAC1J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,qBAAqB,YAAoB,MAAe,SAAmB,QAAiB,WAAoB,MAAyC;AAC3J,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,2CAA2C,QAAQ,gBAAgB,UAAU;AAC7F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAY,YAA8C;AAC5D,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,oCAAoC,QAAQ,gBAAgB,UAAU;AACtF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,YAAiC;AAClD,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,oCAAoC,QAAQ,gBAAgB,UAAU;AACtF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,YAAoB,SAA6C;AACpF,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,yCAAyC,QAAQ,gBAAgB,UAAU;AAC3F,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,cAAsB,SAA6C;AACxF,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,6CAA6C,QAAQ,kBAAkB,YAAY;AACnG,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW,SAAoB,QAA4C;AAC7E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,SAAiB,MAAc,WAA6C;AAC1F,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SAAS,SAAwC;AACnD,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,8BAA8B,QAAQ,aAAa,OAAO;AAC1E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,YAAY,SAAiB,MAAe,WAA6C;AAC3F,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,8BAA8B,QAAQ,aAAa,OAAO;AAC1E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,cAAc,aAAa;AAClC,cAAQ,WAAW,IAAI;AAAA,IAC3B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAY,SAA8B;AAC5C,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,8BAA8B,QAAQ,aAAa,OAAO;AAC1E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,SAAiB,SAA6C;AAC9E,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,mCAAmC,QAAQ,aAAa,OAAO;AAC/E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,gBAAgB,SAAiB,SAAoB,QAAiD;AACxG,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,UAAM,UAAU,0CAA0C,QAAQ,aAAa,OAAO;AACtF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,iBAAiB,SAAiB,cAAsB,UAA8C;AACxG,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,0CAA0C,QAAQ,aAAa,OAAO;AACtF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,iBAAiB,aAAa;AACrC,cAAQ,cAAc,IAAI;AAAA,IAC9B;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,cAAc,SAAiB,cAAkD;AACnF,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,yDAAyD,QAAQ,aAAa,OAAO,EAAE,QAAQ,kBAAkB,YAAY;AAC7I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,SAAiB,cAAmC;AACvE,QAAI,OAAO,YAAY,aAAa;AAChC,YAAM,IAAI,kBAAkB,uCAAuC;AAAA,IACvE;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,kBAAkB,4CAA4C;AAAA,IAC5E;AACA,UAAM,UAAU,yDAAyD,QAAQ,aAAa,OAAO,EAAE,QAAQ,kBAAkB,YAAY;AAC7I,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { SmtpEncryption } from '../enums/smtp-encryption';\n\nexport class Messaging {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * List messages\n     *\n     * Get a list of all messages from the current Appwrite project.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MessageList>}\n     */\n    async listMessages(queries?: string[], search?: string): Promise<Models.MessageList> {\n        const apiPath = '/messaging/messages';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email\n     *\n     * Create a new email message.\n     *\n     * @param {string} messageId\n     * @param {string} subject\n     * @param {string} content\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {string[]} cc\n     * @param {string[]} bcc\n     * @param {string[]} attachments\n     * @param {boolean} draft\n     * @param {boolean} html\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async createEmail(messageId: string, subject: string, content: string, topics?: string[], users?: string[], targets?: string[], cc?: string[], bcc?: string[], attachments?: string[], draft?: boolean, html?: boolean, scheduledAt?: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        if (typeof subject === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subject\"');\n        }\n        if (typeof content === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"content\"');\n        }\n        const apiPath = '/messaging/messages/email';\n        const payload: Payload = {};\n        if (typeof messageId !== 'undefined') {\n            payload['messageId'] = messageId;\n        }\n        if (typeof subject !== 'undefined') {\n            payload['subject'] = subject;\n        }\n        if (typeof content !== 'undefined') {\n            payload['content'] = content;\n        }\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof cc !== 'undefined') {\n            payload['cc'] = cc;\n        }\n        if (typeof bcc !== 'undefined') {\n            payload['bcc'] = bcc;\n        }\n        if (typeof attachments !== 'undefined') {\n            payload['attachments'] = attachments;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof html !== 'undefined') {\n            payload['html'] = html;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update email\n     *\n     * Update an email message by its unique ID.\n\n     *\n     * @param {string} messageId\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {string} subject\n     * @param {string} content\n     * @param {boolean} draft\n     * @param {boolean} html\n     * @param {string[]} cc\n     * @param {string[]} bcc\n     * @param {string} scheduledAt\n     * @param {string[]} attachments\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async updateEmail(messageId: string, topics?: string[], users?: string[], targets?: string[], subject?: string, content?: string, draft?: boolean, html?: boolean, cc?: string[], bcc?: string[], scheduledAt?: string, attachments?: string[]): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/email/{messageId}'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof subject !== 'undefined') {\n            payload['subject'] = subject;\n        }\n        if (typeof content !== 'undefined') {\n            payload['content'] = content;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof html !== 'undefined') {\n            payload['html'] = html;\n        }\n        if (typeof cc !== 'undefined') {\n            payload['cc'] = cc;\n        }\n        if (typeof bcc !== 'undefined') {\n            payload['bcc'] = bcc;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        if (typeof attachments !== 'undefined') {\n            payload['attachments'] = attachments;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create push notification\n     *\n     * Create a new push notification.\n     *\n     * @param {string} messageId\n     * @param {string} title\n     * @param {string} body\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {object} data\n     * @param {string} action\n     * @param {string} image\n     * @param {string} icon\n     * @param {string} sound\n     * @param {string} color\n     * @param {string} tag\n     * @param {string} badge\n     * @param {boolean} draft\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async createPush(messageId: string, title: string, body: string, topics?: string[], users?: string[], targets?: string[], data?: object, action?: string, image?: string, icon?: string, sound?: string, color?: string, tag?: string, badge?: string, draft?: boolean, scheduledAt?: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        if (typeof title === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"title\"');\n        }\n        if (typeof body === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"body\"');\n        }\n        const apiPath = '/messaging/messages/push';\n        const payload: Payload = {};\n        if (typeof messageId !== 'undefined') {\n            payload['messageId'] = messageId;\n        }\n        if (typeof title !== 'undefined') {\n            payload['title'] = title;\n        }\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof action !== 'undefined') {\n            payload['action'] = action;\n        }\n        if (typeof image !== 'undefined') {\n            payload['image'] = image;\n        }\n        if (typeof icon !== 'undefined') {\n            payload['icon'] = icon;\n        }\n        if (typeof sound !== 'undefined') {\n            payload['sound'] = sound;\n        }\n        if (typeof color !== 'undefined') {\n            payload['color'] = color;\n        }\n        if (typeof tag !== 'undefined') {\n            payload['tag'] = tag;\n        }\n        if (typeof badge !== 'undefined') {\n            payload['badge'] = badge;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update push notification\n     *\n     * Update a push notification by its unique ID.\n\n     *\n     * @param {string} messageId\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {string} title\n     * @param {string} body\n     * @param {object} data\n     * @param {string} action\n     * @param {string} image\n     * @param {string} icon\n     * @param {string} sound\n     * @param {string} color\n     * @param {string} tag\n     * @param {number} badge\n     * @param {boolean} draft\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async updatePush(messageId: string, topics?: string[], users?: string[], targets?: string[], title?: string, body?: string, data?: object, action?: string, image?: string, icon?: string, sound?: string, color?: string, tag?: string, badge?: number, draft?: boolean, scheduledAt?: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/push/{messageId}'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof title !== 'undefined') {\n            payload['title'] = title;\n        }\n        if (typeof body !== 'undefined') {\n            payload['body'] = body;\n        }\n        if (typeof data !== 'undefined') {\n            payload['data'] = data;\n        }\n        if (typeof action !== 'undefined') {\n            payload['action'] = action;\n        }\n        if (typeof image !== 'undefined') {\n            payload['image'] = image;\n        }\n        if (typeof icon !== 'undefined') {\n            payload['icon'] = icon;\n        }\n        if (typeof sound !== 'undefined') {\n            payload['sound'] = sound;\n        }\n        if (typeof color !== 'undefined') {\n            payload['color'] = color;\n        }\n        if (typeof tag !== 'undefined') {\n            payload['tag'] = tag;\n        }\n        if (typeof badge !== 'undefined') {\n            payload['badge'] = badge;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create SMS\n     *\n     * Create a new SMS message.\n     *\n     * @param {string} messageId\n     * @param {string} content\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {boolean} draft\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async createSms(messageId: string, content: string, topics?: string[], users?: string[], targets?: string[], draft?: boolean, scheduledAt?: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        if (typeof content === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"content\"');\n        }\n        const apiPath = '/messaging/messages/sms';\n        const payload: Payload = {};\n        if (typeof messageId !== 'undefined') {\n            payload['messageId'] = messageId;\n        }\n        if (typeof content !== 'undefined') {\n            payload['content'] = content;\n        }\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update SMS\n     *\n     * Update an email message by its unique ID.\n\n     *\n     * @param {string} messageId\n     * @param {string[]} topics\n     * @param {string[]} users\n     * @param {string[]} targets\n     * @param {string} content\n     * @param {boolean} draft\n     * @param {string} scheduledAt\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async updateSms(messageId: string, topics?: string[], users?: string[], targets?: string[], content?: string, draft?: boolean, scheduledAt?: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/sms/{messageId}'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        if (typeof topics !== 'undefined') {\n            payload['topics'] = topics;\n        }\n        if (typeof users !== 'undefined') {\n            payload['users'] = users;\n        }\n        if (typeof targets !== 'undefined') {\n            payload['targets'] = targets;\n        }\n        if (typeof content !== 'undefined') {\n            payload['content'] = content;\n        }\n        if (typeof draft !== 'undefined') {\n            payload['draft'] = draft;\n        }\n        if (typeof scheduledAt !== 'undefined') {\n            payload['scheduledAt'] = scheduledAt;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get message\n     *\n     * Get a message by its unique ID.\n\n     *\n     * @param {string} messageId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Message>}\n     */\n    async getMessage(messageId: string): Promise<Models.Message> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/{messageId}'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete message\n     *\n     * Delete a message. If the message is not a draft or scheduled, but has been sent, this will not recall the message.\n     *\n     * @param {string} messageId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async delete(messageId: string): Promise<{}> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/{messageId}'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List message logs\n     *\n     * Get the message activity logs listed by its unique ID.\n     *\n     * @param {string} messageId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listMessageLogs(messageId: string, queries?: string[]): Promise<Models.LogList> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/{messageId}/logs'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List message targets\n     *\n     * Get a list of the targets associated with a message.\n     *\n     * @param {string} messageId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TargetList>}\n     */\n    async listTargets(messageId: string, queries?: string[]): Promise<Models.TargetList> {\n        if (typeof messageId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"messageId\"');\n        }\n        const apiPath = '/messaging/messages/{messageId}/targets'.replace('{messageId}', messageId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List providers\n     *\n     * Get a list of all providers from the current Appwrite project.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.ProviderList>}\n     */\n    async listProviders(queries?: string[], search?: string): Promise<Models.ProviderList> {\n        const apiPath = '/messaging/providers';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create APNS provider\n     *\n     * Create a new Apple Push Notification service provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} authKey\n     * @param {string} authKeyId\n     * @param {string} teamId\n     * @param {string} bundleId\n     * @param {boolean} sandbox\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createApnsProvider(providerId: string, name: string, authKey?: string, authKeyId?: string, teamId?: string, bundleId?: string, sandbox?: boolean, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/apns';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof authKey !== 'undefined') {\n            payload['authKey'] = authKey;\n        }\n        if (typeof authKeyId !== 'undefined') {\n            payload['authKeyId'] = authKeyId;\n        }\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof bundleId !== 'undefined') {\n            payload['bundleId'] = bundleId;\n        }\n        if (typeof sandbox !== 'undefined') {\n            payload['sandbox'] = sandbox;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update APNS provider\n     *\n     * Update a Apple Push Notification service provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} authKey\n     * @param {string} authKeyId\n     * @param {string} teamId\n     * @param {string} bundleId\n     * @param {boolean} sandbox\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateApnsProvider(providerId: string, name?: string, enabled?: boolean, authKey?: string, authKeyId?: string, teamId?: string, bundleId?: string, sandbox?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/apns/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof authKey !== 'undefined') {\n            payload['authKey'] = authKey;\n        }\n        if (typeof authKeyId !== 'undefined') {\n            payload['authKeyId'] = authKeyId;\n        }\n        if (typeof teamId !== 'undefined') {\n            payload['teamId'] = teamId;\n        }\n        if (typeof bundleId !== 'undefined') {\n            payload['bundleId'] = bundleId;\n        }\n        if (typeof sandbox !== 'undefined') {\n            payload['sandbox'] = sandbox;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create FCM provider\n     *\n     * Create a new Firebase Cloud Messaging provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {object} serviceAccountJSON\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createFcmProvider(providerId: string, name: string, serviceAccountJSON?: object, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/fcm';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof serviceAccountJSON !== 'undefined') {\n            payload['serviceAccountJSON'] = serviceAccountJSON;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update FCM provider\n     *\n     * Update a Firebase Cloud Messaging provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {object} serviceAccountJSON\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateFcmProvider(providerId: string, name?: string, enabled?: boolean, serviceAccountJSON?: object): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/fcm/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof serviceAccountJSON !== 'undefined') {\n            payload['serviceAccountJSON'] = serviceAccountJSON;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Mailgun provider\n     *\n     * Create a new Mailgun provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} apiKey\n     * @param {string} domain\n     * @param {boolean} isEuRegion\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createMailgunProvider(providerId: string, name: string, apiKey?: string, domain?: string, isEuRegion?: boolean, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/mailgun';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof domain !== 'undefined') {\n            payload['domain'] = domain;\n        }\n        if (typeof isEuRegion !== 'undefined') {\n            payload['isEuRegion'] = isEuRegion;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Mailgun provider\n     *\n     * Update a Mailgun provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} apiKey\n     * @param {string} domain\n     * @param {boolean} isEuRegion\n     * @param {boolean} enabled\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateMailgunProvider(providerId: string, name?: string, apiKey?: string, domain?: string, isEuRegion?: boolean, enabled?: boolean, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/mailgun/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof domain !== 'undefined') {\n            payload['domain'] = domain;\n        }\n        if (typeof isEuRegion !== 'undefined') {\n            payload['isEuRegion'] = isEuRegion;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Msg91 provider\n     *\n     * Create a new MSG91 provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} templateId\n     * @param {string} senderId\n     * @param {string} authKey\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createMsg91Provider(providerId: string, name: string, templateId?: string, senderId?: string, authKey?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/msg91';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof templateId !== 'undefined') {\n            payload['templateId'] = templateId;\n        }\n        if (typeof senderId !== 'undefined') {\n            payload['senderId'] = senderId;\n        }\n        if (typeof authKey !== 'undefined') {\n            payload['authKey'] = authKey;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Msg91 provider\n     *\n     * Update a MSG91 provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} templateId\n     * @param {string} senderId\n     * @param {string} authKey\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateMsg91Provider(providerId: string, name?: string, enabled?: boolean, templateId?: string, senderId?: string, authKey?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/msg91/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof templateId !== 'undefined') {\n            payload['templateId'] = templateId;\n        }\n        if (typeof senderId !== 'undefined') {\n            payload['senderId'] = senderId;\n        }\n        if (typeof authKey !== 'undefined') {\n            payload['authKey'] = authKey;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Sendgrid provider\n     *\n     * Create a new Sendgrid provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} apiKey\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createSendgridProvider(providerId: string, name: string, apiKey?: string, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/sendgrid';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Sendgrid provider\n     *\n     * Update a Sendgrid provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} apiKey\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateSendgridProvider(providerId: string, name?: string, enabled?: boolean, apiKey?: string, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/sendgrid/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create SMTP provider\n     *\n     * Create a new SMTP provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} host\n     * @param {number} port\n     * @param {string} username\n     * @param {string} password\n     * @param {SmtpEncryption} encryption\n     * @param {boolean} autoTLS\n     * @param {string} mailer\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createSmtpProvider(providerId: string, name: string, host: string, port?: number, username?: string, password?: string, encryption?: SmtpEncryption, autoTLS?: boolean, mailer?: string, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        if (typeof host === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"host\"');\n        }\n        const apiPath = '/messaging/providers/smtp';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof host !== 'undefined') {\n            payload['host'] = host;\n        }\n        if (typeof port !== 'undefined') {\n            payload['port'] = port;\n        }\n        if (typeof username !== 'undefined') {\n            payload['username'] = username;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof encryption !== 'undefined') {\n            payload['encryption'] = encryption;\n        }\n        if (typeof autoTLS !== 'undefined') {\n            payload['autoTLS'] = autoTLS;\n        }\n        if (typeof mailer !== 'undefined') {\n            payload['mailer'] = mailer;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update SMTP provider\n     *\n     * Update a SMTP provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} host\n     * @param {number} port\n     * @param {string} username\n     * @param {string} password\n     * @param {SmtpEncryption} encryption\n     * @param {boolean} autoTLS\n     * @param {string} mailer\n     * @param {string} fromName\n     * @param {string} fromEmail\n     * @param {string} replyToName\n     * @param {string} replyToEmail\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateSmtpProvider(providerId: string, name?: string, host?: string, port?: number, username?: string, password?: string, encryption?: SmtpEncryption, autoTLS?: boolean, mailer?: string, fromName?: string, fromEmail?: string, replyToName?: string, replyToEmail?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/smtp/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof host !== 'undefined') {\n            payload['host'] = host;\n        }\n        if (typeof port !== 'undefined') {\n            payload['port'] = port;\n        }\n        if (typeof username !== 'undefined') {\n            payload['username'] = username;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof encryption !== 'undefined') {\n            payload['encryption'] = encryption;\n        }\n        if (typeof autoTLS !== 'undefined') {\n            payload['autoTLS'] = autoTLS;\n        }\n        if (typeof mailer !== 'undefined') {\n            payload['mailer'] = mailer;\n        }\n        if (typeof fromName !== 'undefined') {\n            payload['fromName'] = fromName;\n        }\n        if (typeof fromEmail !== 'undefined') {\n            payload['fromEmail'] = fromEmail;\n        }\n        if (typeof replyToName !== 'undefined') {\n            payload['replyToName'] = replyToName;\n        }\n        if (typeof replyToEmail !== 'undefined') {\n            payload['replyToEmail'] = replyToEmail;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Telesign provider\n     *\n     * Create a new Telesign provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} from\n     * @param {string} customerId\n     * @param {string} apiKey\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createTelesignProvider(providerId: string, name: string, from?: string, customerId?: string, apiKey?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/telesign';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        if (typeof customerId !== 'undefined') {\n            payload['customerId'] = customerId;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Telesign provider\n     *\n     * Update a Telesign provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} customerId\n     * @param {string} apiKey\n     * @param {string} from\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateTelesignProvider(providerId: string, name?: string, enabled?: boolean, customerId?: string, apiKey?: string, from?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/telesign/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof customerId !== 'undefined') {\n            payload['customerId'] = customerId;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Textmagic provider\n     *\n     * Create a new Textmagic provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} from\n     * @param {string} username\n     * @param {string} apiKey\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createTextmagicProvider(providerId: string, name: string, from?: string, username?: string, apiKey?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/textmagic';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        if (typeof username !== 'undefined') {\n            payload['username'] = username;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Textmagic provider\n     *\n     * Update a Textmagic provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} username\n     * @param {string} apiKey\n     * @param {string} from\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateTextmagicProvider(providerId: string, name?: string, enabled?: boolean, username?: string, apiKey?: string, from?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/textmagic/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof username !== 'undefined') {\n            payload['username'] = username;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Twilio provider\n     *\n     * Create a new Twilio provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} from\n     * @param {string} accountSid\n     * @param {string} authToken\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createTwilioProvider(providerId: string, name: string, from?: string, accountSid?: string, authToken?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/twilio';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        if (typeof accountSid !== 'undefined') {\n            payload['accountSid'] = accountSid;\n        }\n        if (typeof authToken !== 'undefined') {\n            payload['authToken'] = authToken;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Twilio provider\n     *\n     * Update a Twilio provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} accountSid\n     * @param {string} authToken\n     * @param {string} from\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateTwilioProvider(providerId: string, name?: string, enabled?: boolean, accountSid?: string, authToken?: string, from?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/twilio/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof accountSid !== 'undefined') {\n            payload['accountSid'] = accountSid;\n        }\n        if (typeof authToken !== 'undefined') {\n            payload['authToken'] = authToken;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create Vonage provider\n     *\n     * Create a new Vonage provider.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {string} from\n     * @param {string} apiKey\n     * @param {string} apiSecret\n     * @param {boolean} enabled\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async createVonageProvider(providerId: string, name: string, from?: string, apiKey?: string, apiSecret?: string, enabled?: boolean): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/providers/vonage';\n        const payload: Payload = {};\n        if (typeof providerId !== 'undefined') {\n            payload['providerId'] = providerId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof apiSecret !== 'undefined') {\n            payload['apiSecret'] = apiSecret;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update Vonage provider\n     *\n     * Update a Vonage provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string} name\n     * @param {boolean} enabled\n     * @param {string} apiKey\n     * @param {string} apiSecret\n     * @param {string} from\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async updateVonageProvider(providerId: string, name?: string, enabled?: boolean, apiKey?: string, apiSecret?: string, from?: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/vonage/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof enabled !== 'undefined') {\n            payload['enabled'] = enabled;\n        }\n        if (typeof apiKey !== 'undefined') {\n            payload['apiKey'] = apiKey;\n        }\n        if (typeof apiSecret !== 'undefined') {\n            payload['apiSecret'] = apiSecret;\n        }\n        if (typeof from !== 'undefined') {\n            payload['from'] = from;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get provider\n     *\n     * Get a provider by its unique ID.\n\n     *\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Provider>}\n     */\n    async getProvider(providerId: string): Promise<Models.Provider> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete provider\n     *\n     * Delete a provider by its unique ID.\n     *\n     * @param {string} providerId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteProvider(providerId: string): Promise<{}> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/{providerId}'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List provider logs\n     *\n     * Get the provider activity logs listed by its unique ID.\n     *\n     * @param {string} providerId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listProviderLogs(providerId: string, queries?: string[]): Promise<Models.LogList> {\n        if (typeof providerId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"providerId\"');\n        }\n        const apiPath = '/messaging/providers/{providerId}/logs'.replace('{providerId}', providerId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List subscriber logs\n     *\n     * Get the subscriber activity logs listed by its unique ID.\n     *\n     * @param {string} subscriberId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listSubscriberLogs(subscriberId: string, queries?: string[]): Promise<Models.LogList> {\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/subscribers/{subscriberId}/logs'.replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List topics\n     *\n     * Get a list of all topics from the current Appwrite project.\n     *\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.TopicList>}\n     */\n    async listTopics(queries?: string[], search?: string): Promise<Models.TopicList> {\n        const apiPath = '/messaging/topics';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create topic\n     *\n     * Create a new topic.\n     *\n     * @param {string} topicId\n     * @param {string} name\n     * @param {string[]} subscribe\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Topic>}\n     */\n    async createTopic(topicId: string, name: string, subscribe?: string[]): Promise<Models.Topic> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/messaging/topics';\n        const payload: Payload = {};\n        if (typeof topicId !== 'undefined') {\n            payload['topicId'] = topicId;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof subscribe !== 'undefined') {\n            payload['subscribe'] = subscribe;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get topic\n     *\n     * Get a topic by its unique ID.\n\n     *\n     * @param {string} topicId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Topic>}\n     */\n    async getTopic(topicId: string): Promise<Models.Topic> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update topic\n     *\n     * Update a topic by its unique ID.\n\n     *\n     * @param {string} topicId\n     * @param {string} name\n     * @param {string[]} subscribe\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Topic>}\n     */\n    async updateTopic(topicId: string, name?: string, subscribe?: string[]): Promise<Models.Topic> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof subscribe !== 'undefined') {\n            payload['subscribe'] = subscribe;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete topic\n     *\n     * Delete a topic by its unique ID.\n     *\n     * @param {string} topicId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteTopic(topicId: string): Promise<{}> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List topic logs\n     *\n     * Get the topic activity logs listed by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listTopicLogs(topicId: string, queries?: string[]): Promise<Models.LogList> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/logs'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List subscribers\n     *\n     * Get a list of all subscribers from the current Appwrite project.\n     *\n     * @param {string} topicId\n     * @param {string[]} queries\n     * @param {string} search\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SubscriberList>}\n     */\n    async listSubscribers(topicId: string, queries?: string[], search?: string): Promise<Models.SubscriberList> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        if (typeof search !== 'undefined') {\n            payload['search'] = search;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create subscriber\n     *\n     * Create a new subscriber.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @param {string} targetId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    async createSubscriber(topicId: string, subscriberId: string, targetId: string): Promise<Models.Subscriber> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        if (typeof targetId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"targetId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers'.replace('{topicId}', topicId);\n        const payload: Payload = {};\n        if (typeof subscriberId !== 'undefined') {\n            payload['subscriberId'] = subscriberId;\n        }\n        if (typeof targetId !== 'undefined') {\n            payload['targetId'] = targetId;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get subscriber\n     *\n     * Get a subscriber by its unique ID.\n\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Subscriber>}\n     */\n    async getSubscriber(topicId: string, subscriberId: string): Promise<Models.Subscriber> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete subscriber\n     *\n     * Delete a subscriber by its unique ID.\n     *\n     * @param {string} topicId\n     * @param {string} subscriberId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSubscriber(topicId: string, subscriberId: string): Promise<{}> {\n        if (typeof topicId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"topicId\"');\n        }\n        if (typeof subscriberId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"subscriberId\"');\n        }\n        const apiPath = '/messaging/topics/{topicId}/subscribers/{subscriberId}'.replace('{topicId}', topicId).replace('{subscriberId}', subscriberId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}