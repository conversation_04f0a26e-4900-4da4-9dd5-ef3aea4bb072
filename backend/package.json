{"name": "aveimage-cloud-backend", "version": "1.0.0", "description": "AveImage Cloud API Backend", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step needed for Node.js'"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "multer": "^1.4.4", "node-appwrite": "^13.0.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}