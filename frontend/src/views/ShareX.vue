<template>
  <LayoutDashboard>
    <div class="space-y-6">
      <!-- Header -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-100">ShareX Integration</h1>
        <p class="text-gray-400 mt-2">Set up ShareX for seamless image uploading to AveImgCloud</p>
      </div>

      <!-- Quick Setup Card -->
      <div class="bg-gradient-to-r from-purple-600 to-blue-600 p-6 rounded-lg text-white">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold">Quick Setup</h2>
            <p class="text-purple-100 mt-1">Download your personal ShareX configuration</p>
          </div>
          <div class="space-x-3">
            <button
              @click="downloadConfig"
              :disabled="isDownloading"
              class="px-4 py-2 bg-white text-purple-600 rounded-lg hover:bg-gray-100 transition-colors disabled:opacity-50"
            >
              {{ isDownloading ? 'Generating...' : 'Download Config' }}
            </button>
            <button
              @click="viewConfig"
              class="px-4 py-2 bg-purple-700 text-white rounded-lg hover:bg-purple-800 transition-colors"
            >
              View Config
            </button>
          </div>
        </div>
      </div>

      <!-- What is ShareX -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold text-gray-100 mb-4">What is ShareX?</h2>
        <p class="text-gray-300 mb-4">
          ShareX is a free and open source program that lets you capture or record any area of your screen 
          and share it with a single press of a key. It also allows uploading images, text or other types 
          of files to many supported destinations.
        </p>
        <div class="flex items-center space-x-4">
          <a
            href="https://getsharex.com/"
            target="_blank"
            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ExternalLink class="h-4 w-4 mr-2" />
            Download ShareX
          </a>
          <span class="text-gray-400">Free • Open Source • Windows</span>
        </div>
      </div>

      <!-- Setup Instructions -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold text-gray-100 mb-4">Setup Instructions</h2>
        <div class="space-y-4">
          <div v-for="(step, index) in setupSteps" :key="index" class="flex">
            <div class="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              {{ index + 1 }}
            </div>
            <div class="ml-4">
              <h3 class="text-gray-100 font-medium">{{ step.title }}</h3>
              <p class="text-gray-400 text-sm mt-1">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Features -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold text-gray-100 mb-4">Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-for="feature in features" :key="feature.title" class="flex items-start">
            <CheckCircle class="h-5 w-5 text-green-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 class="text-gray-100 font-medium">{{ feature.title }}</h3>
              <p class="text-gray-400 text-sm">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Troubleshooting -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold text-gray-100 mb-4">Troubleshooting</h2>
        <div class="space-y-4">
          <div v-for="issue in troubleshooting" :key="issue.problem" class="border-l-4 border-yellow-400 pl-4">
            <h3 class="text-gray-100 font-medium">{{ issue.problem }}</h3>
            <p class="text-gray-400 text-sm mt-1">{{ issue.solution }}</p>
          </div>
        </div>
      </div>

      <!-- Configuration Modal -->
      <Modal v-if="showConfigModal" @close="showConfigModal = false">
        <div class="bg-gray-800 p-6 rounded-lg max-w-2xl w-full">
          <h3 class="text-lg font-medium text-gray-100 mb-4">ShareX Configuration</h3>
          <div class="bg-gray-900 p-4 rounded-lg">
            <pre class="text-sm text-gray-300 overflow-x-auto">{{ JSON.stringify(configData, null, 2) }}</pre>
          </div>
          <div class="flex justify-between items-center mt-4">
            <p class="text-sm text-gray-400">Copy this configuration and import it into ShareX</p>
            <div class="space-x-3">
              <button
                @click="copyConfig"
                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                <Copy class="h-4 w-4 inline mr-2" />
                Copy
              </button>
              <button
                @click="showConfigModal = false"
                class="px-4 py-2 text-gray-300 hover:text-gray-100 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  </LayoutDashboard>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LayoutDashboard from '../components/LayoutDashboard.vue'
import Modal from '../components/ui/Modal.vue'
import { ExternalLink, CheckCircle, Copy } from 'lucide-vue-next'
import api from '../config/api.js'
import Swal from 'sweetalert2'

const isDownloading = ref(false)
const showConfigModal = ref(false)
const configData = ref(null)

const setupSteps = [
  {
    title: 'Download and install ShareX',
    description: 'Get ShareX from the official website (getsharex.com) and install it on your Windows computer.'
  },
  {
    title: 'Download your configuration',
    description: 'Click the "Download Config" button above to get your personal AveImgCloud configuration file.'
  },
  {
    title: 'Import configuration',
    description: 'Open ShareX, go to Destinations → Custom uploader settings, click "Import from file" and select your downloaded .sxcu file.'
  },
  {
    title: 'Select AveImgCloud',
    description: 'Go to Destinations → Image uploader and select "AveImgCloud" from the list.'
  },
  {
    title: 'Start uploading!',
    description: 'Use Print Screen or any ShareX hotkey to capture and automatically upload images to your AveImgCloud account.'
  }
]

const features = [
  {
    title: 'Automatic Upload',
    description: 'Images are automatically uploaded to your AveImgCloud account after capture'
  },
  {
    title: 'Direct Links',
    description: 'Image links are copied to your clipboard immediately after upload'
  },
  {
    title: 'Dashboard Integration',
    description: 'All uploads appear in your AveImgCloud dashboard for management'
  },
  {
    title: 'Format Support',
    description: 'Supports all image formats that AveImgCloud accepts'
  },
  {
    title: 'Secure Authentication',
    description: 'Uses your session token for secure, authenticated uploads'
  },
  {
    title: 'Hotkey Support',
    description: 'Works with all ShareX hotkeys and capture methods'
  }
]

const troubleshooting = [
  {
    problem: 'Upload fails with authentication error',
    solution: 'Re-download your configuration file from AveImgCloud. Your session token may have expired.'
  },
  {
    problem: 'ShareX says "Custom uploader not found"',
    solution: 'Make sure you\'ve imported the configuration file and selected AveImgCloud as your image uploader.'
  },
  {
    problem: 'Images upload but don\'t appear in dashboard',
    solution: 'Check that you\'re logged into the correct AveImgCloud account and refresh your dashboard.'
  },
  {
    problem: 'ShareX shows "Upload failed" error',
    solution: 'Check your internet connection and ensure you have sufficient storage space in your AveImgCloud account.'
  }
]

const downloadConfig = async () => {
  try {
    isDownloading.value = true
    const response = await api.get('/sharex/config/download')
    
    // Create and download file
    const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'AveImgCloud.sxcu'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    Swal.fire('Success', 'Configuration file downloaded successfully!', 'success')
  } catch (error) {
    console.error('Failed to download config:', error)
    Swal.fire('Error', 'Failed to download configuration', 'error')
  } finally {
    isDownloading.value = false
  }
}

const viewConfig = async () => {
  try {
    const response = await api.get('/sharex/config')
    if (response.data.success) {
      configData.value = response.data.config
      showConfigModal.value = true
    }
  } catch (error) {
    console.error('Failed to get config:', error)
    Swal.fire('Error', 'Failed to load configuration', 'error')
  }
}

const copyConfig = async () => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(configData.value, null, 2))
    Swal.fire('Copied!', 'Configuration copied to clipboard', 'success')
  } catch (error) {
    console.error('Failed to copy:', error)
    Swal.fire('Error', 'Failed to copy configuration', 'error')
  }
}
</script>
