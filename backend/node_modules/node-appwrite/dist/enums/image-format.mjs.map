{"version": 3, "sources": ["../../src/enums/image-format.ts"], "names": ["ImageFormat"], "mappings": ";AAAO,IAAK,cAAL,kBAAKA,iBAAL;AACH,EAAAA,aAAA,SAAM;AACN,EAAAA,aAAA,UAAO;AACP,EAAAA,aAAA,SAAM;AACN,EAAAA,aAAA,SAAM;AACN,EAAAA,aAAA,UAAO;AALC,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum ImageFormat {\n    Jpg = 'jpg',\n    Jpeg = 'jpeg',\n    Gif = 'gif',\n    Png = 'png',\n    Webp = 'webp',\n}"]}