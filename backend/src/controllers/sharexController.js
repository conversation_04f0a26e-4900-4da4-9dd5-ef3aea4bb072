import { UserService } from '../services/userService.js';

class ShareXController {
  // Generate ShareX configuration for user
  async generateConfig(req, res) {
    try {
      const userId = req.user.$id;
      const userProfile = await UserService.getUserProfile(userId);
      
      // Get user's session token for authentication
      const sessionToken = req.sessionId;
      
      const config = {
        "Version": "15.0.0",
        "Name": "AveImgCloud",
        "DestinationType": "ImageUploader",
        "RequestMethod": "POST",
        "RequestURL": `${process.env.FRONTEND_URL || 'http://localhost:5000'}/api/images/sharex`,
        "Headers": {
          "Authorization": `Bearer ${sessionToken}`,
          "User-Agent": "ShareX/AveImgCloud"
        },
        "Body": "MultipartFormData",
        "FileFormName": "image",
        "URL": "$json:image.sharex_url$",
        "ErrorMessage": "$json:error$"
      };

      res.json({
        success: true,
        config,
        instructions: {
          download: "Download this configuration and import it into ShareX",
          setup: [
            "1. Copy the configuration below",
            "2. Open ShareX",
            "3. Go to Destinations > Custom uploader settings",
            "4. Click 'Import' and paste the configuration",
            "5. Select 'AveImgCloud' as your image uploader",
            "6. Start uploading!"
          ]
        }
      });
    } catch (error) {
      console.error('Generate ShareX config error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to generate ShareX configuration'
      });
    }
  }

  // Download ShareX configuration file
  async downloadConfig(req, res) {
    try {
      const userId = req.user.$id;
      const userProfile = await UserService.getUserProfile(userId);
      
      // Get user's session token for authentication
      const sessionToken = req.sessionId;
      
      const config = {
        "Version": "15.0.0",
        "Name": "AveImgCloud",
        "DestinationType": "ImageUploader",
        "RequestMethod": "POST",
        "RequestURL": `${process.env.FRONTEND_URL || 'http://localhost:5000'}/api/images/sharex`,
        "Headers": {
          "Authorization": `Bearer ${sessionToken}`,
          "User-Agent": "ShareX/AveImgCloud"
        },
        "Body": "MultipartFormData",
        "FileFormName": "image",
        "URL": "$json:image.sharex_url$",
        "ErrorMessage": "$json:error$"
      };

      // Set headers for file download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename="AveImgCloud.sxcu"');
      
      res.json(config);
    } catch (error) {
      console.error('Download ShareX config error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to download ShareX configuration'
      });
    }
  }

  // Get ShareX documentation
  async getDocumentation(req, res) {
    try {
      const documentation = {
        title: "ShareX Integration Guide",
        description: "Learn how to set up ShareX with AveImgCloud for seamless image uploading",
        sections: [
          {
            title: "What is ShareX?",
            content: "ShareX is a free and open source program that lets you capture or record any area of your screen and share it with a single press of a key. It also allows uploading images, text or other types of files to many supported destinations."
          },
          {
            title: "Setting up ShareX with AveImgCloud",
            steps: [
              "1. Download and install ShareX from https://getsharex.com/",
              "2. Log in to your AveImgCloud account",
              "3. Navigate to the ShareX configuration page",
              "4. Click 'Download Configuration' to get your personal config file",
              "5. Open ShareX and go to Destinations > Custom uploader settings",
              "6. Click 'Import from file' and select your downloaded .sxcu file",
              "7. Go to Destinations > Image uploader and select 'AveImgCloud'",
              "8. You're ready to upload! Use Print Screen or any ShareX hotkey"
            ]
          },
          {
            title: "Features",
            features: [
              "Automatic image uploading to your AveImgCloud account",
              "Direct links copied to clipboard after upload",
              "All uploads appear in your AveImgCloud dashboard",
              "Supports all image formats supported by AveImgCloud",
              "Secure authentication using your session token"
            ]
          },
          {
            title: "Troubleshooting",
            issues: [
              {
                problem: "Upload fails with authentication error",
                solution: "Re-download your configuration file from AveImgCloud. Your session token may have expired."
              },
              {
                problem: "ShareX says 'Custom uploader not found'",
                solution: "Make sure you've imported the configuration file and selected AveImgCloud as your image uploader."
              },
              {
                problem: "Images upload but don't appear in dashboard",
                solution: "Check that you're logged into the correct AveImgCloud account and refresh your dashboard."
              }
            ]
          }
        ],
        links: {
          sharex_download: "https://getsharex.com/",
          support: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/support`,
          dashboard: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard`
        }
      };

      res.json({
        success: true,
        documentation
      });
    } catch (error) {
      console.error('Get ShareX documentation error:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get documentation'
      });
    }
  }
}

export default new ShareXController();
