{"version": 3, "sources": ["../../src/enums/o-auth-provider.ts"], "names": ["OAuth<PERSON><PERSON><PERSON>"], "mappings": "AAAO,IAAK,gBAAL,kBAAKA,mBAAL;AACH,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,eAAY;AACZ,EAAAA,eAAA,cAAW;AACX,EAAAA,eAAA,eAAY;AACZ,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,SAAM;AACN,EAAAA,eAAA,iBAAc;AACd,EAAAA,eAAA,aAAU;AACV,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,aAAU;AACV,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,cAAW;AACX,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,cAAW;AACX,EAAAA,eAAA,eAAY;AACZ,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,mBAAgB;AAChB,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,gBAAa;AACb,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,aAAU;AACV,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,gBAAa;AACb,EAAAA,eAAA,mBAAgB;AAChB,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,eAAY;AACZ,EAAAA,eAAA,WAAQ;AACR,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,YAAS;AACT,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,UAAO;AACP,EAAAA,eAAA,UAAO;AAvCC,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum OAuthProvider {\n    Amazon = 'amazon',\n    Apple = 'apple',\n    Auth0 = 'auth0',\n    Authentik = 'authentik',\n    Autodesk = 'autodesk',\n    Bitbucket = 'bitbucket',\n    Bitly = 'bitly',\n    Box = 'box',\n    Dailymotion = 'dailymotion',\n    Discord = 'discord',\n    Disqus = 'disqus',\n    Dropbox = 'dropbox',\n    Etsy = 'etsy',\n    Facebook = 'facebook',\n    Github = 'github',\n    Gitlab = 'gitlab',\n    Google = 'google',\n    Linkedin = 'linkedin',\n    Microsoft = 'microsoft',\n    Notion = 'notion',\n    Oidc = 'oidc',\n    Okta = 'okta',\n    Paypal = 'paypal',\n    PaypalSandbox = 'paypalSandbox',\n    Podio = 'podio',\n    Salesforce = 'salesforce',\n    Slack = 'slack',\n    Spotify = 'spotify',\n    Stripe = 'stripe',\n    Tradeshift = 'tradeshift',\n    TradeshiftBox = 'tradeshiftBox',\n    Twitch = 'twitch',\n    Wordpress = 'wordpress',\n    Yahoo = 'yahoo',\n    Yammer = 'yammer',\n    Yandex = 'yandex',\n    Zoho = 'zoho',\n    Zoom = 'zoom',\n    Mock = 'mock',\n}"]}