{"version": 3, "names": ["_setPrototypeOf", "require", "_inherits", "_wrapRegExp", "exports", "default", "re", "groups", "BabelRegExp", "undefined", "_super", "RegExp", "prototype", "_groups", "WeakMap", "flags", "_this", "set", "get", "setPrototypeOf", "inherits", "exec", "str", "result", "call", "buildGroups", "indices", "Symbol", "replace", "substitution", "_", "name", "group", "Array", "isArray", "join", "args", "arguments", "length", "slice", "push", "apply", "g", "Object", "keys", "reduce", "i", "k", "create"], "sources": ["../../src/helpers/wrapRegExp.ts"], "sourcesContent": ["/* @minVersion 7.19.0 */\n\nimport setPrototypeOf from \"./setPrototypeOf.ts\";\nimport inherits from \"./inherits.ts\";\n\n// Define interfaces for clarity and type safety\ninterface GroupMap {\n  [key: string]: string | [number, number];\n}\n\ndeclare class BabelRegExp extends RegExp {\n  exec(str: string): RegExpExecArray | null;\n  [Symbol.replace](str: string, substitution: string | Function): string;\n}\n\ninterface BabelRegExpConstructor {\n  new (re: RegExp, flags?: string, groups?: GroupMap): BabelRegExp;\n  readonly prototype: BabelRegExp;\n}\n\nexport default function _wrapRegExp(this: any): RegExp {\n  // @ts-expect-error -- deliberately re-assign\n  _wrapRegExp = function (re: RegExp, groups?: GroupMap): RegExp {\n    return new (BabelRegExp as any as BabelRegExpConstructor)(\n      re,\n      undefined,\n      groups,\n    );\n  };\n\n  var _super = RegExp.prototype;\n  var _groups = new WeakMap<RegExp, GroupMap>();\n\n  function BabelRegExp(\n    this: BabelRegExp,\n    re: RegExp,\n    flags?: string,\n    groups?: GroupMap,\n  ) {\n    var _this = new RegExp(re, flags);\n    // if the regex is re-created with 'g' flag\n    _groups.set(_this, groups || _groups.get(re)!);\n    return setPrototypeOf(_this, BabelRegExp.prototype) as BabelRegExp;\n  }\n  inherits(BabelRegExp, RegExp);\n\n  BabelRegExp.prototype.exec = function (\n    this: BabelRegExp,\n    str: string,\n  ): RegExpExecArray | null {\n    var result = _super.exec.call(this, str);\n    if (result) {\n      result.groups = buildGroups(result, this);\n      var indices = result.indices;\n      if (indices) indices.groups = buildGroups(indices, this);\n    }\n    return result;\n  };\n\n  BabelRegExp.prototype[Symbol.replace] = function (\n    this: BabelRegExp,\n    str: string,\n    substitution: string | Function,\n  ): string {\n    if (typeof substitution === \"string\") {\n      var groups = _groups.get(this)!;\n      return (\n        _super[Symbol.replace] as (\n          string: string,\n          replaceValue: string,\n        ) => string\n      ).call(\n        this,\n        str,\n        substitution.replace(/\\$<([^>]+)>/g, function (_, name) {\n          var group = groups[name];\n          return \"$\" + (Array.isArray(group) ? group.join(\"$\") : group);\n        }),\n      );\n    } else if (typeof substitution === \"function\") {\n      var _this = this;\n      return (\n        _super[Symbol.replace] as (\n          string: string,\n          replacer: (substring: string, ...args: any[]) => string,\n        ) => string\n      ).call(this, str, function (this: any) {\n        var args: IArguments | any[] = arguments;\n        // Modern engines already pass result.groups returned by exec() as the last arg.\n        if (typeof args[args.length - 1] !== \"object\") {\n          args = [].slice.call(args) as any[];\n          args.push(buildGroups(args, _this));\n        }\n        return substitution.apply(this, args);\n      });\n    } else {\n      return _super[Symbol.replace].call(this, str, substitution);\n    }\n  };\n\n  function buildGroups(\n    result: RegExpExecArray,\n    re: RegExp,\n  ): Record<string, string>;\n  function buildGroups(\n    result: RegExpIndicesArray,\n    re: RegExp,\n  ): Record<string, [number, number]>;\n  function buildGroups(\n    result: RegExpExecArray | RegExpIndicesArray,\n    re: RegExp,\n  ): Record<string, string> | Record<string, [number, number]> {\n    var g = _groups.get(re)!;\n    return Object.keys(g).reduce(function (groups, name) {\n      var i = g[name];\n      if (typeof i === \"number\") groups[name] = result[i];\n      else {\n        var k = 0;\n        while (\n          result[(i as [number, number])[k]] === undefined &&\n          k + 1 < i.length\n        ) {\n          k++;\n        }\n        groups[name] = result[(i as [number, number])[k]];\n      }\n      return groups;\n    }, Object.create(null));\n  }\n\n  return _wrapRegExp.apply(this, arguments as any);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,eAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAiBe,SAASE,WAAWA,CAAA,EAAoB;EAErDC,OAAA,CAAAC,OAAA,GAAAF,WAAW,GAAG,SAAAA,CAAUG,EAAU,EAAEC,MAAiB,EAAU;IAC7D,OAAO,IAAKC,WAAW,CACrBF,EAAE,EACFG,SAAS,EACTF,MACF,CAAC;EACH,CAAC;EAED,IAAIG,MAAM,GAAGC,MAAM,CAACC,SAAS;EAC7B,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAAmB,CAAC;EAE7C,SAASN,WAAWA,CAElBF,EAAU,EACVS,KAAc,EACdR,MAAiB,EACjB;IACA,IAAIS,KAAK,GAAG,IAAIL,MAAM,CAACL,EAAE,EAAES,KAAK,CAAC;IAEjCF,OAAO,CAACI,GAAG,CAACD,KAAK,EAAET,MAAM,IAAIM,OAAO,CAACK,GAAG,CAACZ,EAAE,CAAE,CAAC;IAC9C,OAAO,IAAAa,uBAAc,EAACH,KAAK,EAAER,WAAW,CAACI,SAAS,CAAC;EACrD;EACA,IAAAQ,iBAAQ,EAACZ,WAAW,EAAEG,MAAM,CAAC;EAE7BH,WAAW,CAACI,SAAS,CAACS,IAAI,GAAG,UAE3BC,GAAW,EACa;IACxB,IAAIC,MAAM,GAAGb,MAAM,CAACW,IAAI,CAACG,IAAI,CAAC,IAAI,EAAEF,GAAG,CAAC;IACxC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAChB,MAAM,GAAGkB,WAAW,CAACF,MAAM,EAAE,IAAI,CAAC;MACzC,IAAIG,OAAO,GAAGH,MAAM,CAACG,OAAO;MAC5B,IAAIA,OAAO,EAAEA,OAAO,CAACnB,MAAM,GAAGkB,WAAW,CAACC,OAAO,EAAE,IAAI,CAAC;IAC1D;IACA,OAAOH,MAAM;EACf,CAAC;EAEDf,WAAW,CAACI,SAAS,CAACe,MAAM,CAACC,OAAO,CAAC,GAAG,UAEtCN,GAAW,EACXO,YAA+B,EACvB;IACR,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpC,IAAItB,MAAM,GAAGM,OAAO,CAACK,GAAG,CAAC,IAAI,CAAE;MAC/B,OACER,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAItBJ,IAAI,CACJ,IAAI,EACJF,GAAG,EACHO,YAAY,CAACD,OAAO,CAAC,cAAc,EAAE,UAAUE,CAAC,EAAEC,IAAI,EAAE;QACtD,IAAIC,KAAK,GAAGzB,MAAM,CAACwB,IAAI,CAAC;QACxB,OAAO,GAAG,IAAIE,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGH,KAAK,CAAC;MAC/D,CAAC,CACH,CAAC;IACH,CAAC,MAAM,IAAI,OAAOH,YAAY,KAAK,UAAU,EAAE;MAC7C,IAAIb,KAAK,GAAG,IAAI;MAChB,OACEN,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAItBJ,IAAI,CAAC,IAAI,EAAEF,GAAG,EAAE,YAAqB;QACrC,IAAIc,IAAwB,GAAGC,SAAS;QAExC,IAAI,OAAOD,IAAI,CAACA,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;UAC7CF,IAAI,GAAG,EAAE,CAACG,KAAK,CAACf,IAAI,CAACY,IAAI,CAAU;UACnCA,IAAI,CAACI,IAAI,CAACf,WAAW,CAACW,IAAI,EAAEpB,KAAK,CAAC,CAAC;QACrC;QACA,OAAOa,YAAY,CAACY,KAAK,CAAC,IAAI,EAAEL,IAAI,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO1B,MAAM,CAACiB,MAAM,CAACC,OAAO,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAEF,GAAG,EAAEO,YAAY,CAAC;IAC7D;EACF,CAAC;EAUD,SAASJ,WAAWA,CAClBF,MAA4C,EAC5CjB,EAAU,EACiD;IAC3D,IAAIoC,CAAC,GAAG7B,OAAO,CAACK,GAAG,CAACZ,EAAE,CAAE;IACxB,OAAOqC,MAAM,CAACC,IAAI,CAACF,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUtC,MAAM,EAAEwB,IAAI,EAAE;MACnD,IAAIe,CAAC,GAAGJ,CAAC,CAACX,IAAI,CAAC;MACf,IAAI,OAAOe,CAAC,KAAK,QAAQ,EAAEvC,MAAM,CAACwB,IAAI,CAAC,GAAGR,MAAM,CAACuB,CAAC,CAAC,CAAC,KAC/C;QACH,IAAIC,CAAC,GAAG,CAAC;QACT,OACExB,MAAM,CAAEuB,CAAC,CAAsBC,CAAC,CAAC,CAAC,KAAKtC,SAAS,IAChDsC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAACR,MAAM,EAChB;UACAS,CAAC,EAAE;QACL;QACAxC,MAAM,CAACwB,IAAI,CAAC,GAAGR,MAAM,CAAEuB,CAAC,CAAsBC,CAAC,CAAC,CAAC;MACnD;MACA,OAAOxC,MAAM;IACf,CAAC,EAAEoC,MAAM,CAACK,MAAM,CAAC,IAAI,CAAC,CAAC;EACzB;EAEA,OAAO7C,WAAW,CAACsC,KAAK,CAAC,IAAI,EAAEJ,SAAgB,CAAC;AAClD", "ignoreList": []}