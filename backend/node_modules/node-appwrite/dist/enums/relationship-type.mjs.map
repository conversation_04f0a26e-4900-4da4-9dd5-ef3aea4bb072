{"version": 3, "sources": ["../../src/enums/relationship-type.ts"], "names": ["RelationshipType"], "mappings": ";AAAO,IAAK,mBAAL,kBAAKA,sBAAL;AACH,EAAAA,kBAAA,cAAW;AACX,EAAAA,kBAAA,eAAY;AACZ,EAAAA,kBAAA,gBAAa;AACb,EAAAA,kBAAA,eAAY;AAJJ,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum RelationshipType {\n    OneToOne = 'oneToOne',\n    ManyToOne = 'manyToOne',\n    ManyToMany = 'manyToMany',\n    OneToMany = 'oneToMany',\n}"]}