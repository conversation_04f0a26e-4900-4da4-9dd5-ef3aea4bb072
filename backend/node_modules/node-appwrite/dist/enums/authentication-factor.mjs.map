{"version": 3, "sources": ["../../src/enums/authentication-factor.ts"], "names": ["AuthenticationFactor"], "mappings": ";AAAO,IAAK,uBAAL,kBAAKA,0BAAL;AACH,EAAAA,sBAAA,WAAQ;AACR,EAAAA,sBAAA,WAAQ;AACR,EAAAA,sBAAA,UAAO;AACP,EAAAA,sBAAA,kBAAe;AAJP,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum AuthenticationFactor {\n    Email = 'email',\n    Phone = 'phone',\n    Totp = 'totp',\n    Recoverycode = 'recoverycode',\n}"]}