'use strict';

var PasswordHash = /* @__PURE__ */ ((PasswordHash2) => {
  PasswordHash2["Sha1"] = "sha1";
  PasswordHash2["Sha224"] = "sha224";
  PasswordHash2["Sha256"] = "sha256";
  PasswordHash2["Sha384"] = "sha384";
  PasswordHash2["Sha512224"] = "sha512/224";
  PasswordHash2["Sha512256"] = "sha512/256";
  PasswordHash2["Sha512"] = "sha512";
  PasswordHash2["Sha3224"] = "sha3-224";
  PasswordHash2["Sha3256"] = "sha3-256";
  PasswordHash2["Sha3384"] = "sha3-384";
  PasswordHash2["Sha3512"] = "sha3-512";
  return PasswordHash2;
})(PasswordHash || {});

exports.PasswordHash = PasswordHash;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=password-hash.js.map