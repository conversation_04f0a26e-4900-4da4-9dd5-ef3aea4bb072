{"version": 3, "sources": ["../src/inputFile.ts"], "names": [], "mappings": ";AAAA,SAAS,YAAY;AACrB,SAAS,cAAc,oBAAoB;AAGpC,IAAM,YAAN,MAAgB;AAAA,EACrB,OAAO,WACL,OACA,MACM;AACN,WAAO,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;AAAA,EAC/B;AAAA,EAEA,OAAO,SAAS,MAAc,MAAoB;AAChD,UAAM,WAAW,aAAa,IAAI;AAClC,UAAM,WAAW,aAAa,QAAQ;AACtC,WAAO,KAAK,WAAW,UAAU,IAAI;AAAA,EACvC;AAAA,EAEA,OAAO,cAAc,SAAiB,MAAoB;AACxD,UAAM,aAAa,IAAI,YAAY,EAAE,OAAO,OAAO;AACnD,WAAO,KAAK,WAAW,YAAY,IAAI;AAAA,EACzC;AACF", "sourcesContent": ["import { File } from \"node-fetch-native-with-agent\";\nimport { realpathSync, readFileSync } from \"fs\";\nimport type { BinaryLike } from \"crypto\";\n\nexport class InputFile {\n  static fromBuffer(\n    parts: Blob | BinaryLike,\n    name: string\n  ): File {\n    return new File([parts], name);\n  }\n\n  static fromPath(path: string, name: string): File {\n    const realPath = realpathSync(path);\n    const contents = readFileSync(realPath);\n    return this.fromBuffer(contents, name);\n  }\n\n  static fromPlainText(content: string, name: string): File {\n    const arrayBytes = new TextEncoder().encode(content);\n    return this.fromBuffer(arrayBytes, name);\n  }\n}\n"]}