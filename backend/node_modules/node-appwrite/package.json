{"name": "node-appwrite", "homepage": "https://appwrite.io/support", "description": "Appwrite is an open-source self-hosted backend server that abstract and simplify complex and repetitive development tasks behind a very simple REST API", "version": "13.0.0", "license": "BSD-3-<PERSON><PERSON>", "main": "dist/index.js", "type": "commonjs", "scripts": {"build": "tsup"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./file": {"import": {"types": "./dist/inputFile.d.mts", "default": "./dist/inputFile.mjs"}, "require": {"types": "./dist/inputFile.d.ts", "default": "./dist/inputFile.js"}}}, "files": ["dist"], "module": "dist/index.mjs", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/appwrite/sdk-for-node"}, "devDependencies": {"@types/node": "20.11.25", "tsup": "7.2.0", "esbuild-plugin-file-path-extensions": "^2.0.0", "tslib": "2.6.2", "typescript": "5.4.2"}, "dependencies": {"node-fetch-native-with-agent": "1.7.2"}}