<template>
  <Layout>
    <div class="text-center py-12">
      <div v-if="isLoading" class="space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Completing sign-in...
        </h2>
        <p class="text-gray-500 dark:text-gray-400">
          Please wait while we complete your authentication.
        </p>
      </div>
      
      <div v-else-if="error" class="space-y-4">
        <div class="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
          <AlertCircle class="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Authentication Failed
        </h2>
        <p class="text-gray-500 dark:text-gray-400">
          {{ error }}
        </p>
        <router-link
          to="/auth/login"
          class="inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Try Again
        </router-link>
      </div>
      
      <div v-else class="space-y-4">
        <div class="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
          <CheckCircle class="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
          Authentication Successful!
        </h2>
        <p class="text-gray-500 dark:text-gray-400">
          Redirecting to dashboard...
        </p>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth.js'
import Layout from '../../components/Layout.vue'
import { CheckCircle, AlertCircle } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const isLoading = ref(true)
const error = ref(null)

onMounted(async () => {
  try {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    
    // Handle OAuth callback
    const result = await authStore.handleOAuthCallback(urlParams);
    
    if (result.success) {
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 2000)
    } else {
      error.value = result.error
    }
  } catch (err) {
    console.error('OAuth callback error:', err);
    error.value = 'Authentication failed. Please try again.'
  } finally {
    isLoading.value = false
  }
})
</script>
