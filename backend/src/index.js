import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { config } from './config/config.js';
import { generalLimiter } from './middleware/rateLimiting.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';

// Import routes
import indexRoutes from './routes/index.js';
import authRoutes from './routes/auth.js';
import imageRoutes from './routes/images.js';

dotenv.config();

const app = express();
const PORT = config.port;

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: config.frontendUrl,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Routes
app.use('/api', indexRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/images', imageRoutes);

// Direct image serving route (for /i/uuid URLs)
app.get('/i/:uuid', async (req, res) => {
  try {
    const { default: imageController } = await import('./controllers/imageController.js');
    await imageController.serveImage(req, res);
  } catch (error) {
    console.error('Direct image serve error:', error);
    res.status(404).send('Image not found');
  }
});

// Legacy routes for backward compatibility
app.get('/health', (req, res) => {
  res.redirect('/api/health');
});

// Error handling
app.use(errorHandler);
app.use(notFoundHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AveImage Cloud API server running on port ${PORT}`);
  console.log(`📊 Environment: ${config.nodeEnv}`);
  console.log(`🌐 Frontend URL: ${config.frontendUrl}`);
  console.log(`� Appwrite Project: ${config.appwrite.projectId}`);
});
