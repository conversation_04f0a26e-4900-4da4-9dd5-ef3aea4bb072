import { databases, users } from '../config/appwrite.js';
import { ID, Query } from 'node-appwrite';

const DATABASE_ID = process.env.APPWRITE_DATABASE_ID || 'main';
const USERS_COLLECTION = process.env.APPWRITE_USERS_COLLECTION_ID || 'users_profile';

// Default user settings
const DEFAULT_STORAGE_LIMIT = 52428800; // 50MB in bytes
const DEFAULT_CREDITS = 500; // Starting credits (updated to match requirements)
const DEFAULT_EMBED_FOOTER = "Powered by Avehub ⭐";
const DEFAULT_EMBED_COLOR = "#7C3AED";

export class UserService {
  // Create user profile after registration
  static async createUserProfile(userId, userData = {}) {
    try {
      const profile = await databases.createDocument(
        DATABASE_ID,
        USERS_COLLECTION,
        ID.unique(),
        {
          userId,
          credits: userData.credits || DEFAULT_CREDITS,
          storageUsed: 0,
          storageLimit: userData.storageLimit || DEFAULT_STORAGE_LIMIT,
          isAdmin: userData.isAdmin || false,
          canCustomizeFooter: userData.canCustomizeFooter || false,
          embedFooter: userData.embedFooter || DEFAULT_EMBED_FOOTER,
          embedColor: userData.embedColor || DEFAULT_EMBED_COLOR,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      );
      return profile;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  // Get user profile
  static async getUserProfile(userId) {
    try {
      const profiles = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION,
        [Query.equal('userId', userId)]
      );

      if (profiles.documents.length === 0) {
        // Create profile if it doesn't exist
        return await this.createUserProfile(userId);
      }

      return profiles.documents[0];
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw error;
    }
  }

  // Update user profile
  static async updateUserProfile(userId, updates) {
    try {
      const profile = await this.getUserProfile(userId);
      
      const updatedProfile = await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION,
        profile.$id,
        {
          ...updates,
          updatedAt: new Date().toISOString()
        }
      );
      
      return updatedProfile;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Add/subtract credits
  static async updateCredits(userId, amount) {
    try {
      const profile = await this.getUserProfile(userId);
      const newCredits = Math.max(0, profile.credits + amount);
      
      return await this.updateUserProfile(userId, { credits: newCredits });
    } catch (error) {
      console.error('Error updating credits:', error);
      throw error;
    }
  }

  // Update storage usage
  static async updateStorageUsage(userId, sizeChange) {
    try {
      const profile = await this.getUserProfile(userId);
      const newStorageUsed = Math.max(0, profile.storageUsed + sizeChange);
      
      return await this.updateUserProfile(userId, { storageUsed: newStorageUsed });
    } catch (error) {
      console.error('Error updating storage usage:', error);
      throw error;
    }
  }

  // Check if user has enough storage
  static async hasStorageSpace(userId, requiredSize) {
    try {
      const profile = await this.getUserProfile(userId);
      return (profile.storageUsed + requiredSize) <= profile.storageLimit;
    } catch (error) {
      console.error('Error checking storage space:', error);
      return false;
    }
  }

  // Check if user has enough credits
  static async hasCredits(userId, requiredCredits) {
    try {
      const profile = await this.getUserProfile(userId);
      return profile.credits >= requiredCredits;
    } catch (error) {
      console.error('Error checking credits:', error);
      return false;
    }
  }

  // Get user statistics
  static async getUserStats(userId) {
    try {
      const profile = await this.getUserProfile(userId);
      const user = await users.get(userId);
      
      return {
        profile,
        user,
        storageUsedMB: (profile.storageUsed / (1024 * 1024)).toFixed(2),
        storageLimitMB: (profile.storageLimit / (1024 * 1024)).toFixed(2),
        storageUsagePercent: Math.round((profile.storageUsed / profile.storageLimit) * 100)
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw error;
    }
  }

  // Admin: Get all users with profiles
  static async getAllUsersWithProfiles(limit = 50, offset = 0) {
    try {
      const profiles = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION,
        [Query.limit(limit), Query.offset(offset)]
      );

      const usersWithProfiles = await Promise.all(
        profiles.documents.map(async (profile) => {
          try {
            const user = await users.get(profile.userId);
            return {
              ...profile,
              user
            };
          } catch (error) {
            console.error(`Error getting user ${profile.userId}:`, error);
            return {
              ...profile,
              user: null
            };
          }
        })
      );

      return {
        users: usersWithProfiles,
        total: profiles.total
      };
    } catch (error) {
      console.error('Error getting all users with profiles:', error);
      throw error;
    }
  }

  // Admin: Get system statistics
  static async getSystemStats() {
    try {
      const allProfiles = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION,
        [Query.limit(1000)]
      );

      const totalUsers = allProfiles.total;
      const totalStorageUsed = allProfiles.documents.reduce((sum, profile) => sum + (profile.storageUsed || 0), 0);
      const totalCredits = allProfiles.documents.reduce((sum, profile) => sum + (profile.credits || 0), 0);
      const totalStorageLimit = allProfiles.documents.reduce((sum, profile) => sum + (profile.storageLimit || 0), 0);
      const adminUsers = allProfiles.documents.filter(profile => profile.isAdmin).length;

      // Get recent users (last 10)
      const recentUsers = await databases.listDocuments(
        DATABASE_ID,
        USERS_COLLECTION,
        [Query.orderDesc('createdAt'), Query.limit(10)]
      );

      // Calculate monthly stats (simplified)
      const thisMonth = new Date();
      thisMonth.setDate(1);
      const newUsersThisMonth = allProfiles.documents.filter(profile =>
        new Date(profile.createdAt) >= thisMonth
      ).length;

      return {
        totalUsers,
        totalStorageUsed,
        totalStorageLimit,
        totalStorageUsedMB: Math.round(totalStorageUsed / (1024 * 1024)),
        totalCredits,
        adminUsers,
        newUsersThisMonth,
        recentUsers: recentUsers.documents,
        averageStoragePerUser: totalUsers > 0 ? (totalStorageUsed / totalUsers / (1024 * 1024)).toFixed(2) : 0
      };
    } catch (error) {
      console.error('Error getting system stats:', error);
      throw error;
    }
  }
}
