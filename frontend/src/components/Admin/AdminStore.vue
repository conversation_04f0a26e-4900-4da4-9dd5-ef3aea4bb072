<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-xl font-semibold text-gray-100">Store Management</h2>
        <p class="text-gray-400">Manage storage plans and pricing</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
      >
        Create Plan
      </button>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center">
          <Package class="h-8 w-8 text-purple-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Total Plans</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalPlans || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center">
          <CheckCircle class="h-8 w-8 text-green-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Active Plans</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.activePlans || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center">
          <ShoppingCart class="h-8 w-8 text-blue-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Total Sales</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalTransactions || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-800 p-4 rounded-lg">
        <div class="flex items-center">
          <Coins class="h-8 w-8 text-yellow-400" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-400">Revenue</p>
            <p class="text-2xl font-bold text-gray-100">{{ stats.totalRevenue || 0 }} credits</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Storage Plans Table -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-medium text-gray-100">Storage Plans</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-700">
          <thead class="bg-gray-900">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Plan Name
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Storage
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Cost
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Features
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-gray-800 divide-y divide-gray-700">
            <tr v-for="plan in plans" :key="plan.$id" class="hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-100">{{ plan.name }}</div>
                  <div class="text-sm text-gray-400">{{ plan.description }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                {{ plan.storageAmountMB }} MB
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                {{ plan.creditCost }} credits
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                <span v-if="plan.includesFooterCustomization" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Footer Customization
                </span>
                <span v-else class="text-gray-500">Standard</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="plan.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ plan.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editPlan(plan)"
                  class="text-purple-400 hover:text-purple-300"
                >
                  <Edit class="h-4 w-4" />
                </button>
                <button
                  @click="deletePlan(plan)"
                  class="text-red-400 hover:text-red-300"
                >
                  <Trash2 class="h-4 w-4" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Create/Edit Plan Modal -->
    <Modal v-if="showCreateModal || editingPlan" @close="closeModal">
      <div class="bg-gray-800 p-6 rounded-lg max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-100 mb-4">
          {{ editingPlan ? 'Edit Plan' : 'Create New Plan' }}
        </h3>
        
        <form @submit.prevent="savePlan" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Plan Name</label>
            <input
              v-model="planForm.name"
              type="text"
              required
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="e.g., Premium Storage"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
            <textarea
              v-model="planForm.description"
              rows="2"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Plan description..."
            ></textarea>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Storage Amount (MB)</label>
            <input
              v-model.number="planForm.storageAmountMB"
              type="number"
              required
              min="1"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="e.g., 1000"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-1">Credit Cost</label>
            <input
              v-model.number="planForm.creditCost"
              type="number"
              required
              min="1"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="e.g., 100"
            />
          </div>
          
          <div class="flex items-center">
            <input
              v-model="planForm.includesFooterCustomization"
              type="checkbox"
              id="footerCustomization"
              class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            <label for="footerCustomization" class="ml-2 block text-sm text-gray-300">
              Include Footer Customization
            </label>
          </div>
          
          <div class="flex items-center">
            <input
              v-model="planForm.isActive"
              type="checkbox"
              id="isActive"
              class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            <label for="isActive" class="ml-2 block text-sm text-gray-300">
              Active Plan
            </label>
          </div>
          
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-gray-300 hover:text-gray-100 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSaving"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {{ isSaving ? 'Saving...' : (editingPlan ? 'Update' : 'Create') }}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import Modal from '../ui/Modal.vue'
import { 
  Package, CheckCircle, ShoppingCart, Coins, 
  Edit, Trash2 
} from 'lucide-vue-next'
import api from '../../config/api.js'
import Swal from 'sweetalert2'

const plans = ref([])
const stats = ref({})
const isLoading = ref(false)
const isSaving = ref(false)
const showCreateModal = ref(false)
const editingPlan = ref(null)

const planForm = reactive({
  name: '',
  description: '',
  storageAmountMB: 0,
  creditCost: 0,
  includesFooterCustomization: false,
  isActive: true
})

const loadPlans = async () => {
  try {
    isLoading.value = true
    const response = await api.get('/admin/storage/plans')
    if (response.data.success) {
      plans.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load plans:', error)
    Swal.fire('Error', 'Failed to load storage plans', 'error')
  } finally {
    isLoading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await api.get('/admin/storage/stats')
    if (response.data.success) {
      stats.value = response.data.data
    }
  } catch (error) {
    console.error('Failed to load stats:', error)
  }
}

const editPlan = (plan) => {
  editingPlan.value = plan
  Object.assign(planForm, {
    name: plan.name,
    description: plan.description || '',
    storageAmountMB: plan.storageAmountMB,
    creditCost: plan.creditCost,
    includesFooterCustomization: plan.includesFooterCustomization || false,
    isActive: plan.isActive !== false
  })
}

const closeModal = () => {
  showCreateModal.value = false
  editingPlan.value = null
  Object.assign(planForm, {
    name: '',
    description: '',
    storageAmountMB: 0,
    creditCost: 0,
    includesFooterCustomization: false,
    isActive: true
  })
}

const savePlan = async () => {
  try {
    isSaving.value = true
    
    if (editingPlan.value) {
      // Update existing plan
      await api.put(`/admin/storage/plans/${editingPlan.value.$id}`, planForm)
      Swal.fire('Success', 'Plan updated successfully', 'success')
    } else {
      // Create new plan
      await api.post('/admin/storage/plans', planForm)
      Swal.fire('Success', 'Plan created successfully', 'success')
    }
    
    closeModal()
    await loadPlans()
    await loadStats()
  } catch (error) {
    console.error('Failed to save plan:', error)
    Swal.fire('Error', error.response?.data?.error || 'Failed to save plan', 'error')
  } finally {
    isSaving.value = false
  }
}

const deletePlan = async (plan) => {
  const result = await Swal.fire({
    title: 'Delete Plan',
    text: `Are you sure you want to delete "${plan.name}"?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#dc2626',
    cancelButtonColor: '#6b7280',
    confirmButtonText: 'Delete'
  })
  
  if (result.isConfirmed) {
    try {
      await api.delete(`/admin/storage/plans/${plan.$id}`)
      Swal.fire('Deleted', 'Plan deleted successfully', 'success')
      await loadPlans()
      await loadStats()
    } catch (error) {
      console.error('Failed to delete plan:', error)
      Swal.fire('Error', 'Failed to delete plan', 'error')
    }
  }
}

onMounted(() => {
  loadPlans()
  loadStats()
})
</script>
