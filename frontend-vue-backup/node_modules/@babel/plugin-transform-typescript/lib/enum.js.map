{"version": 3, "names": ["_core", "require", "_assert", "_helperAnnotateAsPure", "_helperSkipTransparentExpressionWrappers", "ENUMS", "WeakMap", "buildEnumWrapper", "template", "expression", "transpileEnum", "path", "t", "node", "parentPath", "declare", "remove", "name", "id", "fill", "data", "isPure", "enumFill", "type", "isGlobal", "isProgram", "parent", "isSeen", "seen", "init", "objectExpression", "logicalExpression", "cloneNode", "ID", "enumIIFE", "Object", "assign", "INIT", "annotateAsPure", "toReplace", "isExportDeclaration", "replaceWith", "expressionStatement", "assignmentExpression", "scope", "registerDeclaration", "variableDeclaration", "variableDeclarator", "set", "getBindingIdentifier", "Error", "getData", "setData", "buildStringAssignment", "buildNumericAssignment", "buildEnumMember", "isString", "options", "enum<PERSON><PERSON><PERSON>", "x", "translateEnumValues", "assignments", "map", "memberName", "memberValue", "isSyntacticallyString", "ENUM", "NAME", "VALUE", "ASSIGNMENTS", "expr", "skipTransparentExprWrapperNodes", "left", "right", "operator", "ReferencedIdentifier", "state", "has", "hasOwnBinding", "memberExpression", "skip", "enumSelfReferenceVisitor", "_ENUMS$get", "bindingIdentifier", "get", "Map", "constV<PERSON>ue", "lastName", "memberPath", "member", "isIdentifier", "value", "initializerPath", "initializer", "computeConstantValue", "undefined", "assert", "Infinity", "Number", "isNaN", "identifier", "String", "unaryExpression", "valueToNode", "isReferencedIdentifier", "traverse", "numericLiteral", "buildCodeFrameError", "lastRef", "stringLiteral", "binaryExpression", "prevMembers", "Set", "evaluate", "evaluateRef", "evalUnaryExpression", "evalBinaryExpression", "quasis", "length", "cooked", "paths", "str", "i", "isMemberExpression", "obj", "object", "prop", "property", "computed", "isStringLiteral", "includes", "add", "resolve", "Math", "pow"], "sources": ["../src/enum.ts"], "sourcesContent": ["import { template, types as t, type NodePath } from \"@babel/core\";\nimport assert from \"assert\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\nimport { skipTransparentExprWrapperNodes } from \"@babel/helper-skip-transparent-expression-wrappers\";\n\ntype t = typeof t;\n\nconst ENUMS = new WeakMap<t.Identifier, PreviousEnumMembers>();\n\nconst buildEnumWrapper = template.expression(\n  `\n    (function (ID) {\n      ASSIGNMENTS;\n      return ID;\n    })(INIT)\n  `,\n);\n\nexport default function transpileEnum(\n  path: NodePath<t.TSEnumDeclaration>,\n  t: t,\n) {\n  const { node, parentPath } = path;\n\n  if (node.declare) {\n    path.remove();\n    return;\n  }\n\n  const name = node.id.name;\n  const { fill, data, isPure } = enumFill(path, t, node.id);\n\n  switch (parentPath.type) {\n    case \"BlockStatement\":\n    case \"ExportNamedDeclaration\":\n    case \"Program\": {\n      // todo: Consider exclude program with import/export\n      // && !path.parent.body.some(n => t.isImportDeclaration(n) || t.isExportDeclaration(n));\n      const isGlobal = t.isProgram(path.parent);\n      const isSeen = seen(parentPath);\n\n      let init: t.Expression = t.objectExpression([]);\n      if (isSeen || isGlobal) {\n        init = t.logicalExpression(\"||\", t.cloneNode(fill.ID), init);\n      }\n      const enumIIFE = buildEnumWrapper({ ...fill, INIT: init });\n      if (isPure) annotateAsPure(enumIIFE);\n\n      if (isSeen) {\n        const toReplace = parentPath.isExportDeclaration() ? parentPath : path;\n        toReplace.replaceWith(\n          t.expressionStatement(\n            t.assignmentExpression(\"=\", t.cloneNode(node.id), enumIIFE),\n          ),\n        );\n      } else {\n        path.scope.registerDeclaration(\n          path.replaceWith(\n            t.variableDeclaration(isGlobal ? \"var\" : \"let\", [\n              t.variableDeclarator(node.id, enumIIFE),\n            ]),\n          )[0],\n        );\n      }\n      ENUMS.set(path.scope.getBindingIdentifier(name), data);\n      break;\n    }\n\n    default:\n      throw new Error(`Unexpected enum parent '${path.parent.type}`);\n  }\n\n  function seen(parentPath: NodePath<t.Node>): boolean {\n    if (parentPath.isExportDeclaration()) {\n      return seen(parentPath.parentPath);\n    }\n\n    if (parentPath.getData(name)) {\n      return true;\n    } else {\n      parentPath.setData(name, true);\n      return false;\n    }\n  }\n}\n\nconst buildStringAssignment = template(`\n  ENUM[\"NAME\"] = VALUE;\n`);\n\nconst buildNumericAssignment = template(`\n  ENUM[ENUM[\"NAME\"] = VALUE] = \"NAME\";\n`);\n\nconst buildEnumMember = (isString: boolean, options: Record<string, unknown>) =>\n  (isString ? buildStringAssignment : buildNumericAssignment)(options);\n\n/**\n * Generates the statement that fills in the variable declared by the enum.\n * `(function (E) { ... assignments ... })(E || (E = {}));`\n */\nfunction enumFill(path: NodePath<t.TSEnumDeclaration>, t: t, id: t.Identifier) {\n  const { enumValues: x, data, isPure } = translateEnumValues(path, t);\n  const assignments = x.map(([memberName, memberValue]) =>\n    buildEnumMember(isSyntacticallyString(memberValue), {\n      ENUM: t.cloneNode(id),\n      NAME: memberName,\n      VALUE: memberValue,\n    }),\n  );\n\n  return {\n    fill: {\n      ID: t.cloneNode(id),\n      ASSIGNMENTS: assignments,\n    },\n    data,\n    isPure,\n  };\n}\n\nexport function isSyntacticallyString(expr: t.Expression): boolean {\n  // @ts-ignore(Babel 7 vs Babel 8) Type 'Expression | Super' is not assignable to type 'Expression' in Babel 8\n  expr = skipTransparentExprWrapperNodes(expr);\n  switch (expr.type) {\n    case \"BinaryExpression\": {\n      const left = expr.left;\n      const right = expr.right;\n      return (\n        expr.operator === \"+\" &&\n        (isSyntacticallyString(left as t.Expression) ||\n          isSyntacticallyString(right))\n      );\n    }\n    case \"TemplateLiteral\":\n    case \"StringLiteral\":\n      return true;\n  }\n  return false;\n}\n\n/**\n * Maps the name of an enum member to its value.\n * We keep track of the previous enum members so you can write code like:\n *   enum E {\n *     X = 1 << 0,\n *     Y = 1 << 1,\n *     Z = X | Y,\n *   }\n */\ntype PreviousEnumMembers = Map<string, number | string>;\n\ntype EnumSelfReferenceVisitorState = {\n  seen: PreviousEnumMembers;\n  path: NodePath<t.TSEnumDeclaration>;\n  t: t;\n};\n\nfunction ReferencedIdentifier(\n  expr: NodePath<t.Identifier>,\n  state: EnumSelfReferenceVisitorState,\n) {\n  const { seen, path, t } = state;\n  const name = expr.node.name;\n  if (seen.has(name) && !expr.scope.hasOwnBinding(name)) {\n    expr.replaceWith(\n      t.memberExpression(t.cloneNode(path.node.id), t.cloneNode(expr.node)),\n    );\n    expr.skip();\n  }\n}\n\nconst enumSelfReferenceVisitor = {\n  ReferencedIdentifier,\n};\n\nexport function translateEnumValues(path: NodePath<t.TSEnumDeclaration>, t: t) {\n  const bindingIdentifier = path.scope.getBindingIdentifier(path.node.id.name);\n  const seen: PreviousEnumMembers = ENUMS.get(bindingIdentifier) ?? new Map();\n\n  // Start at -1 so the first enum member is its increment, 0.\n  let constValue: number | string | undefined = -1;\n  let lastName: string;\n  let isPure = true;\n\n  const enumValues: Array<[name: string, value: t.Expression]> = path\n    .get(\"members\")\n    .map(memberPath => {\n      const member = memberPath.node;\n      const name = t.isIdentifier(member.id) ? member.id.name : member.id.value;\n      const initializerPath = memberPath.get(\"initializer\");\n      const initializer = member.initializer;\n      let value: t.Expression;\n      if (initializer) {\n        constValue = computeConstantValue(initializerPath, seen);\n        if (constValue !== undefined) {\n          seen.set(name, constValue);\n          assert(\n            typeof constValue === \"number\" || typeof constValue === \"string\",\n          );\n          // We do not use `t.valueToNode` because `Infinity`/`NaN` might refer\n          // to a local variable. Even 1/0\n          // Revisit once https://github.com/microsoft/TypeScript/issues/55091\n          // is fixed. Note: we will have to distinguish between actual\n          // infinities and reference  to non-infinite variables names Infinity.\n          if (constValue === Infinity || Number.isNaN(constValue)) {\n            value = t.identifier(String(constValue));\n          } else if (constValue === -Infinity) {\n            value = t.unaryExpression(\"-\", t.identifier(\"Infinity\"));\n          } else {\n            value = t.valueToNode(constValue);\n          }\n        } else {\n          isPure &&= initializerPath.isPure();\n\n          if (initializerPath.isReferencedIdentifier()) {\n            ReferencedIdentifier(initializerPath, {\n              t,\n              seen,\n              path,\n            });\n          } else {\n            initializerPath.traverse(enumSelfReferenceVisitor, {\n              t,\n              seen,\n              path,\n            });\n          }\n\n          value = initializerPath.node;\n          seen.set(name, undefined);\n        }\n      } else if (typeof constValue === \"number\") {\n        constValue += 1;\n        value = t.numericLiteral(constValue);\n        seen.set(name, constValue);\n      } else if (typeof constValue === \"string\") {\n        throw path.buildCodeFrameError(\"Enum member must have initializer.\");\n      } else {\n        // create dynamic initializer: 1 + ENUM[\"PREVIOUS\"]\n        const lastRef = t.memberExpression(\n          t.cloneNode(path.node.id),\n          t.stringLiteral(lastName),\n          true,\n        );\n        value = t.binaryExpression(\"+\", t.numericLiteral(1), lastRef);\n        seen.set(name, undefined);\n      }\n\n      lastName = name;\n      return [name, value];\n    });\n\n  return {\n    isPure,\n    data: seen,\n    enumValues,\n  };\n}\n\n// Based on the TypeScript repository's `computeConstantValue` in `checker.ts`.\nfunction computeConstantValue(\n  path: NodePath,\n  prevMembers?: PreviousEnumMembers,\n  seen: Set<t.Identifier> = new Set(),\n): number | string | undefined {\n  return evaluate(path);\n\n  function evaluate(path: NodePath): number | string | undefined {\n    const expr = path.node;\n    switch (expr.type) {\n      case \"MemberExpression\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"StringLiteral\":\n        return expr.value;\n      case \"UnaryExpression\":\n        return evalUnaryExpression(path as NodePath<t.UnaryExpression>);\n      case \"BinaryExpression\":\n        return evalBinaryExpression(path as NodePath<t.BinaryExpression>);\n      case \"NumericLiteral\":\n        return expr.value;\n      case \"ParenthesizedExpression\":\n        return evaluate(path.get(\"expression\"));\n      case \"Identifier\":\n        return evaluateRef(path, prevMembers, seen);\n      case \"TemplateLiteral\": {\n        if (expr.quasis.length === 1) {\n          return expr.quasis[0].value.cooked;\n        }\n\n        const paths = (path as NodePath<t.TemplateLiteral>).get(\"expressions\");\n        const quasis = expr.quasis;\n        let str = \"\";\n\n        for (let i = 0; i < quasis.length; i++) {\n          str += quasis[i].value.cooked;\n\n          if (i + 1 < quasis.length) {\n            const value = evaluateRef(paths[i], prevMembers, seen);\n            if (value === undefined) return undefined;\n            str += value;\n          }\n        }\n        return str;\n      }\n      default:\n        return undefined;\n    }\n  }\n\n  function evaluateRef(\n    path: NodePath,\n    prevMembers: PreviousEnumMembers,\n    seen: Set<t.Identifier>,\n  ): number | string | undefined {\n    if (path.isMemberExpression()) {\n      const expr = path.node;\n\n      const obj = expr.object;\n      const prop = expr.property;\n      if (\n        !t.isIdentifier(obj) ||\n        (expr.computed ? !t.isStringLiteral(prop) : !t.isIdentifier(prop))\n      ) {\n        return;\n      }\n      const bindingIdentifier = path.scope.getBindingIdentifier(obj.name);\n      const data = ENUMS.get(bindingIdentifier);\n      if (!data) return;\n      // @ts-expect-error checked above\n      return data.get(prop.computed ? prop.value : prop.name);\n    } else if (path.isIdentifier()) {\n      const name = path.node.name;\n\n      if ([\"Infinity\", \"NaN\"].includes(name)) {\n        return Number(name);\n      }\n\n      let value = prevMembers?.get(name);\n      if (value !== undefined) {\n        return value;\n      }\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      value = computeConstantValue(path.resolve(), prevMembers, seen);\n      prevMembers?.set(name, value);\n      return value;\n    }\n  }\n\n  function evalUnaryExpression(\n    path: NodePath<t.UnaryExpression>,\n  ): number | string | undefined {\n    const value = evaluate(path.get(\"argument\"));\n    if (value === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"+\":\n        return value;\n      case \"-\":\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-unary-minus\n        return -value;\n      case \"~\":\n        return ~value;\n      default:\n        return undefined;\n    }\n  }\n\n  function evalBinaryExpression(\n    path: NodePath<t.BinaryExpression>,\n  ): number | string | undefined {\n    const left = evaluate(path.get(\"left\")) as any;\n    if (left === undefined) {\n      return undefined;\n    }\n    const right = evaluate(path.get(\"right\")) as any;\n    if (right === undefined) {\n      return undefined;\n    }\n\n    switch (path.node.operator) {\n      case \"|\":\n        return left | right;\n      case \"&\":\n        return left & right;\n      case \">>\":\n        return left >> right;\n      case \">>>\":\n        return left >>> right;\n      case \"<<\":\n        return left << right;\n      case \"^\":\n        return left ^ right;\n      case \"*\":\n        return left * right;\n      case \"/\":\n        return left / right;\n      case \"+\":\n        return left + right;\n      case \"-\":\n        return left - right;\n      case \"%\":\n        return left % right;\n      case \"**\":\n        return left ** right;\n      default:\n        return undefined;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AACA,IAAAG,wCAAA,GAAAH,OAAA;AAIA,MAAMI,KAAK,GAAG,IAAIC,OAAO,CAAoC,CAAC;AAE9D,MAAMC,gBAAgB,GAAGC,cAAQ,CAACC,UAAU,CAC1C;AACF;AACA;AACA;AACA;AACA,GACA,CAAC;AAEc,SAASC,aAAaA,CACnCC,IAAmC,EACnCC,CAAI,EACJ;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGH,IAAI;EAEjC,IAAIE,IAAI,CAACE,OAAO,EAAE;IAChBJ,IAAI,CAACK,MAAM,CAAC,CAAC;IACb;EACF;EAEA,MAAMC,IAAI,GAAGJ,IAAI,CAACK,EAAE,CAACD,IAAI;EACzB,MAAM;IAAEE,IAAI;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGC,QAAQ,CAACX,IAAI,EAAEC,CAAC,EAAEC,IAAI,CAACK,EAAE,CAAC;EAEzD,QAAQJ,UAAU,CAACS,IAAI;IACrB,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,SAAS;MAAE;QAGd,MAAMC,QAAQ,GAAGZ,CAAC,CAACa,SAAS,CAACd,IAAI,CAACe,MAAM,CAAC;QACzC,MAAMC,MAAM,GAAGC,IAAI,CAACd,UAAU,CAAC;QAE/B,IAAIe,IAAkB,GAAGjB,CAAC,CAACkB,gBAAgB,CAAC,EAAE,CAAC;QAC/C,IAAIH,MAAM,IAAIH,QAAQ,EAAE;UACtBK,IAAI,GAAGjB,CAAC,CAACmB,iBAAiB,CAAC,IAAI,EAAEnB,CAAC,CAACoB,SAAS,CAACb,IAAI,CAACc,EAAE,CAAC,EAAEJ,IAAI,CAAC;QAC9D;QACA,MAAMK,QAAQ,GAAG3B,gBAAgB,CAAA4B,MAAA,CAAAC,MAAA,KAAMjB,IAAI;UAAEkB,IAAI,EAAER;QAAI,EAAE,CAAC;QAC1D,IAAIR,MAAM,EAAE,IAAAiB,6BAAc,EAACJ,QAAQ,CAAC;QAEpC,IAAIP,MAAM,EAAE;UACV,MAAMY,SAAS,GAAGzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,GAAG1B,UAAU,GAAGH,IAAI;UACtE4B,SAAS,CAACE,WAAW,CACnB7B,CAAC,CAAC8B,mBAAmB,CACnB9B,CAAC,CAAC+B,oBAAoB,CAAC,GAAG,EAAE/B,CAAC,CAACoB,SAAS,CAACnB,IAAI,CAACK,EAAE,CAAC,EAAEgB,QAAQ,CAC5D,CACF,CAAC;QACH,CAAC,MAAM;UACLvB,IAAI,CAACiC,KAAK,CAACC,mBAAmB,CAC5BlC,IAAI,CAAC8B,WAAW,CACd7B,CAAC,CAACkC,mBAAmB,CAACtB,QAAQ,GAAG,KAAK,GAAG,KAAK,EAAE,CAC9CZ,CAAC,CAACmC,kBAAkB,CAAClC,IAAI,CAACK,EAAE,EAAEgB,QAAQ,CAAC,CACxC,CACH,CAAC,CAAC,CAAC,CACL,CAAC;QACH;QACA7B,KAAK,CAAC2C,GAAG,CAACrC,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAAChC,IAAI,CAAC,EAAEG,IAAI,CAAC;QACtD;MACF;IAEA;MACE,MAAM,IAAI8B,KAAK,CAAC,2BAA2BvC,IAAI,CAACe,MAAM,CAACH,IAAI,EAAE,CAAC;EAClE;EAEA,SAASK,IAAIA,CAACd,UAA4B,EAAW;IACnD,IAAIA,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,EAAE;MACpC,OAAOZ,IAAI,CAACd,UAAU,CAACA,UAAU,CAAC;IACpC;IAEA,IAAIA,UAAU,CAACqC,OAAO,CAAClC,IAAI,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb,CAAC,MAAM;MACLH,UAAU,CAACsC,OAAO,CAACnC,IAAI,EAAE,IAAI,CAAC;MAC9B,OAAO,KAAK;IACd;EACF;AACF;AAEA,MAAMoC,qBAAqB,GAAG,IAAA7C,cAAQ,EAAC;AACvC;AACA,CAAC,CAAC;AAEF,MAAM8C,sBAAsB,GAAG,IAAA9C,cAAQ,EAAC;AACxC;AACA,CAAC,CAAC;AAEF,MAAM+C,eAAe,GAAGA,CAACC,QAAiB,EAAEC,OAAgC,KAC1E,CAACD,QAAQ,GAAGH,qBAAqB,GAAGC,sBAAsB,EAAEG,OAAO,CAAC;AAMtE,SAASnC,QAAQA,CAACX,IAAmC,EAAEC,CAAI,EAAEM,EAAgB,EAAE;EAC7E,MAAM;IAAEwC,UAAU,EAAEC,CAAC;IAAEvC,IAAI;IAAEC;EAAO,CAAC,GAAGuC,mBAAmB,CAACjD,IAAI,EAAEC,CAAC,CAAC;EACpE,MAAMiD,WAAW,GAAGF,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,WAAW,CAAC,KAClDT,eAAe,CAACU,qBAAqB,CAACD,WAAW,CAAC,EAAE;IAClDE,IAAI,EAAEtD,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;IACrBiD,IAAI,EAAEJ,UAAU;IAChBK,KAAK,EAAEJ;EACT,CAAC,CACH,CAAC;EAED,OAAO;IACL7C,IAAI,EAAE;MACJc,EAAE,EAAErB,CAAC,CAACoB,SAAS,CAACd,EAAE,CAAC;MACnBmD,WAAW,EAAER;IACf,CAAC;IACDzC,IAAI;IACJC;EACF,CAAC;AACH;AAEO,SAAS4C,qBAAqBA,CAACK,IAAkB,EAAW;EAEjEA,IAAI,GAAG,IAAAC,wEAA+B,EAACD,IAAI,CAAC;EAC5C,QAAQA,IAAI,CAAC/C,IAAI;IACf,KAAK,kBAAkB;MAAE;QACvB,MAAMiD,IAAI,GAAGF,IAAI,CAACE,IAAI;QACtB,MAAMC,KAAK,GAAGH,IAAI,CAACG,KAAK;QACxB,OACEH,IAAI,CAACI,QAAQ,KAAK,GAAG,KACpBT,qBAAqB,CAACO,IAAoB,CAAC,IAC1CP,qBAAqB,CAACQ,KAAK,CAAC,CAAC;MAEnC;IACA,KAAK,iBAAiB;IACtB,KAAK,eAAe;MAClB,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd;AAmBA,SAASE,oBAAoBA,CAC3BL,IAA4B,EAC5BM,KAAoC,EACpC;EACA,MAAM;IAAEhD,IAAI;IAAEjB,IAAI;IAAEC;EAAE,CAAC,GAAGgE,KAAK;EAC/B,MAAM3D,IAAI,GAAGqD,IAAI,CAACzD,IAAI,CAACI,IAAI;EAC3B,IAAIW,IAAI,CAACiD,GAAG,CAAC5D,IAAI,CAAC,IAAI,CAACqD,IAAI,CAAC1B,KAAK,CAACkC,aAAa,CAAC7D,IAAI,CAAC,EAAE;IACrDqD,IAAI,CAAC7B,WAAW,CACd7B,CAAC,CAACmE,gBAAgB,CAACnE,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EAAEN,CAAC,CAACoB,SAAS,CAACsC,IAAI,CAACzD,IAAI,CAAC,CACtE,CAAC;IACDyD,IAAI,CAACU,IAAI,CAAC,CAAC;EACb;AACF;AAEA,MAAMC,wBAAwB,GAAG;EAC/BN;AACF,CAAC;AAEM,SAASf,mBAAmBA,CAACjD,IAAmC,EAAEC,CAAI,EAAE;EAAA,IAAAsE,UAAA;EAC7E,MAAMC,iBAAiB,GAAGxE,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAACtC,IAAI,CAACE,IAAI,CAACK,EAAE,CAACD,IAAI,CAAC;EAC5E,MAAMW,IAAyB,IAAAsD,UAAA,GAAG7E,KAAK,CAAC+E,GAAG,CAACD,iBAAiB,CAAC,YAAAD,UAAA,GAAI,IAAIG,GAAG,CAAC,CAAC;EAG3E,IAAIC,UAAuC,GAAG,CAAC,CAAC;EAChD,IAAIC,QAAgB;EACpB,IAAIlE,MAAM,GAAG,IAAI;EAEjB,MAAMqC,UAAsD,GAAG/C,IAAI,CAChEyE,GAAG,CAAC,SAAS,CAAC,CACdtB,GAAG,CAAC0B,UAAU,IAAI;IACjB,MAAMC,MAAM,GAAGD,UAAU,CAAC3E,IAAI;IAC9B,MAAMI,IAAI,GAAGL,CAAC,CAAC8E,YAAY,CAACD,MAAM,CAACvE,EAAE,CAAC,GAAGuE,MAAM,CAACvE,EAAE,CAACD,IAAI,GAAGwE,MAAM,CAACvE,EAAE,CAACyE,KAAK;IACzE,MAAMC,eAAe,GAAGJ,UAAU,CAACJ,GAAG,CAAC,aAAa,CAAC;IACrD,MAAMS,WAAW,GAAGJ,MAAM,CAACI,WAAW;IACtC,IAAIF,KAAmB;IACvB,IAAIE,WAAW,EAAE;MACfP,UAAU,GAAGQ,oBAAoB,CAACF,eAAe,EAAEhE,IAAI,CAAC;MACxD,IAAI0D,UAAU,KAAKS,SAAS,EAAE;QAC5BnE,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEqE,UAAU,CAAC;QAC1BU,OAAM,CACJ,OAAOV,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,QAC1D,CAAC;QAMD,IAAIA,UAAU,KAAKW,QAAQ,IAAIC,MAAM,CAACC,KAAK,CAACb,UAAU,CAAC,EAAE;UACvDK,KAAK,GAAG/E,CAAC,CAACwF,UAAU,CAACC,MAAM,CAACf,UAAU,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIA,UAAU,KAAK,CAACW,QAAQ,EAAE;UACnCN,KAAK,GAAG/E,CAAC,CAAC0F,eAAe,CAAC,GAAG,EAAE1F,CAAC,CAACwF,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,MAAM;UACLT,KAAK,GAAG/E,CAAC,CAAC2F,WAAW,CAACjB,UAAU,CAAC;QACnC;MACF,CAAC,MAAM;QACLjE,MAAM,KAANA,MAAM,GAAKuE,eAAe,CAACvE,MAAM,CAAC,CAAC;QAEnC,IAAIuE,eAAe,CAACY,sBAAsB,CAAC,CAAC,EAAE;UAC5C7B,oBAAoB,CAACiB,eAAe,EAAE;YACpChF,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLiF,eAAe,CAACa,QAAQ,CAACxB,wBAAwB,EAAE;YACjDrE,CAAC;YACDgB,IAAI;YACJjB;UACF,CAAC,CAAC;QACJ;QAEAgF,KAAK,GAAGC,eAAe,CAAC/E,IAAI;QAC5Be,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE8E,SAAS,CAAC;MAC3B;IACF,CAAC,MAAM,IAAI,OAAOT,UAAU,KAAK,QAAQ,EAAE;MACzCA,UAAU,IAAI,CAAC;MACfK,KAAK,GAAG/E,CAAC,CAAC8F,cAAc,CAACpB,UAAU,CAAC;MACpC1D,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAEqE,UAAU,CAAC;IAC5B,CAAC,MAAM,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACzC,MAAM3E,IAAI,CAACgG,mBAAmB,CAAC,oCAAoC,CAAC;IACtE,CAAC,MAAM;MAEL,MAAMC,OAAO,GAAGhG,CAAC,CAACmE,gBAAgB,CAChCnE,CAAC,CAACoB,SAAS,CAACrB,IAAI,CAACE,IAAI,CAACK,EAAE,CAAC,EACzBN,CAAC,CAACiG,aAAa,CAACtB,QAAQ,CAAC,EACzB,IACF,CAAC;MACDI,KAAK,GAAG/E,CAAC,CAACkG,gBAAgB,CAAC,GAAG,EAAElG,CAAC,CAAC8F,cAAc,CAAC,CAAC,CAAC,EAAEE,OAAO,CAAC;MAC7DhF,IAAI,CAACoB,GAAG,CAAC/B,IAAI,EAAE8E,SAAS,CAAC;IAC3B;IAEAR,QAAQ,GAAGtE,IAAI;IACf,OAAO,CAACA,IAAI,EAAE0E,KAAK,CAAC;EACtB,CAAC,CAAC;EAEJ,OAAO;IACLtE,MAAM;IACND,IAAI,EAAEQ,IAAI;IACV8B;EACF,CAAC;AACH;AAGA,SAASoC,oBAAoBA,CAC3BnF,IAAc,EACdoG,WAAiC,EACjCnF,IAAuB,GAAG,IAAIoF,GAAG,CAAC,CAAC,EACN;EAC7B,OAAOC,QAAQ,CAACtG,IAAI,CAAC;EAErB,SAASsG,QAAQA,CAACtG,IAAc,EAA+B;IAC7D,MAAM2D,IAAI,GAAG3D,IAAI,CAACE,IAAI;IACtB,QAAQyD,IAAI,CAAC/C,IAAI;MACf,KAAK,kBAAkB;QACrB,OAAO2F,WAAW,CAACvG,IAAI,EAAEoG,WAAW,EAAEnF,IAAI,CAAC;MAC7C,KAAK,eAAe;QAClB,OAAO0C,IAAI,CAACqB,KAAK;MACnB,KAAK,iBAAiB;QACpB,OAAOwB,mBAAmB,CAACxG,IAAmC,CAAC;MACjE,KAAK,kBAAkB;QACrB,OAAOyG,oBAAoB,CAACzG,IAAoC,CAAC;MACnE,KAAK,gBAAgB;QACnB,OAAO2D,IAAI,CAACqB,KAAK;MACnB,KAAK,yBAAyB;QAC5B,OAAOsB,QAAQ,CAACtG,IAAI,CAACyE,GAAG,CAAC,YAAY,CAAC,CAAC;MACzC,KAAK,YAAY;QACf,OAAO8B,WAAW,CAACvG,IAAI,EAAEoG,WAAW,EAAEnF,IAAI,CAAC;MAC7C,KAAK,iBAAiB;QAAE;UACtB,IAAI0C,IAAI,CAAC+C,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAOhD,IAAI,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAAC1B,KAAK,CAAC4B,MAAM;UACpC;UAEA,MAAMC,KAAK,GAAI7G,IAAI,CAAiCyE,GAAG,CAAC,aAAa,CAAC;UACtE,MAAMiC,MAAM,GAAG/C,IAAI,CAAC+C,MAAM;UAC1B,IAAII,GAAG,GAAG,EAAE;UAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACC,MAAM,EAAEI,CAAC,EAAE,EAAE;YACtCD,GAAG,IAAIJ,MAAM,CAACK,CAAC,CAAC,CAAC/B,KAAK,CAAC4B,MAAM;YAE7B,IAAIG,CAAC,GAAG,CAAC,GAAGL,MAAM,CAACC,MAAM,EAAE;cACzB,MAAM3B,KAAK,GAAGuB,WAAW,CAACM,KAAK,CAACE,CAAC,CAAC,EAAEX,WAAW,EAAEnF,IAAI,CAAC;cACtD,IAAI+D,KAAK,KAAKI,SAAS,EAAE,OAAOA,SAAS;cACzC0B,GAAG,IAAI9B,KAAK;YACd;UACF;UACA,OAAO8B,GAAG;QACZ;MACA;QACE,OAAO1B,SAAS;IACpB;EACF;EAEA,SAASmB,WAAWA,CAClBvG,IAAc,EACdoG,WAAgC,EAChCnF,IAAuB,EACM;IAC7B,IAAIjB,IAAI,CAACgH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,MAAMrD,IAAI,GAAG3D,IAAI,CAACE,IAAI;MAEtB,MAAM+G,GAAG,GAAGtD,IAAI,CAACuD,MAAM;MACvB,MAAMC,IAAI,GAAGxD,IAAI,CAACyD,QAAQ;MAC1B,IACE,CAACnH,WAAC,CAAC8E,YAAY,CAACkC,GAAG,CAAC,KACnBtD,IAAI,CAAC0D,QAAQ,GAAG,CAACpH,WAAC,CAACqH,eAAe,CAACH,IAAI,CAAC,GAAG,CAAClH,WAAC,CAAC8E,YAAY,CAACoC,IAAI,CAAC,CAAC,EAClE;QACA;MACF;MACA,MAAM3C,iBAAiB,GAAGxE,IAAI,CAACiC,KAAK,CAACK,oBAAoB,CAAC2E,GAAG,CAAC3G,IAAI,CAAC;MACnE,MAAMG,IAAI,GAAGf,KAAK,CAAC+E,GAAG,CAACD,iBAAiB,CAAC;MACzC,IAAI,CAAC/D,IAAI,EAAE;MAEX,OAAOA,IAAI,CAACgE,GAAG,CAAC0C,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACnC,KAAK,GAAGmC,IAAI,CAAC7G,IAAI,CAAC;IACzD,CAAC,MAAM,IAAIN,IAAI,CAAC+E,YAAY,CAAC,CAAC,EAAE;MAC9B,MAAMzE,IAAI,GAAGN,IAAI,CAACE,IAAI,CAACI,IAAI;MAE3B,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAACiH,QAAQ,CAACjH,IAAI,CAAC,EAAE;QACtC,OAAOiF,MAAM,CAACjF,IAAI,CAAC;MACrB;MAEA,IAAI0E,KAAK,GAAGoB,WAAW,oBAAXA,WAAW,CAAE3B,GAAG,CAACnE,IAAI,CAAC;MAClC,IAAI0E,KAAK,KAAKI,SAAS,EAAE;QACvB,OAAOJ,KAAK;MACd;MAEA,IAAI/D,IAAI,CAACiD,GAAG,CAAClE,IAAI,CAACE,IAAI,CAAC,EAAE;MACzBe,IAAI,CAACuG,GAAG,CAACxH,IAAI,CAACE,IAAI,CAAC;MAEnB8E,KAAK,GAAGG,oBAAoB,CAACnF,IAAI,CAACyH,OAAO,CAAC,CAAC,EAAErB,WAAW,EAAEnF,IAAI,CAAC;MAC/DmF,WAAW,YAAXA,WAAW,CAAE/D,GAAG,CAAC/B,IAAI,EAAE0E,KAAK,CAAC;MAC7B,OAAOA,KAAK;IACd;EACF;EAEA,SAASwB,mBAAmBA,CAC1BxG,IAAiC,EACJ;IAC7B,MAAMgF,KAAK,GAAGsB,QAAQ,CAACtG,IAAI,CAACyE,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5C,IAAIO,KAAK,KAAKI,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQpF,IAAI,CAACE,IAAI,CAAC6D,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOiB,KAAK;MACd,KAAK,GAAG;QAEN,OAAO,CAACA,KAAK;MACf,KAAK,GAAG;QACN,OAAO,CAACA,KAAK;MACf;QACE,OAAOI,SAAS;IACpB;EACF;EAEA,SAASqB,oBAAoBA,CAC3BzG,IAAkC,EACL;IAC7B,MAAM6D,IAAI,GAAGyC,QAAQ,CAACtG,IAAI,CAACyE,GAAG,CAAC,MAAM,CAAC,CAAQ;IAC9C,IAAIZ,IAAI,KAAKuB,SAAS,EAAE;MACtB,OAAOA,SAAS;IAClB;IACA,MAAMtB,KAAK,GAAGwC,QAAQ,CAACtG,IAAI,CAACyE,GAAG,CAAC,OAAO,CAAC,CAAQ;IAChD,IAAIX,KAAK,KAAKsB,SAAS,EAAE;MACvB,OAAOA,SAAS;IAClB;IAEA,QAAQpF,IAAI,CAACE,IAAI,CAAC6D,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,KAAK;QACR,OAAOD,IAAI,KAAKC,KAAK;MACvB,KAAK,IAAI;QACP,OAAOD,IAAI,IAAIC,KAAK;MACtB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,GAAG;QACN,OAAOD,IAAI,GAAGC,KAAK;MACrB,KAAK,IAAI;QACP,OAAA4D,IAAA,CAAAC,GAAA,CAAO9D,IAAI,EAAIC,KAAK;MACtB;QACE,OAAOsB,SAAS;IACpB;EACF;AACF", "ignoreList": []}