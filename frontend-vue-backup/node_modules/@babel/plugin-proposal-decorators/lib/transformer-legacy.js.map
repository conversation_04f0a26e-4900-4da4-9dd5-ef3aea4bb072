{"version": 3, "names": ["_core", "require", "buildClassDecorator", "template", "statement", "buildClassPrototype", "buildGetDescriptor", "buildGetObjectInitializer", "WARNING_CALLS", "WeakSet", "applyEnsureOrdering", "path", "decorators", "isClass", "get", "reduce", "acc", "prop", "concat", "node", "identDecorators", "filter", "decorator", "t", "isIdentifier", "expression", "length", "sequenceExpression", "map", "id", "scope", "generateDeclaredUidIdentifier", "assignmentExpression", "applyClassDecorators", "classPath", "hasClassDecorators", "name", "dec", "reverse", "CLASS_REF", "cloneNode", "DECORATOR", "INNER", "classNode", "_classNode$decorators", "applyMethodDecorators", "state", "hasMethodDecorators", "body", "applyTargetDecorators", "some", "_node$decorators", "applyObjectDecorators", "properties", "type", "decoratedProps", "exprs", "computed", "buildCodeFrameError", "property", "isLiteral", "key", "stringLiteral", "target", "static", "isClassProperty", "descriptor", "initializer", "value", "functionExpression", "blockStatement", "returnStatement", "nullLiteral", "callExpression", "addHelper", "thisExpression", "add", "push", "arrayExpression", "objectExpression", "objectProperty", "identifier", "booleanLiteral", "isObjectProperty", "TEMP", "TARGET", "PROPERTY", "decoratedClassToExpression", "ref", "generateUidIdentifier", "variableDeclaration", "variableDeclarator", "toExpression", "visitor", "ExportDefaultDeclaration", "decl", "isClassDeclaration", "replacement", "varDeclPath", "replaceWithMultiple", "exportNamedDeclaration", "exportSpecifier", "declarations", "registerDeclaration", "ClassDeclaration", "newPath", "replaceWith", "binding", "getOwnBinding", "ClassExpression", "decoratedClass", "ObjectExpression", "decoratedObject", "AssignmentExpression", "has", "right", "CallExpression", "arguments", "callee", "_default", "exports", "default"], "sources": ["../src/transformer-legacy.ts"], "sourcesContent": ["// Fork of https://github.com/loganfsmyth/babel-plugin-proposal-decorators-legacy\n\nimport { template, types as t } from \"@babel/core\";\nimport type { NodePath, Visitor, PluginPass } from \"@babel/core\";\n\nconst buildClassDecorator = template.statement(`\n  DECORATOR(CLASS_REF = INNER) || CLASS_REF;\n`) as (replacements: {\n  DECORATOR: t.Expression;\n  CLASS_REF: t.Identifier;\n  INNER: t.Expression;\n}) => t.ExpressionStatement;\n\nconst buildClassPrototype = template(`\n  CLASS_REF.prototype;\n`) as (replacements: { CLASS_REF: t.Identifier }) => t.ExpressionStatement;\n\nconst buildGetDescriptor = template(`\n    Object.getOwnPropertyDescriptor(TARGET, PROPERTY);\n`) as (replacements: {\n  TARGET: t.Expression;\n  PROPERTY: t.Literal;\n}) => t.ExpressionStatement;\n\nconst buildGetObjectInitializer = template(`\n    (TEMP = Object.getOwnPropertyDescriptor(TARGET, PROPERTY), (TEMP = TEMP ? TEMP.value : undefined), {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        initializer: function(){\n            return TEMP;\n        }\n    })\n`) as (replacements: {\n  TEMP: t.Identifier;\n  TARGET: t.Expression;\n  PROPERTY: t.Literal;\n}) => t.ExpressionStatement;\n\nconst WARNING_CALLS = new WeakSet();\n\n// legacy decorator does not support ClassAccessorProperty\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty;\n\n/**\n * If the decorator expressions are non-identifiers, hoist them to before the class so we can be sure\n * that they are evaluated in order.\n */\nfunction applyEnsureOrdering(\n  path: NodePath<t.ClassExpression | t.ObjectExpression>,\n) {\n  // TODO: This should probably also hoist computed properties.\n  const decorators: t.Decorator[] = (\n    path.isClass()\n      ? [\n          path,\n          ...(path.get(\"body.body\") as NodePath<ClassDecoratableElement>[]),\n        ]\n      : path.get(\"properties\")\n  ).reduce(\n    (\n      acc: t.Decorator[],\n      prop: NodePath<\n        t.ObjectMember | t.ClassExpression | ClassDecoratableElement\n      >,\n    ) => acc.concat(prop.node.decorators || []),\n    [],\n  );\n\n  const identDecorators = decorators.filter(\n    decorator => !t.isIdentifier(decorator.expression),\n  );\n  if (identDecorators.length === 0) return;\n\n  return t.sequenceExpression(\n    identDecorators\n      .map((decorator): t.Expression => {\n        const expression = decorator.expression;\n        const id = (decorator.expression =\n          path.scope.generateDeclaredUidIdentifier(\"dec\"));\n        return t.assignmentExpression(\"=\", id, expression);\n      })\n      .concat([path.node]),\n  );\n}\n\n/**\n * Given a class expression with class-level decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyClassDecorators(classPath: NodePath<t.ClassExpression>) {\n  if (!hasClassDecorators(classPath.node)) return;\n\n  const decorators = classPath.node.decorators || [];\n  classPath.node.decorators = null;\n\n  const name = classPath.scope.generateDeclaredUidIdentifier(\"class\");\n\n  return decorators\n    .map(dec => dec.expression)\n    .reverse()\n    .reduce(function (acc, decorator) {\n      return buildClassDecorator({\n        CLASS_REF: t.cloneNode(name),\n        DECORATOR: t.cloneNode(decorator),\n        INNER: acc,\n      }).expression;\n    }, classPath.node);\n}\n\nfunction hasClassDecorators(classNode: t.Class) {\n  return !!classNode.decorators?.length;\n}\n\n/**\n * Given a class expression with method-level decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyMethodDecorators(\n  path: NodePath<t.ClassExpression>,\n  state: PluginPass,\n) {\n  if (!hasMethodDecorators(path.node.body.body)) return;\n\n  return applyTargetDecorators(\n    path,\n    state,\n    // @ts-expect-error ClassAccessorProperty is not supported in legacy decorator\n    path.node.body.body,\n  );\n}\n\nfunction hasMethodDecorators(\n  body: t.ClassBody[\"body\"] | t.ObjectExpression[\"properties\"],\n) {\n  return body.some(\n    node =>\n      // @ts-expect-error decorators not in SpreadElement/StaticBlock\n      node.decorators?.length,\n  );\n}\n\n/**\n * Given an object expression with property decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyObjectDecorators(\n  path: NodePath<t.ObjectExpression>,\n  state: PluginPass,\n) {\n  if (!hasMethodDecorators(path.node.properties)) return;\n\n  return applyTargetDecorators(\n    path,\n    state,\n    path.node.properties.filter(\n      (prop): prop is t.ObjectMember => prop.type !== \"SpreadElement\",\n    ),\n  );\n}\n\n/**\n * A helper to pull out property decorators into a sequence expression.\n */\nfunction applyTargetDecorators(\n  path: NodePath<t.ClassExpression | t.ObjectExpression>,\n  state: PluginPass,\n  decoratedProps: (t.ObjectMember | ClassDecoratableElement)[],\n) {\n  const name = path.scope.generateDeclaredUidIdentifier(\n    path.isClass() ? \"class\" : \"obj\",\n  );\n\n  const exprs = decoratedProps.reduce(function (acc, node) {\n    let decorators: t.Decorator[] = [];\n    if (node.decorators != null) {\n      decorators = node.decorators;\n      node.decorators = null;\n    }\n\n    if (decorators.length === 0) return acc;\n\n    if (\n      // @ts-expect-error computed is not in ClassPrivateProperty\n      node.computed\n    ) {\n      throw path.buildCodeFrameError(\n        \"Computed method/property decorators are not yet supported.\",\n      );\n    }\n\n    const property: t.Literal = t.isLiteral(node.key)\n      ? node.key\n      : t.stringLiteral(\n          // @ts-expect-error: should we handle ClassPrivateProperty?\n          node.key.name,\n        );\n\n    const target =\n      path.isClass() && !(node as ClassDecoratableElement).static\n        ? buildClassPrototype({\n            CLASS_REF: name,\n          }).expression\n        : name;\n\n    if (t.isClassProperty(node, { static: false })) {\n      const descriptor = path.scope.generateDeclaredUidIdentifier(\"descriptor\");\n\n      const initializer = node.value\n        ? t.functionExpression(\n            null,\n            [],\n            t.blockStatement([t.returnStatement(node.value)]),\n          )\n        : t.nullLiteral();\n\n      node.value = t.callExpression(\n        state.addHelper(\"initializerWarningHelper\"),\n        [descriptor, t.thisExpression()],\n      );\n\n      WARNING_CALLS.add(node.value);\n\n      acc.push(\n        t.assignmentExpression(\n          \"=\",\n          t.cloneNode(descriptor),\n          t.callExpression(state.addHelper(\"applyDecoratedDescriptor\"), [\n            t.cloneNode(target),\n            t.cloneNode(property),\n            t.arrayExpression(\n              decorators.map(dec => t.cloneNode(dec.expression)),\n            ),\n            t.objectExpression([\n              t.objectProperty(\n                t.identifier(\"configurable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(\n                t.identifier(\"enumerable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(\n                t.identifier(\"writable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(t.identifier(\"initializer\"), initializer),\n            ]),\n          ]),\n        ),\n      );\n    } else {\n      acc.push(\n        t.callExpression(state.addHelper(\"applyDecoratedDescriptor\"), [\n          t.cloneNode(target),\n          t.cloneNode(property),\n          t.arrayExpression(decorators.map(dec => t.cloneNode(dec.expression))),\n          t.isObjectProperty(node) || t.isClassProperty(node, { static: true })\n            ? buildGetObjectInitializer({\n                TEMP: path.scope.generateDeclaredUidIdentifier(\"init\"),\n                TARGET: t.cloneNode(target),\n                PROPERTY: t.cloneNode(property),\n              }).expression\n            : buildGetDescriptor({\n                TARGET: t.cloneNode(target),\n                PROPERTY: t.cloneNode(property),\n              }).expression,\n          t.cloneNode(target),\n        ]),\n      );\n    }\n\n    return acc;\n  }, []);\n\n  return t.sequenceExpression([\n    t.assignmentExpression(\"=\", t.cloneNode(name), path.node),\n    t.sequenceExpression(exprs),\n    t.cloneNode(name),\n  ]);\n}\n\nfunction decoratedClassToExpression({ node, scope }: NodePath<t.Class>) {\n  if (!hasClassDecorators(node) && !hasMethodDecorators(node.body.body)) {\n    return;\n  }\n\n  const ref = node.id\n    ? t.cloneNode(node.id)\n    : scope.generateUidIdentifier(\"class\");\n\n  return t.variableDeclaration(\"let\", [\n    t.variableDeclarator(ref, t.toExpression(node)),\n  ]);\n}\n\nconst visitor: Visitor<PluginPass> = {\n  ExportDefaultDeclaration(path) {\n    const decl = path.get(\"declaration\");\n    if (!decl.isClassDeclaration()) return;\n\n    const replacement = decoratedClassToExpression(decl);\n    if (replacement) {\n      const [varDeclPath] = path.replaceWithMultiple([\n        replacement,\n        t.exportNamedDeclaration(null, [\n          t.exportSpecifier(\n            // @ts-expect-error todo(flow->ts) might be add more specific return type for decoratedClassToExpression\n            t.cloneNode(replacement.declarations[0].id),\n            t.identifier(\"default\"),\n          ),\n        ]),\n      ]);\n\n      if (!decl.node.id) {\n        path.scope.registerDeclaration(varDeclPath);\n      }\n    }\n  },\n  ClassDeclaration(path) {\n    const replacement = decoratedClassToExpression(path);\n    if (replacement) {\n      const [newPath] = path.replaceWith(replacement);\n\n      const decl = newPath.get(\"declarations.0\");\n      const id = decl.node.id as t.Identifier;\n\n      // TODO: Maybe add this logic to @babel/traverse\n      const binding = path.scope.getOwnBinding(id.name);\n      binding.identifier = id;\n      binding.path = decl;\n    }\n  },\n  ClassExpression(path, state) {\n    // Create a replacement for the class node if there is one. We do one pass to replace classes with\n    // class decorators, and a second pass to process method decorators.\n    const decoratedClass =\n      applyEnsureOrdering(path) ||\n      applyClassDecorators(path) ||\n      applyMethodDecorators(path, state);\n\n    if (decoratedClass) path.replaceWith(decoratedClass);\n  },\n  ObjectExpression(path, state) {\n    const decoratedObject =\n      applyEnsureOrdering(path) || applyObjectDecorators(path, state);\n\n    if (decoratedObject) path.replaceWith(decoratedObject);\n  },\n\n  AssignmentExpression(path, state) {\n    if (!WARNING_CALLS.has(path.node.right)) return;\n\n    path.replaceWith(\n      t.callExpression(state.addHelper(\"initializerDefineProperty\"), [\n        // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n        t.cloneNode(path.get(\"left.object\").node),\n        t.stringLiteral(\n          // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n          path.get(\"left.property\").node.name ||\n            // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n            path.get(\"left.property\").node.value,\n        ),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"right.arguments\")[0].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"right.arguments\")[1].node),\n      ]),\n    );\n  },\n\n  CallExpression(path, state) {\n    if (path.node.arguments.length !== 3) return;\n    if (!WARNING_CALLS.has(path.node.arguments[2])) return;\n\n    // If the class properties plugin isn't enabled, this line will add an unused helper\n    // to the code. It's not ideal, but it's ok since the configuration is not valid anyway.\n    // @ts-expect-error todo(flow->ts) check that `callee` is Identifier\n    if (path.node.callee.name !== state.addHelper(\"defineProperty\").name) {\n      return;\n    }\n\n    path.replaceWith(\n      t.callExpression(state.addHelper(\"initializerDefineProperty\"), [\n        t.cloneNode(path.get(\"arguments\")[0].node),\n        t.cloneNode(path.get(\"arguments\")[1].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"arguments.2.arguments\")[0].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"arguments.2.arguments\")[1].node),\n      ]),\n    );\n  },\n};\n\nexport default visitor;\n"], "mappings": ";;;;;;AAEA,IAAAA,KAAA,GAAAC,OAAA;AAGA,MAAMC,mBAAmB,GAAGC,cAAQ,CAACC,SAAS,CAAC;AAC/C;AACA,CAAC,CAI0B;AAE3B,MAAMC,mBAAmB,GAAG,IAAAF,cAAQ,EAAC;AACrC;AACA,CAAC,CAAyE;AAE1E,MAAMG,kBAAkB,GAAG,IAAAH,cAAQ,EAAC;AACpC;AACA,CAAC,CAG0B;AAE3B,MAAMI,yBAAyB,GAAG,IAAAJ,cAAQ,EAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAI0B;AAE3B,MAAMK,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AAanC,SAASC,mBAAmBA,CAC1BC,IAAsD,EACtD;EAEA,MAAMC,UAAyB,GAAG,CAChCD,IAAI,CAACE,OAAO,CAAC,CAAC,GACV,CACEF,IAAI,EACJ,GAAIA,IAAI,CAACG,GAAG,CAAC,WAAW,CAAyC,CAClE,GACDH,IAAI,CAACG,GAAG,CAAC,YAAY,CAAC,EAC1BC,MAAM,CACN,CACEC,GAAkB,EAClBC,IAEC,KACED,GAAG,CAACE,MAAM,CAACD,IAAI,CAACE,IAAI,CAACP,UAAU,IAAI,EAAE,CAAC,EAC3C,EACF,CAAC;EAED,MAAMQ,eAAe,GAAGR,UAAU,CAACS,MAAM,CACvCC,SAAS,IAAI,CAACC,WAAC,CAACC,YAAY,CAACF,SAAS,CAACG,UAAU,CACnD,CAAC;EACD,IAAIL,eAAe,CAACM,MAAM,KAAK,CAAC,EAAE;EAElC,OAAOH,WAAC,CAACI,kBAAkB,CACzBP,eAAe,CACZQ,GAAG,CAAEN,SAAS,IAAmB;IAChC,MAAMG,UAAU,GAAGH,SAAS,CAACG,UAAU;IACvC,MAAMI,EAAE,GAAIP,SAAS,CAACG,UAAU,GAC9Bd,IAAI,CAACmB,KAAK,CAACC,6BAA6B,CAAC,KAAK,CAAE;IAClD,OAAOR,WAAC,CAACS,oBAAoB,CAAC,GAAG,EAAEH,EAAE,EAAEJ,UAAU,CAAC;EACpD,CAAC,CAAC,CACDP,MAAM,CAAC,CAACP,IAAI,CAACQ,IAAI,CAAC,CACvB,CAAC;AACH;AAMA,SAASc,oBAAoBA,CAACC,SAAsC,EAAE;EACpE,IAAI,CAACC,kBAAkB,CAACD,SAAS,CAACf,IAAI,CAAC,EAAE;EAEzC,MAAMP,UAAU,GAAGsB,SAAS,CAACf,IAAI,CAACP,UAAU,IAAI,EAAE;EAClDsB,SAAS,CAACf,IAAI,CAACP,UAAU,GAAG,IAAI;EAEhC,MAAMwB,IAAI,GAAGF,SAAS,CAACJ,KAAK,CAACC,6BAA6B,CAAC,OAAO,CAAC;EAEnE,OAAOnB,UAAU,CACdgB,GAAG,CAACS,GAAG,IAAIA,GAAG,CAACZ,UAAU,CAAC,CAC1Ba,OAAO,CAAC,CAAC,CACTvB,MAAM,CAAC,UAAUC,GAAG,EAAEM,SAAS,EAAE;IAChC,OAAOpB,mBAAmB,CAAC;MACzBqC,SAAS,EAAEhB,WAAC,CAACiB,SAAS,CAACJ,IAAI,CAAC;MAC5BK,SAAS,EAAElB,WAAC,CAACiB,SAAS,CAAClB,SAAS,CAAC;MACjCoB,KAAK,EAAE1B;IACT,CAAC,CAAC,CAACS,UAAU;EACf,CAAC,EAAES,SAAS,CAACf,IAAI,CAAC;AACtB;AAEA,SAASgB,kBAAkBA,CAACQ,SAAkB,EAAE;EAAA,IAAAC,qBAAA;EAC9C,OAAO,CAAC,GAAAA,qBAAA,GAACD,SAAS,CAAC/B,UAAU,aAApBgC,qBAAA,CAAsBlB,MAAM;AACvC;AAMA,SAASmB,qBAAqBA,CAC5BlC,IAAiC,EACjCmC,KAAiB,EACjB;EACA,IAAI,CAACC,mBAAmB,CAACpC,IAAI,CAACQ,IAAI,CAAC6B,IAAI,CAACA,IAAI,CAAC,EAAE;EAE/C,OAAOC,qBAAqB,CAC1BtC,IAAI,EACJmC,KAAK,EAELnC,IAAI,CAACQ,IAAI,CAAC6B,IAAI,CAACA,IACjB,CAAC;AACH;AAEA,SAASD,mBAAmBA,CAC1BC,IAA4D,EAC5D;EACA,OAAOA,IAAI,CAACE,IAAI,CACd/B,IAAI;IAAA,IAAAgC,gBAAA;IAAA,QAAAA,gBAAA,GAEFhC,IAAI,CAACP,UAAU,qBAAfuC,gBAAA,CAAiBzB,MAAM;EAAA,CAC3B,CAAC;AACH;AAMA,SAAS0B,qBAAqBA,CAC5BzC,IAAkC,EAClCmC,KAAiB,EACjB;EACA,IAAI,CAACC,mBAAmB,CAACpC,IAAI,CAACQ,IAAI,CAACkC,UAAU,CAAC,EAAE;EAEhD,OAAOJ,qBAAqB,CAC1BtC,IAAI,EACJmC,KAAK,EACLnC,IAAI,CAACQ,IAAI,CAACkC,UAAU,CAAChC,MAAM,CACxBJ,IAAI,IAA6BA,IAAI,CAACqC,IAAI,KAAK,eAClD,CACF,CAAC;AACH;AAKA,SAASL,qBAAqBA,CAC5BtC,IAAsD,EACtDmC,KAAiB,EACjBS,cAA4D,EAC5D;EACA,MAAMnB,IAAI,GAAGzB,IAAI,CAACmB,KAAK,CAACC,6BAA6B,CACnDpB,IAAI,CAACE,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG,KAC7B,CAAC;EAED,MAAM2C,KAAK,GAAGD,cAAc,CAACxC,MAAM,CAAC,UAAUC,GAAG,EAAEG,IAAI,EAAE;IACvD,IAAIP,UAAyB,GAAG,EAAE;IAClC,IAAIO,IAAI,CAACP,UAAU,IAAI,IAAI,EAAE;MAC3BA,UAAU,GAAGO,IAAI,CAACP,UAAU;MAC5BO,IAAI,CAACP,UAAU,GAAG,IAAI;IACxB;IAEA,IAAIA,UAAU,CAACc,MAAM,KAAK,CAAC,EAAE,OAAOV,GAAG;IAEvC,IAEEG,IAAI,CAACsC,QAAQ,EACb;MACA,MAAM9C,IAAI,CAAC+C,mBAAmB,CAC5B,4DACF,CAAC;IACH;IAEA,MAAMC,QAAmB,GAAGpC,WAAC,CAACqC,SAAS,CAACzC,IAAI,CAAC0C,GAAG,CAAC,GAC7C1C,IAAI,CAAC0C,GAAG,GACRtC,WAAC,CAACuC,aAAa,CAEb3C,IAAI,CAAC0C,GAAG,CAACzB,IACX,CAAC;IAEL,MAAM2B,MAAM,GACVpD,IAAI,CAACE,OAAO,CAAC,CAAC,IAAI,CAAEM,IAAI,CAA6B6C,MAAM,GACvD3D,mBAAmB,CAAC;MAClBkC,SAAS,EAAEH;IACb,CAAC,CAAC,CAACX,UAAU,GACbW,IAAI;IAEV,IAAIb,WAAC,CAAC0C,eAAe,CAAC9C,IAAI,EAAE;MAAE6C,MAAM,EAAE;IAAM,CAAC,CAAC,EAAE;MAC9C,MAAME,UAAU,GAAGvD,IAAI,CAACmB,KAAK,CAACC,6BAA6B,CAAC,YAAY,CAAC;MAEzE,MAAMoC,WAAW,GAAGhD,IAAI,CAACiD,KAAK,GAC1B7C,WAAC,CAAC8C,kBAAkB,CAClB,IAAI,EACJ,EAAE,EACF9C,WAAC,CAAC+C,cAAc,CAAC,CAAC/C,WAAC,CAACgD,eAAe,CAACpD,IAAI,CAACiD,KAAK,CAAC,CAAC,CAClD,CAAC,GACD7C,WAAC,CAACiD,WAAW,CAAC,CAAC;MAEnBrD,IAAI,CAACiD,KAAK,GAAG7C,WAAC,CAACkD,cAAc,CAC3B3B,KAAK,CAAC4B,SAAS,CAAC,0BAA0B,CAAC,EAC3C,CAACR,UAAU,EAAE3C,WAAC,CAACoD,cAAc,CAAC,CAAC,CACjC,CAAC;MAEDnE,aAAa,CAACoE,GAAG,CAACzD,IAAI,CAACiD,KAAK,CAAC;MAE7BpD,GAAG,CAAC6D,IAAI,CACNtD,WAAC,CAACS,oBAAoB,CACpB,GAAG,EACHT,WAAC,CAACiB,SAAS,CAAC0B,UAAU,CAAC,EACvB3C,WAAC,CAACkD,cAAc,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC5DnD,WAAC,CAACiB,SAAS,CAACuB,MAAM,CAAC,EACnBxC,WAAC,CAACiB,SAAS,CAACmB,QAAQ,CAAC,EACrBpC,WAAC,CAACuD,eAAe,CACflE,UAAU,CAACgB,GAAG,CAACS,GAAG,IAAId,WAAC,CAACiB,SAAS,CAACH,GAAG,CAACZ,UAAU,CAAC,CACnD,CAAC,EACDF,WAAC,CAACwD,gBAAgB,CAAC,CACjBxD,WAAC,CAACyD,cAAc,CACdzD,WAAC,CAAC0D,UAAU,CAAC,cAAc,CAAC,EAC5B1D,WAAC,CAAC2D,cAAc,CAAC,IAAI,CACvB,CAAC,EACD3D,WAAC,CAACyD,cAAc,CACdzD,WAAC,CAAC0D,UAAU,CAAC,YAAY,CAAC,EAC1B1D,WAAC,CAAC2D,cAAc,CAAC,IAAI,CACvB,CAAC,EACD3D,WAAC,CAACyD,cAAc,CACdzD,WAAC,CAAC0D,UAAU,CAAC,UAAU,CAAC,EACxB1D,WAAC,CAAC2D,cAAc,CAAC,IAAI,CACvB,CAAC,EACD3D,WAAC,CAACyD,cAAc,CAACzD,WAAC,CAAC0D,UAAU,CAAC,aAAa,CAAC,EAAEd,WAAW,CAAC,CAC3D,CAAC,CACH,CACH,CACF,CAAC;IACH,CAAC,MAAM;MACLnD,GAAG,CAAC6D,IAAI,CACNtD,WAAC,CAACkD,cAAc,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,0BAA0B,CAAC,EAAE,CAC5DnD,WAAC,CAACiB,SAAS,CAACuB,MAAM,CAAC,EACnBxC,WAAC,CAACiB,SAAS,CAACmB,QAAQ,CAAC,EACrBpC,WAAC,CAACuD,eAAe,CAAClE,UAAU,CAACgB,GAAG,CAACS,GAAG,IAAId,WAAC,CAACiB,SAAS,CAACH,GAAG,CAACZ,UAAU,CAAC,CAAC,CAAC,EACrEF,WAAC,CAAC4D,gBAAgB,CAAChE,IAAI,CAAC,IAAII,WAAC,CAAC0C,eAAe,CAAC9C,IAAI,EAAE;QAAE6C,MAAM,EAAE;MAAK,CAAC,CAAC,GACjEzD,yBAAyB,CAAC;QACxB6E,IAAI,EAAEzE,IAAI,CAACmB,KAAK,CAACC,6BAA6B,CAAC,MAAM,CAAC;QACtDsD,MAAM,EAAE9D,WAAC,CAACiB,SAAS,CAACuB,MAAM,CAAC;QAC3BuB,QAAQ,EAAE/D,WAAC,CAACiB,SAAS,CAACmB,QAAQ;MAChC,CAAC,CAAC,CAAClC,UAAU,GACbnB,kBAAkB,CAAC;QACjB+E,MAAM,EAAE9D,WAAC,CAACiB,SAAS,CAACuB,MAAM,CAAC;QAC3BuB,QAAQ,EAAE/D,WAAC,CAACiB,SAAS,CAACmB,QAAQ;MAChC,CAAC,CAAC,CAAClC,UAAU,EACjBF,WAAC,CAACiB,SAAS,CAACuB,MAAM,CAAC,CACpB,CACH,CAAC;IACH;IAEA,OAAO/C,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOO,WAAC,CAACI,kBAAkB,CAAC,CAC1BJ,WAAC,CAACS,oBAAoB,CAAC,GAAG,EAAET,WAAC,CAACiB,SAAS,CAACJ,IAAI,CAAC,EAAEzB,IAAI,CAACQ,IAAI,CAAC,EACzDI,WAAC,CAACI,kBAAkB,CAAC6B,KAAK,CAAC,EAC3BjC,WAAC,CAACiB,SAAS,CAACJ,IAAI,CAAC,CAClB,CAAC;AACJ;AAEA,SAASmD,0BAA0BA,CAAC;EAAEpE,IAAI;EAAEW;AAAyB,CAAC,EAAE;EACtE,IAAI,CAACK,kBAAkB,CAAChB,IAAI,CAAC,IAAI,CAAC4B,mBAAmB,CAAC5B,IAAI,CAAC6B,IAAI,CAACA,IAAI,CAAC,EAAE;IACrE;EACF;EAEA,MAAMwC,GAAG,GAAGrE,IAAI,CAACU,EAAE,GACfN,WAAC,CAACiB,SAAS,CAACrB,IAAI,CAACU,EAAE,CAAC,GACpBC,KAAK,CAAC2D,qBAAqB,CAAC,OAAO,CAAC;EAExC,OAAOlE,WAAC,CAACmE,mBAAmB,CAAC,KAAK,EAAE,CAClCnE,WAAC,CAACoE,kBAAkB,CAACH,GAAG,EAAEjE,WAAC,CAACqE,YAAY,CAACzE,IAAI,CAAC,CAAC,CAChD,CAAC;AACJ;AAEA,MAAM0E,OAA4B,GAAG;EACnCC,wBAAwBA,CAACnF,IAAI,EAAE;IAC7B,MAAMoF,IAAI,GAAGpF,IAAI,CAACG,GAAG,CAAC,aAAa,CAAC;IACpC,IAAI,CAACiF,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE;IAEhC,MAAMC,WAAW,GAAGV,0BAA0B,CAACQ,IAAI,CAAC;IACpD,IAAIE,WAAW,EAAE;MACf,MAAM,CAACC,WAAW,CAAC,GAAGvF,IAAI,CAACwF,mBAAmB,CAAC,CAC7CF,WAAW,EACX1E,WAAC,CAAC6E,sBAAsB,CAAC,IAAI,EAAE,CAC7B7E,WAAC,CAAC8E,eAAe,CAEf9E,WAAC,CAACiB,SAAS,CAACyD,WAAW,CAACK,YAAY,CAAC,CAAC,CAAC,CAACzE,EAAE,CAAC,EAC3CN,WAAC,CAAC0D,UAAU,CAAC,SAAS,CACxB,CAAC,CACF,CAAC,CACH,CAAC;MAEF,IAAI,CAACc,IAAI,CAAC5E,IAAI,CAACU,EAAE,EAAE;QACjBlB,IAAI,CAACmB,KAAK,CAACyE,mBAAmB,CAACL,WAAW,CAAC;MAC7C;IACF;EACF,CAAC;EACDM,gBAAgBA,CAAC7F,IAAI,EAAE;IACrB,MAAMsF,WAAW,GAAGV,0BAA0B,CAAC5E,IAAI,CAAC;IACpD,IAAIsF,WAAW,EAAE;MACf,MAAM,CAACQ,OAAO,CAAC,GAAG9F,IAAI,CAAC+F,WAAW,CAACT,WAAW,CAAC;MAE/C,MAAMF,IAAI,GAAGU,OAAO,CAAC3F,GAAG,CAAC,gBAAgB,CAAC;MAC1C,MAAMe,EAAE,GAAGkE,IAAI,CAAC5E,IAAI,CAACU,EAAkB;MAGvC,MAAM8E,OAAO,GAAGhG,IAAI,CAACmB,KAAK,CAAC8E,aAAa,CAAC/E,EAAE,CAACO,IAAI,CAAC;MACjDuE,OAAO,CAAC1B,UAAU,GAAGpD,EAAE;MACvB8E,OAAO,CAAChG,IAAI,GAAGoF,IAAI;IACrB;EACF,CAAC;EACDc,eAAeA,CAAClG,IAAI,EAAEmC,KAAK,EAAE;IAG3B,MAAMgE,cAAc,GAClBpG,mBAAmB,CAACC,IAAI,CAAC,IACzBsB,oBAAoB,CAACtB,IAAI,CAAC,IAC1BkC,qBAAqB,CAAClC,IAAI,EAAEmC,KAAK,CAAC;IAEpC,IAAIgE,cAAc,EAAEnG,IAAI,CAAC+F,WAAW,CAACI,cAAc,CAAC;EACtD,CAAC;EACDC,gBAAgBA,CAACpG,IAAI,EAAEmC,KAAK,EAAE;IAC5B,MAAMkE,eAAe,GACnBtG,mBAAmB,CAACC,IAAI,CAAC,IAAIyC,qBAAqB,CAACzC,IAAI,EAAEmC,KAAK,CAAC;IAEjE,IAAIkE,eAAe,EAAErG,IAAI,CAAC+F,WAAW,CAACM,eAAe,CAAC;EACxD,CAAC;EAEDC,oBAAoBA,CAACtG,IAAI,EAAEmC,KAAK,EAAE;IAChC,IAAI,CAACtC,aAAa,CAAC0G,GAAG,CAACvG,IAAI,CAACQ,IAAI,CAACgG,KAAK,CAAC,EAAE;IAEzCxG,IAAI,CAAC+F,WAAW,CACdnF,WAAC,CAACkD,cAAc,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,2BAA2B,CAAC,EAAE,CAE7DnD,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,aAAa,CAAC,CAACK,IAAI,CAAC,EACzCI,WAAC,CAACuC,aAAa,CAEbnD,IAAI,CAACG,GAAG,CAAC,eAAe,CAAC,CAACK,IAAI,CAACiB,IAAI,IAEjCzB,IAAI,CAACG,GAAG,CAAC,eAAe,CAAC,CAACK,IAAI,CAACiD,KACnC,CAAC,EAED7C,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAEhDI,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CACjD,CACH,CAAC;EACH,CAAC;EAEDiG,cAAcA,CAACzG,IAAI,EAAEmC,KAAK,EAAE;IAC1B,IAAInC,IAAI,CAACQ,IAAI,CAACkG,SAAS,CAAC3F,MAAM,KAAK,CAAC,EAAE;IACtC,IAAI,CAAClB,aAAa,CAAC0G,GAAG,CAACvG,IAAI,CAACQ,IAAI,CAACkG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAKhD,IAAI1G,IAAI,CAACQ,IAAI,CAACmG,MAAM,CAAClF,IAAI,KAAKU,KAAK,CAAC4B,SAAS,CAAC,gBAAgB,CAAC,CAACtC,IAAI,EAAE;MACpE;IACF;IAEAzB,IAAI,CAAC+F,WAAW,CACdnF,WAAC,CAACkD,cAAc,CAAC3B,KAAK,CAAC4B,SAAS,CAAC,2BAA2B,CAAC,EAAE,CAC7DnD,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAC1CI,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAE1CI,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAEtDI,WAAC,CAACiB,SAAS,CAAC7B,IAAI,CAACG,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,CACvD,CACH,CAAC;EACH;AACF,CAAC;AAAC,IAAAoG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa5B,OAAO", "ignoreList": []}