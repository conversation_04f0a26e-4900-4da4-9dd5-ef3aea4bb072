{"version": 3, "sources": ["../../src/services/avatars.ts"], "names": [], "mappings": "AAAA,SAAS,yBAA+D;AAMjE,MAAM,QAAQ;AAAA,EAGjB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,WAAW,MAAe,OAAgB,QAAiB,SAAwC;AACrG,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,2BAA2B,QAAQ,UAAU,IAAI;AACjE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,cAAc,MAAkB,OAAgB,QAAiB,SAAwC;AAC3G,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,+BAA+B,QAAQ,UAAU,IAAI;AACrE,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,WAAW,KAAmC;AAChD,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,QAAQ,MAAY,OAAgB,QAAiB,SAAwC;AAC/F,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,wBAAwB,QAAQ,UAAU,IAAI;AAC9D,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,SAAS,KAAa,OAAgB,QAAuC;AAC/E,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,YAAY,MAAe,OAAgB,QAAiB,YAA2C;AACzG,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,eAAe,aAAa;AACnC,cAAQ,YAAY,IAAI;AAAA,IAC5B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,MAAM,MAAc,MAAe,QAAiB,UAA0C;AAChG,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { <PERSON>rowser } from '../enums/browser';\nimport { CreditCard } from '../enums/credit-card';\nimport { Flag } from '../enums/flag';\n\nexport class Avatars {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get browser icon\n     *\n     * You can use this endpoint to show different browser icons to your users. The code argument receives the browser code as it appears in your user [GET /account/sessions](https://appwrite.io/docs/references/cloud/client-web/account#getSessions) endpoint. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n     *\n     * @param {Browser} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getBrowser(code: Browser, width?: number, height?: number, quality?: number): Promise<ArrayBuffer> {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/browsers/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get credit card icon\n     *\n     * The credit card endpoint will return you the icon of the credit card provider you need. Use width, height and quality arguments to change the output settings.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {CreditCard} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getCreditCard(code: CreditCard, width?: number, height?: number, quality?: number): Promise<ArrayBuffer> {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/credit-cards/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get favicon\n     *\n     * Use this endpoint to fetch the favorite icon (AKA favicon) of any remote website URL.\n\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getFavicon(url: string): Promise<ArrayBuffer> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/favicon';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get country flag\n     *\n     * You can use this endpoint to show different country flags icons to your users. The code argument receives the 2 letter country code. Use width, height and quality arguments to change the output settings. Country codes follow the [ISO 3166-1](https://en.wikipedia.org/wiki/ISO_3166-1) standard.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {Flag} code\n     * @param {number} width\n     * @param {number} height\n     * @param {number} quality\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getFlag(code: Flag, width?: number, height?: number, quality?: number): Promise<ArrayBuffer> {\n        if (typeof code === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"code\"');\n        }\n        const apiPath = '/avatars/flags/{code}'.replace('{code}', code);\n        const payload: Payload = {};\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof quality !== 'undefined') {\n            payload['quality'] = quality;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get image from URL\n     *\n     * Use this endpoint to fetch a remote image URL and crop it to any image size you want. This endpoint is very useful if you need to crop and display remote images in your app or in case you want to make sure a 3rd party image is properly served using a TLS protocol.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 400x400px.\n\n     *\n     * @param {string} url\n     * @param {number} width\n     * @param {number} height\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getImage(url: string, width?: number, height?: number): Promise<ArrayBuffer> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/avatars/image';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get user initials\n     *\n     * Use this endpoint to show your user initials avatar icon on your website or app. By default, this route will try to print your logged-in user name or email initials. You can also overwrite the user name if you pass the &#039;name&#039; parameter. If no name is given and no user is logged, an empty avatar will be returned.\n\nYou can use the color and background params to change the avatar colors. By default, a random theme will be selected. The random theme will persist for the user&#039;s initials when reloading the same theme will always return for the same initials.\n\nWhen one dimension is specified and the other is 0, the image is scaled with preserved aspect ratio. If both dimensions are 0, the API provides an image at source quality. If dimensions are not specified, the default size of image returned is 100x100px.\n\n     *\n     * @param {string} name\n     * @param {number} width\n     * @param {number} height\n     * @param {string} background\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getInitials(name?: string, width?: number, height?: number, background?: string): Promise<ArrayBuffer> {\n        const apiPath = '/avatars/initials';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        if (typeof width !== 'undefined') {\n            payload['width'] = width;\n        }\n        if (typeof height !== 'undefined') {\n            payload['height'] = height;\n        }\n        if (typeof background !== 'undefined') {\n            payload['background'] = background;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n    /**\n     * Get QR code\n     *\n     * Converts a given plain text to a QR code image. You can use the query parameters to change the size and style of the resulting image.\n\n     *\n     * @param {string} text\n     * @param {number} size\n     * @param {number} margin\n     * @param {boolean} download\n     * @throws {AppwriteException}\n     * @returns {Promise<ArrayBuffer>}\n     */\n    async getQR(text: string, size?: number, margin?: number, download?: boolean): Promise<ArrayBuffer> {\n        if (typeof text === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"text\"');\n        }\n        const apiPath = '/avatars/qr';\n        const payload: Payload = {};\n        if (typeof text !== 'undefined') {\n            payload['text'] = text;\n        }\n        if (typeof size !== 'undefined') {\n            payload['size'] = size;\n        }\n        if (typeof margin !== 'undefined') {\n            payload['margin'] = margin;\n        }\n        if (typeof download !== 'undefined') {\n            payload['download'] = download;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n            'arrayBuffer'\n        );\n    }\n}\n"]}