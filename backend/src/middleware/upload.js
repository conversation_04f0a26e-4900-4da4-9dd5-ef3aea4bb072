import multer from 'multer';
import { config } from '../config/config.js';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // Check if file type is allowed
    if (config.upload.allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not allowed. Allowed types: ${config.upload.allowedMimeTypes.join(', ')}`), false);
    }
  }
});

// Error handling middleware for multer
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ 
        success: false, 
        error: `File too large. Maximum size is ${config.upload.maxFileSize / (1024 * 1024)}MB` 
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({ 
        success: false, 
        error: 'Unexpected field name' 
      });
    }
  }
  
  if (error.message.includes('File type')) {
    return res.status(400).json({ 
      success: false, 
      error: error.message 
    });
  }
  
  next(error);
};

export default upload;
