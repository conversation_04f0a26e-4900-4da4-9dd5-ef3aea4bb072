<template>
  <LayoutDashboard>
    <div class="max-w-4xl mx-auto">
      <!-- Page Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Upload Images</h1>
        <p class="text-gray-400">Upload and manage your images with custom embed settings</p>
      </div>

      <!-- Upload Area -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <div 
          @drop="onDrop"
          @dragover="onDragOver"
          @dragleave="onDragLeave"
          :class="[
            'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
            isDragOver ? 'border-purple-400 bg-purple-400/10' : 'border-gray-600 hover:border-gray-500'
          ]"
        >
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/*"
            @change="onFileSelect"
            class="hidden"
          >
          
          <div class="flex flex-col items-center">
            <Upload class="w-12 h-12 text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-white mb-2">Drop images here or click to browse</h3>
            <p class="text-gray-400 mb-4">Supports JPEG, PNG, GIF, WebP up to 50MB</p>
            <button
              @click="$refs.fileInput.click()"
              class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Choose Files
            </button>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="uploading" class="mt-6">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm text-gray-300">Uploading...</span>
            <span class="text-sm text-gray-300">{{ uploadProgress }}%</span>
          </div>
          <div class="w-full bg-gray-700 rounded-full h-2">
            <div 
              class="bg-purple-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: uploadProgress + '%' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Embed Settings -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-white mb-4">Embed Settings</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Title</label>
            <input
              v-model="embedSettings.title"
              type="text"
              placeholder="Custom embed title"
              class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Color</label>
            <input
              v-model="embedSettings.color"
              type="color"
              class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 h-10 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
          </div>
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
            <textarea
              v-model="embedSettings.description"
              placeholder="Custom embed description"
              rows="3"
              class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            ></textarea>
          </div>
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-300 mb-2">Footer</label>
            <input
              v-model="embedSettings.footer"
              type="text"
              placeholder="Custom embed footer"
              class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
          </div>
          <div class="md:col-span-2">
            <label class="flex items-center space-x-2">
              <input
                v-model="embedSettings.sharex_enabled"
                type="checkbox"
                class="form-checkbox h-4 w-4 text-purple-600"
              >
              <span class="text-sm text-gray-300">Enable ShareX compatibility</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Recent Uploads -->
      <div class="bg-gray-800 rounded-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-white">Recent Uploads</h2>
          <button
            @click="loadImages"
            class="text-purple-400 hover:text-purple-300 text-sm flex items-center space-x-1"
          >
            <RefreshCw class="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>

        <div v-if="imagesStore.loading && !imagesStore.images.length" class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p class="text-gray-400">Loading images...</p>
        </div>

        <div v-else-if="!imagesStore.images.length" class="text-center py-8">
          <Image class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-400">No images uploaded yet</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="image in imagesStore.sortedImages"
            :key="image.$id"
            class="bg-gray-700 rounded-lg overflow-hidden group hover:bg-gray-600 transition-colors"
          >
            <div class="aspect-video bg-gray-600 relative">
              <img
                :src="image.file_url"
                :alt="image.filename"
                class="w-full h-full object-cover"
                @error="onImageError"
              >
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                  <button
                    @click="copyImageUrl(image)"
                    class="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-lg"
                    title="Copy URL"
                  >
                    <Copy class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteImage(image.$id)"
                    class="bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg"
                    title="Delete"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
            <div class="p-4">
              <h3 class="text-white font-medium truncate mb-1">{{ image.embed_title || image.filename }}</h3>
              <div class="flex items-center justify-between text-sm text-gray-400">
                <span>{{ formatFileSize(image.size) }}</span>
                <span>{{ formatDate(image.uploaded_at) }}</span>
              </div>
              <div class="flex items-center justify-between mt-2 text-sm">
                <span class="text-gray-400">Views: {{ image.views || 0 }}</span>
                <div class="flex items-center space-x-1">
                  <div 
                    class="w-3 h-3 rounded-full"
                    :style="{ backgroundColor: image.embed_color }"
                  ></div>
                  <span class="text-gray-400">{{ image.embed_color }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </LayoutDashboard>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useImagesStore } from '../stores/images.js'
import { useUserStore } from '../stores/user.js'
import LayoutDashboard from '../components/LayoutDashboard.vue'
import { Upload, RefreshCw, Image, Copy, Trash2 } from 'lucide-vue-next'

const imagesStore = useImagesStore()
const userStore = useUserStore()

const fileInput = ref(null)
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)

const embedSettings = reactive({
  title: '',
  description: '',
  color: '#7c3aed',
  footer: 'Powered by AveImgCloud ⭐',
  sharex_enabled: false
})

// Drag and drop handlers
const onDragOver = (e) => {
  e.preventDefault()
  isDragOver.value = true
}

const onDragLeave = (e) => {
  e.preventDefault()
  isDragOver.value = false
}

const onDrop = (e) => {
  e.preventDefault()
  isDragOver.value = false
  
  const files = Array.from(e.dataTransfer.files).filter(file => 
    file.type.startsWith('image/')
  )
  
  if (files.length > 0) {
    uploadFiles(files)
  }
}

const onFileSelect = (e) => {
  const files = Array.from(e.target.files)
  if (files.length > 0) {
    uploadFiles(files)
  }
}

const uploadFiles = async (files) => {
  uploading.value = true
  uploadProgress.value = 0
  
  try {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      // Set title to filename if not set
      if (!embedSettings.title) {
        embedSettings.title = file.name
      }
      
      await imagesStore.uploadImage(file, embedSettings)
      
      uploadProgress.value = Math.round(((i + 1) / files.length) * 100)
    }
    
    // Reset form
    embedSettings.title = ''
    embedSettings.description = ''
    fileInput.value.value = ''
    
    // Refresh user stats
    await userStore.fetchProfile()
    
  } catch (error) {
    console.error('Upload error:', error)
    alert('Upload failed: ' + error.message)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const loadImages = async () => {
  try {
    await imagesStore.fetchImages()
  } catch (error) {
    console.error('Load images error:', error)
  }
}

const copyImageUrl = async (image) => {
  try {
    await navigator.clipboard.writeText(image.file_url)
    alert('Image URL copied to clipboard!')
  } catch (error) {
    console.error('Copy error:', error)
    alert('Failed to copy URL')
  }
}

const deleteImage = async (imageId) => {
  if (!confirm('Are you sure you want to delete this image?')) {
    return
  }
  
  try {
    await imagesStore.deleteImage(imageId)
    await userStore.fetchProfile() // Refresh user stats
  } catch (error) {
    console.error('Delete error:', error)
    alert('Failed to delete image: ' + error.message)
  }
}

const onImageError = (e) => {
  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0xMiAxNkw4IDEySDEwVjhIMTRWMTJIMTZMMTIgMTZaIiBmaWxsPSIjNkI3Mjg5Ii8+Cjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3Mjg5IiBmb250LXNpemU9IjEwIj5JbWFnZTwvdGV4dD4KPHN2Zz4K'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

onMounted(() => {
  loadImages()
})
</script>
