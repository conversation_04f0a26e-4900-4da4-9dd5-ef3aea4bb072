"use strict";var u=Object.defineProperty;var o=(e,t)=>u(e,"name",{value:t,configurable:!0});const http=require("node:http"),https=require("node:https"),index=require("./shared/node-fetch-native-with-agent.df7e6bd6.cjs"),nodeFetchNativeWithAgent=require("node-fetch-native-with-agent");require("node:assert"),require("node:net"),require("node:stream"),require("node:buffer"),require("node:util"),require("node:querystring"),require("node:diagnostics_channel"),require("node:events"),require("./shared/node-fetch-native-with-agent.61758d11.cjs"),require("node:tls"),require("node:zlib"),require("node:perf_hooks"),require("node:util/types"),require("node:os"),require("node:url"),require("node:async_hooks"),require("node:console"),require("string_decoder"),require("node:worker_threads");var i=Object.defineProperty,r=o((e,t)=>i(e,"name",{value:t,configurable:!0}),"r");function createAgent(e,t={}){const n={rejectUnauthorized:t.rejectUnauthorized},c=e?.startsWith("https:")?new https.Agent(n):new http.Agent,s=new index.Agent_1({connect:n});return{agent:c,dispatcher:s}}o(createAgent,"createAgent"),r(createAgent,"createAgent");function createFetch(e={}){const t=createAgent(void 0,e);return(n,c)=>nodeFetchNativeWithAgent.fetch(n,{...t,...c})}o(createFetch,"createFetch"),r(createFetch,"createFetch");const fetch=createFetch({});exports.createAgent=createAgent,exports.createFetch=createFetch,exports.fetch=fetch;
