<template>
    <div class="min-h-screen bg-gradient text-gray-100">
        <div v-if="loading" class="fixed inset-0 z-50 flex items-center justify-center bg-gradient">
            <div class="text-center">
                <div class="w-16 h-16 mb-4 mx-auto">
                    <svg class="animate-spin" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                </div>
                <div
                    class="text-xl font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
                    Loading...
                </div>
            </div>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <template v-if="!loading">
            <div v-if="isSidebarOpen" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
                @click="closeSidebar"></div>

            <!-- Top Navigation Bar -->
            <nav
                class="fixed top-0 left-0 right-0 h-16 bg-gray-900/50 backdrop-blur-sm border-b border-gray-700/50 z-30">
                <div class="h-full px-4 flex items-center justify-between">
                    <!-- Left: Logo & Menu Button -->
                    <div class="flex items-center gap-3">
                        <button class="lg:hidden p-2 hover:bg-gray-800/50 rounded-lg" @click="toggleSidebar">
                            <MenuIcon v-if="!isSidebarOpen" class="w-5 h-5" />
                            <XIcon v-else class="w-5 h-5" />
                        </button>

                        <div class="flex items-center gap-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                <ImageIcon class="w-5 h-5" />
                            </div>
                            <span
                                class="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                                ImageHost Pro
                            </span>
                        </div>
                        
                        <!-- Search Bar (Desktop) -->
                        <div class="hidden lg:block absolute left-1/2 transform -translate-x-1/2">
                            <div class="relative">
                                <SearchIcon class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                                <input type="text" placeholder="Search images, albums... (Ctrl + /)"
                                    class="px-10 py-2 w-80 bg-gray-800/50 border border-gray-700/50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                                    @click="toggleSearch" readonly />
                            </div>
                        </div>
                        
                        <!-- Search Icon (Mobile) -->
                        <button class="lg:hidden p-2 hover:bg-gray-800/50 rounded-lg" @click="toggleSearch">
                            <SearchIcon class="w-5 h-5" />
                        </button>
                    </div>

                    <!-- Right: Actions -->
                    <div class="flex items-center gap-2">
                        <!-- Quick Upload Button -->
                        <button @click="quickUpload" class="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-lg text-sm font-medium transition-all duration-200">
                            <UploadIcon class="w-4 h-4" />
                            <span class="hidden sm:inline">Upload</span>
                        </button>
                        
                        <button @click="toggleNotifications" class="p-2 hover:bg-gray-800/50 rounded-lg relative">
                            <BellIcon class="w-5 h-5" />
                            <span v-if="unreadNotifications > 0" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs flex items-center justify-center">{{ unreadNotifications }}</span>
                        </button>

                        <button @click="toggleProfile" class="p-2 hover:bg-gray-800/50 rounded-lg">
                            <UserIcon class="w-5 h-5" />
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Sidebar -->
            <aside
                class="fixed top-0 left-0 h-full w-64 bg-gray-900/50 backdrop-blur-sm border-r border-gray-700/50 transform transition-transform duration-200 ease-in-out z-50 lg:translate-x-0 lg:z-20"
                :class="isSidebarOpen ? 'translate-x-0' : '-translate-x-full'">
                <!-- Sidebar Content -->
                <div class="flex flex-col h-full pt-16">
                    <!-- Storage Usage -->
                    <div class="p-4 border-b border-gray-700/50">
                        <div class="text-sm text-gray-400 mb-2">Storage Usage</div>
                        <div class="w-full bg-gray-700/50 rounded-full h-2 mb-2">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" :style="`width: ${storageUsage}%`"></div>
                        </div>
                        <div class="text-xs text-gray-400">{{ usedStorage }} / {{ totalStorage }} used</div>
                    </div>
                    
                    <div class="flex-1 overflow-y-auto">
                        <nav class="p-4">
                            <div v-for="(section, index) in menuSections" :key="index" class="mb-6">
                                <div class="text-xs uppercase tracking-wider text-gray-500 font-medium px-4 mb-3">
                                    {{ section.title }}
                                </div>
                                <div class="space-y-1">
                                    <router-link v-for="item in section.items" :key="item.name" :to="item.href"
                                        class="flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-800/50 transition-colors group"
                                        :class="{ 'bg-purple-500/10 text-purple-400 border-r-2 border-purple-500': item.active }"
                                        @click="setActiveItem(item)">
                                        <component :is="item.icon" class="w-5 h-5 transition-colors" 
                                            :class="item.active ? 'text-purple-400' : 'text-gray-400 group-hover:text-purple-400'" />
                                        <span class="font-medium">{{ item.name }}</span>
                                        <span v-if="item.badge" class="ml-auto px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
                                            {{ item.badge }}
                                        </span>
                                    </router-link>
                                </div>
                            </div>
                        </nav>
                    </div>
                    
                    <!-- Upgrade Banner (only for non-premium users) -->
                    <div v-if="user.plan === 'Free'" class="p-4 border-t border-gray-700/50">
                        <div class="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg p-4">
                            <div class="flex items-center gap-3 mb-2">
                                <StarIcon class="w-5 h-5 text-yellow-400" />
                                <span class="font-semibold text-sm">Upgrade to Pro</span>
                            </div>
                            <p class="text-xs text-gray-400 mb-3">Get unlimited storage and advanced features</p>
                            <button class="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white text-sm font-medium py-2 rounded-lg transition-all duration-200">
                                Upgrade Now
                            </button>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="pt-16 lg:pl-64 min-h-screen pb-20">
                <div class="p-6">
                    <slot></slot>
                </div>
            </main>

            <!-- Search Modal -->
            <div v-if="isSearchOpen" class="fixed inset-0 bg-gray-900/95 backdrop-blur-sm z-50" @click="closeSearch">
                <div class="max-w-3xl mx-auto pt-32 px-4" @click.stop>
                    <div class="relative">
                        <SearchIcon class="absolute left-4 top-3.5 h-5 w-5 text-gray-400" />
                        <input type="text" placeholder="Search images, albums, users..."
                            class="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg pl-11 pr-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                            @keydown.esc="closeSearch" ref="searchInput" />
                    </div>
                    <div class="mt-4 text-sm text-gray-400">
                        <div class="flex gap-4">
                            <span>Press <kbd class="px-2 py-1 bg-gray-700 rounded">Enter</kbd> to search</span>
                            <span>Press <kbd class="px-2 py-1 bg-gray-700 rounded">Esc</kbd> to close</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Dropdown -->
            <div v-if="isNotificationsOpen"
                class="absolute top-16 right-4 w-80 bg-gray-900/95 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl z-50 dropdown">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-purple-400">Notifications</h3>
                        <button @click="markAllAsRead" class="text-xs text-gray-400 hover:text-purple-400">Mark all as read</button>
                    </div>
                    <div class="space-y-3 max-h-80 overflow-y-auto">
                        <div v-for="notification in notifications" :key="notification.id"
                            class="flex items-start gap-3 p-3 hover:bg-gray-800/50 rounded-lg transition-colors"
                            :class="{ 'bg-purple-500/5 border-l-2 border-purple-500': !notification.read }">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
                                :class="notification.type === 'upload' ? 'bg-green-500/20 text-green-400' : 
                                       notification.type === 'storage' ? 'bg-yellow-500/20 text-yellow-400' :
                                       'bg-blue-500/20 text-blue-400'">
                                <component :is="notification.icon" class="h-4 w-4" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="font-medium text-sm">{{ notification.title }}</p>
                                <p class="text-xs text-gray-400 mt-1">{{ notification.message }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ notification.time }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Dropdown -->
            <div v-if="isProfileOpen"
                class="absolute top-16 right-4 w-64 bg-gray-900/95 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl z-50 dropdown">
                <div class="p-4">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="h-12 w-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center relative">
                            <UserIcon class="h-6 w-6 text-white" />
                            <div v-if="user.isVerified" class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <CheckCircle class="h-2.5 w-2.5 text-white" />
                            </div>
                        </div>
                        <div>
                            <p class="font-medium">{{ user.name }}</p>
                            <p class="text-sm text-gray-400">{{ user.email }}</p>
                            <div class="flex items-center gap-2 mt-1">
                                <p class="text-xs text-purple-400">{{ user.plan }} Plan</p>
                                <span v-if="user.isVerified" class="text-xs text-green-400">✓ Verified</span>
                                <span v-if="isAdmin" class="text-xs text-yellow-400">Admin</span>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-1">
                        <router-link v-for="item in profileMenu" :key="item.name" :to="item.href"
                            class="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-800/50 transition-colors flex items-center gap-3 group"
                            @click="item.action && item.action()">
                            <component :is="item.icon" class="h-4 w-4 text-gray-400 group-hover:text-purple-400" />
                            <span class="text-sm">{{ item.name }}</span>
                        </router-link>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer
                class="fixed bottom-0 left-0 right-0 bg-gray-900/50 backdrop-blur-sm border-t border-gray-700/50 py-4 z-20 lg:pl-64">
                <div class="flex justify-between items-center text-gray-400 text-sm px-6">
                    <div class="flex items-center gap-4">
                        <span>© 2020-{{ new Date().getFullYear() }} ImageHost Pro</span>
                        <span class="text-xs">v2.1.0</span>
                    </div>
                    <div class="flex gap-4">
                        <a href="/terms" class="hover:text-purple-400 transition-colors">Terms</a>
                        <a href="/privacy" class="hover:text-purple-400 transition-colors">Privacy</a>
                        <a href="/support" class="hover:text-purple-400 transition-colors">Support</a>
                    </div>
                </div>
            </footer>
        </template>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import {
    Search as SearchIcon,
    Bell as BellIcon,
    User as UserIcon,
    Menu as MenuIcon,
    X as XIcon,
    Image as ImageIcon,
    Upload as UploadIcon,
    Star as StarIcon,
    LayoutDashboard as DashboardIcon,
    FolderOpen as AlbumIcon,
    Image as GalleryIcon,
    Share2 as ShareIcon,
    Settings as SettingsIcon,
    Trash2 as TrashIcon,
    LogOut as LogOutIcon,
    CheckCircle as UploadSuccessIcon,
    AlertTriangle as StorageWarningIcon,
    Info as InfoIcon,
    Crown as AdminIcon
} from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(true)
const isSidebarOpen = ref(false)
const isSearchOpen = ref(false)
const isNotificationsOpen = ref(false)
const isProfileOpen = ref(false)
const searchInput = ref(null)

// User data from auth store
const user = computed(() => ({
    name: authStore.user?.name || 'Guest User',
    email: authStore.user?.email || '<EMAIL>',
    plan: authStore.user?.labels?.includes('premium') ? 'Premium' : 
           authStore.user?.labels?.includes('pro') ? 'Pro' : 'Free',
    avatar: authStore.user?.prefs?.avatar || null,
    isVerified: authStore.user?.emailVerification || false
}))

// Check if user is admin
const isAdmin = computed(() => authStore.user?.labels?.includes('admin') || false)

// Storage data - this would come from a storage API in a real app
const storageUsage = ref(67)
const usedStorage = ref('2.1 GB')
const totalStorage = ref('5 GB')
const unreadNotifications = ref(3)

// Menu Sections - Update with proper routing
const menuSections = computed(() => {
    const sections = [
        {
            title: 'Main',
            items: [
                { name: 'Dashboard', icon: DashboardIcon, href: '/dashboard', active: true },
                { name: 'My Images', icon: GalleryIcon, href: '/dashboard/images', active: false },
                { name: 'Upload', icon: UploadIcon, href: '/dashboard/upload', active: false }
            ]
        },
        {
            title: 'Organize',
            items: [
                { name: 'Albums', icon: AlbumIcon, href: '/albums', active: false },
                { name: 'Favorites', icon: StarIcon, href: '/favorites', active: false },
                { name: 'Shared', icon: ShareIcon, href: '/shared', active: false }
            ]
        },
        {
            title: 'Tools',
            items: [
                { name: 'ShareX Setup', icon: ShareIcon, href: '/sharex', active: false },
                { name: 'Store', icon: StarIcon, href: '/store', active: false }
            ]
        },
        {
            title: 'Account',
            items: [
                { name: 'Settings', icon: SettingsIcon, href: '/dashboard/account', active: false },
                { name: 'Trash', icon: TrashIcon, href: '/trash', active: false }
            ]
        }
    ]

    // Add admin section if user is admin
    if (isAdmin.value) {
        sections.push({
            title: 'Administration',
            items: [
                { name: 'Admin Panel', icon: AdminIcon, href: '/admin', active: false }
            ]
        })
    }

    return sections
})

// Profile Menu with logout functionality
const profileMenu = computed(() => [
    { name: 'Account Settings', icon: SettingsIcon, href: '/account/settings' },
    { name: 'API Keys', icon: InfoIcon, href: '/account/api-keys' },
    { name: 'Support', icon: InfoIcon, href: '/support' },
    { name: 'Sign Out', icon: LogOutIcon, href: '#', action: handleLogout }
])

// Logout function
const handleLogout = async () => {
    try {
        await authStore.logout()
        router.push('/auth/login')
    } catch (error) {
        console.error('Logout error:', error)
    }
}

// Notifications with image hosting context
const notifications = ref([
    { 
        id: 1, 
        title: 'Image uploaded successfully', 
        message: 'vacation-photo.jpg has been uploaded to Summer 2024 album',
        time: '2 minutes ago', 
        icon: UploadSuccessIcon,
        type: 'upload',
        read: false
    },
    { 
        id: 2, 
        title: 'Storage warning', 
        message: 'You are using 85% of your storage quota',
        time: '1 hour ago', 
        icon: StorageWarningIcon,
        type: 'storage',
        read: false
    },
    { 
        id: 3, 
        title: 'Shared album viewed', 
        message: 'Someone viewed your "Wedding Photos" album',
        time: '3 hours ago', 
        icon: ShareIcon,
        type: 'activity',
        read: false
    },
    { 
        id: 4, 
        title: 'Bulk upload completed', 
        message: '24 images uploaded to Travel album',
        time: '1 day ago', 
        icon: UploadIcon,
        type: 'upload',
        read: true
    }
])

// Functions
const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value
}

const closeSidebar = () => {
    isSidebarOpen.value = false
}

const toggleSearch = () => {
    isSearchOpen.value = true
    isNotificationsOpen.value = false
    isProfileOpen.value = false
    setTimeout(() => {
        searchInput.value?.focus()
    }, 100)
}

const toggleNotifications = () => {
    isNotificationsOpen.value = !isNotificationsOpen.value
    isProfileOpen.value = false
    isSearchOpen.value = false
}

const toggleProfile = () => {
    isProfileOpen.value = !isProfileOpen.value
    isNotificationsOpen.value = false
    isSearchOpen.value = false
}

const closeSearch = () => {
    isSearchOpen.value = false
}

const closeDropdowns = () => {
    isNotificationsOpen.value = false
    isProfileOpen.value = false
}

const setActiveItem = (item) => {
    menuSections.value.forEach(section => {
        section.items.forEach(menuItem => {
            menuItem.active = menuItem.name === item.name
        })
    })
}

const quickUpload = () => {
    // Trigger file upload dialog
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = true
    input.accept = 'image/*'
    input.onchange = (e) => {
        const files = e.target.files
        if (files.length > 0) {
            console.log('Files selected for upload:', files)
            // Handle file upload logic here
        }
    }
    input.click()
}

const markAllAsRead = () => {
    notifications.value.forEach(notification => {
        notification.read = true
    })
    unreadNotifications.value = 0
}

// Event Handlers
const handleClickOutside = (event) => {
    if (!event.target.closest('.dropdown') && !event.target.closest('button')) {
        closeDropdowns()
    }
}

const handleKeydown = (event) => {
    if (event.ctrlKey && event.key === '/') {
        event.preventDefault()
        toggleSearch()
    }
    if (event.key === 'Escape') {
        closeSearch()
        closeDropdowns()
        closeSidebar()
    }
}

// Lifecycle Hooks
onMounted(async () => {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
    
    // Auth is already initialized by router guard, no need to call initAuth again
    // Just simulate loading
    setTimeout(() => {
        loading.value = false
    }, 1000)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.bg-gradient {
    background: radial-gradient(circle at center, #2D1B69 0%, #1a103f 50%, #0F0A24 100%);
}

:deep(.dropdown) {
    animation: dropdown 0.2s ease-out;
}

@keyframes dropdown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

kbd {
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.75rem;
}
</style>