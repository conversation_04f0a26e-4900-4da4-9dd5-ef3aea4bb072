import { UserService } from '../services/userService.js';

// Webhook handler for user creation
export const handleUserCreated = async (req, res) => {
  try {
    const { userId, email, name } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Create user profile
    await UserService.createUserProfile(userId, {
      email,
      name
    });

    res.json({
      success: true,
      message: 'User profile created successfully'
    });
  } catch (error) {
    console.error('User creation webhook error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create user profile'
    });
  }
};

// Initialize user profile (called from frontend after registration)
export const initializeUserProfile = async (req, res) => {
  try {
    const userId = req.user.$id;
    
    // Check if profile already exists
    const existingProfile = await UserService.getUserProfile(userId);
    
    if (existingProfile) {
      return res.json({
        success: true,
        message: 'User profile already exists',
        data: existingProfile
      });
    }

    // Create new profile
    const profile = await UserService.createUserProfile(userId);

    res.json({
      success: true,
      message: 'User profile initialized successfully',
      data: profile
    });
  } catch (error) {
    console.error('Initialize user profile error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to initialize user profile'
    });
  }
};
