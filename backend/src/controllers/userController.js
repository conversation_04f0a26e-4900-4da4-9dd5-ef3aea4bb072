import { UserService } from '../services/userService.js';
import { StorageService } from '../services/storageService.js';

// Get current user profile with stats
export const getUserProfile = async (req, res) => {
  try {
    const userId = req.user.$id;
    const stats = await UserService.getUserStats(userId);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get user profile'
    });
  }
};

// Update user profile settings
export const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user.$id;
    const { embedFooter, embedColor } = req.body;

    // Validate inputs
    const updates = {};
    if (embedFooter !== undefined) {
      if (typeof embedFooter !== 'string' || embedFooter.length > 100) {
        return res.status(400).json({
          success: false,
          error: 'Embed footer must be a string with max 100 characters'
        });
      }
      updates.embedFooter = embedFooter;
    }

    if (embedColor !== undefined) {
      if (typeof embedColor !== 'string' || !/^#[0-9A-F]{6}$/i.test(embedColor)) {
        return res.status(400).json({
          success: false,
          error: 'Embed color must be a valid hex color code'
        });
      }
      updates.embedColor = embedColor;
    }

    const updatedProfile = await UserService.updateUserProfile(userId, updates);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: updatedProfile
    });
  } catch (error) {
    console.error('Update user profile error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update profile'
    });
  }
};

// Get user credits
export const getUserCredits = async (req, res) => {
  try {
    const userId = req.user.$id;
    const profile = await UserService.getUserProfile(userId);

    res.json({
      success: true,
      data: {
        credits: profile.credits,
        storageUsed: profile.storageUsed,
        storageLimit: profile.storageLimit
      }
    });
  } catch (error) {
    console.error('Get user credits error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get user credits'
    });
  }
};

// Purchase credits (placeholder for payment integration)
export const purchaseCredits = async (req, res) => {
  try {
    const userId = req.user.$id;
    const { amount, packageType } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid credit amount'
      });
    }

    // TODO: Integrate with payment system
    // For now, we'll simulate successful purchase
    const updatedProfile = await UserService.updateCredits(userId, amount);

    res.json({
      success: true,
      message: `Successfully purchased ${amount} credits`,
      data: {
        credits: updatedProfile.credits
      }
    });
  } catch (error) {
    console.error('Purchase credits error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to purchase credits'
    });
  }
};

// Purchase storage plan
export const purchaseStorage = async (req, res) => {
  try {
    const userId = req.user.$id;
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({
        success: false,
        error: 'Plan ID is required'
      });
    }

    const result = await StorageService.purchaseStoragePlan(userId, planId);

    res.json({
      success: true,
      message: result.message,
      data: {
        transaction: result.transaction,
        profile: result.updatedProfile
      }
    });
  } catch (error) {
    console.error('Purchase storage error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to purchase storage'
    });
  }
};

// Get user usage statistics
export const getUserUsage = async (req, res) => {
  try {
    const userId = req.user.$id;
    const stats = await UserService.getUserStats(userId);
    
    res.json({
      success: true,
      usage: {
        storage_used: stats.storage_used,
        storage_quota: stats.storage_quota,
        credits: stats.credits,
        images_count: stats.images_count,
        storage_percentage: ((stats.storage_used / stats.storage_quota) * 100).toFixed(1)
      }
    });
  } catch (error) {
    console.error('Get user usage error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get usage statistics'
    });
  }
};

// Initialize user profile (webhook)
export const initializeUserProfile = async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const profile = await UserService.initializeUserProfile(userId);
    
    res.json({
      success: true,
      profile
    });
  } catch (error) {
    console.error('Initialize user profile error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to initialize user profile'
    });
  }
};

// Get available storage plans
export const getStoragePlans = async (req, res) => {
  try {
    const plans = await StorageService.getAllStoragePlans(true); // Only active plans

    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Get storage plans error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get storage plans'
    });
  }
};

// Get user transactions
export const getUserTransactions = async (req, res) => {
  try {
    const userId = req.user.$id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const result = await StorageService.getUserTransactions(userId, parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: {
        transactions: result.transactions,
        total: result.total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(result.total / limit)
      }
    });
  } catch (error) {
    console.error('Get user transactions error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get transactions'
    });
  }
};
