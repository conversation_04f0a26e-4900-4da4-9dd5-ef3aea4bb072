<template>
  <div class="space-y-6">
    <!-- User Profile Overview -->
    <CardComponent cardTitle="Profile Overview" cardDescription="Your account information and usage statistics">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- User Info -->
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
              {{ userInitials }}
            </div>
            <div>
              <h3 class="text-lg font-semibold text-white">{{ authStore.user?.name || 'User' }}</h3>
              <p class="text-gray-400">{{ authStore.user?.email || '<EMAIL>' }}</p>
              <span v-if="userStore.isAdmin" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-900 text-purple-200 mt-1">
                <Crown class="w-3 h-3 mr-1" />
                Admin
              </span>
            </div>
          </div>
        </div>

        <!-- Usage Stats -->
        <div class="space-y-4">
          <div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-400">Credits</span>
              <span class="text-sm font-semibold text-purple-400">{{ userStore.credits }}</span>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-sm text-gray-400">Storage</span>
              <span class="text-sm font-semibold text-blue-400">
                {{ userStore.storageUsedMB }}MB / {{ userStore.storageLimitMB }}MB
              </span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full" 
                   :style="{ width: `${userStore.storageUsagePercent}%` }"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">{{ userStore.storageUsagePercent }}% used</div>
          </div>
        </div>
      </div>
    </CardComponent>

    <!-- Profile Settings -->
    <CardComponent cardTitle="Profile Settings" cardDescription="Manage your profile information">
      <form @submit.prevent="updateProfile" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">Name</label>
            <TextInput v-model="profileForm.name" placeholder="Your full name" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">Email</label>
            <TextInput v-model="profileForm.email" type="email" disabled 
                      class="opacity-50 cursor-not-allowed" />
            <p class="text-xs text-gray-500 mt-1">Email cannot be changed</p>
          </div>
        </div>
        
        <div class="flex gap-3">
          <button type="submit" :disabled="userStore.isLoading"
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors">
            {{ userStore.isLoading ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </CardComponent>

    <!-- Embed Customization -->
    <CardComponent cardTitle="Embed Customization" cardDescription="Customize how your images appear when shared">
      <form @submit.prevent="updateEmbedSettings" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">Embed Footer</label>
            <TextInput v-model="embedForm.embedFooter" placeholder="Powered by AveImgCloud ⭐" maxlength="100" />
            <p class="text-xs text-gray-500 mt-1">Text shown at the bottom of embeds (max 100 chars)</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-400 mb-2">Embed Color</label>
            <div class="flex items-center space-x-2">
              <input type="color" v-model="embedForm.embedColor" 
                     class="w-12 h-10 rounded border border-gray-600 bg-gray-800">
              <TextInput v-model="embedForm.embedColor" placeholder="#7C3AED" class="flex-1" />
            </div>
            <p class="text-xs text-gray-500 mt-1">Color for embed borders and accents</p>
          </div>
        </div>

        <!-- Embed Preview -->
        <div class="mt-6">
          <h4 class="text-sm font-medium text-gray-400 mb-3">Preview</h4>
          <div class="bg-gray-800 rounded-lg p-4 border-l-4" :style="{ borderLeftColor: embedForm.embedColor }">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gray-700 rounded"></div>
              <div>
                <h5 class="text-white font-medium">Image Preview</h5>
                <p class="text-gray-400 text-sm">sample-image.jpg</p>
              </div>
            </div>
            <div class="mt-3 text-xs text-gray-500">{{ embedForm.embedFooter }}</div>
          </div>
        </div>
        
        <div class="flex gap-3">
          <button type="submit" :disabled="userStore.isLoading"
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white rounded-lg transition-colors">
            {{ userStore.isLoading ? 'Saving...' : 'Save Embed Settings' }}
          </button>
        </div>
      </form>
    </CardComponent>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '../../../stores/auth.js'
import { useUserStore } from '../../../stores/user.js'
import CardComponent from '../../ui/Card/CardComponent.vue'
import TextInput from '../../ui/TextForms/TextInput.vue'
import { Crown } from 'lucide-vue-next'
import Swal from 'sweetalert2'

const authStore = useAuthStore()
const userStore = useUserStore()

// Reactive form data
const profileForm = reactive({
  name: '',
  email: ''
})

const embedForm = reactive({
  embedFooter: '',
  embedColor: '#7C3AED'
})

// Computed properties
const userInitials = computed(() => {
  const name = authStore.user?.name || 'U'
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
})

// Methods
const updateProfile = async () => {
  try {
    const result = await authStore.updateProfile({
      name: profileForm.name
    })

    if (result.success) {
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: result.message,
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      throw new Error(result.error)
    }
  } catch (error) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: error.message || 'Failed to update profile'
    })
  }
}

const updateEmbedSettings = async () => {
  try {
    const result = await userStore.updateProfile({
      embedFooter: embedForm.embedFooter,
      embedColor: embedForm.embedColor
    })

    if (result.success) {
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: result.message,
        timer: 2000,
        showConfirmButton: false
      })
    } else {
      throw new Error(result.error)
    }
  } catch (error) {
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: error.message || 'Failed to update embed settings'
    })
  }
}

// Initialize data
onMounted(async () => {
  try {
    // Load user profile
    await userStore.fetchProfile()
    
    // Initialize forms with current data
    profileForm.name = authStore.user?.name || ''
    profileForm.email = authStore.user?.email || ''
    embedForm.embedFooter = userStore.embedFooter
    embedForm.embedColor = userStore.embedColor
  } catch (error) {
    console.error('Failed to load user data:', error)
  }
})
</script>