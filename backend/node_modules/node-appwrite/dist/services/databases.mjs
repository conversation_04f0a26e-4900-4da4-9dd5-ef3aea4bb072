import { AppwriteException } from '../client.mjs';

// src/services/databases.ts
var Databases = class {
  constructor(client) {
    this.client = client;
  }
  /**
   * List databases
   *
   * Get a list of all databases from the current Appwrite project. You can use the search parameter to filter your results.
   *
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.DatabaseList>}
   */
  async list(queries, search) {
    const apiPath = "/databases";
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create database
       *
       * Create a new Database.
  
       *
       * @param {string} databaseId
       * @param {string} name
       * @param {boolean} enabled
       * @throws {AppwriteException}
       * @returns {Promise<Models.Database>}
       */
  async create(databaseId, name, enabled) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof name === "undefined") {
      throw new AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/databases";
    const payload = {};
    if (typeof databaseId !== "undefined") {
      payload["databaseId"] = databaseId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get database
   *
   * Get a database by its unique ID. This endpoint response returns a JSON object with the database metadata.
   *
   * @param {string} databaseId
   * @throws {AppwriteException}
   * @returns {Promise<Models.Database>}
   */
  async get(databaseId) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    const apiPath = "/databases/{databaseId}".replace("{databaseId}", databaseId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update database
   *
   * Update a database by its unique ID.
   *
   * @param {string} databaseId
   * @param {string} name
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Database>}
   */
  async update(databaseId, name, enabled) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof name === "undefined") {
      throw new AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/databases/{databaseId}".replace("{databaseId}", databaseId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete database
   *
   * Delete a database by its unique ID. Only API keys with with databases.write scope can delete a database.
   *
   * @param {string} databaseId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async delete(databaseId) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    const apiPath = "/databases/{databaseId}".replace("{databaseId}", databaseId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List collections
   *
   * Get a list of all collections that belong to the provided databaseId. You can use the search parameter to filter your results.
   *
   * @param {string} databaseId
   * @param {string[]} queries
   * @param {string} search
   * @throws {AppwriteException}
   * @returns {Promise<Models.CollectionList>}
   */
  async listCollections(databaseId, queries, search) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    const apiPath = "/databases/{databaseId}/collections".replace("{databaseId}", databaseId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    if (typeof search !== "undefined") {
      payload["search"] = search;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create collection
   *
   * Create a new Collection. Before using this route, you should create a new database resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} name
   * @param {string[]} permissions
   * @param {boolean} documentSecurity
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Collection>}
   */
  async createCollection(databaseId, collectionId, name, permissions, documentSecurity, enabled) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof name === "undefined") {
      throw new AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/databases/{databaseId}/collections".replace("{databaseId}", databaseId);
    const payload = {};
    if (typeof collectionId !== "undefined") {
      payload["collectionId"] = collectionId;
    }
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    if (typeof documentSecurity !== "undefined") {
      payload["documentSecurity"] = documentSecurity;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get collection
   *
   * Get a collection by its unique ID. This endpoint response returns a JSON object with the collection metadata.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @throws {AppwriteException}
   * @returns {Promise<Models.Collection>}
   */
  async getCollection(databaseId, collectionId) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update collection
   *
   * Update a collection by its unique ID.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} name
   * @param {string[]} permissions
   * @param {boolean} documentSecurity
   * @param {boolean} enabled
   * @throws {AppwriteException}
   * @returns {Promise<Models.Collection>}
   */
  async updateCollection(databaseId, collectionId, name, permissions, documentSecurity, enabled) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof name === "undefined") {
      throw new AppwriteException('Missing required parameter: "name"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof name !== "undefined") {
      payload["name"] = name;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    if (typeof documentSecurity !== "undefined") {
      payload["documentSecurity"] = documentSecurity;
    }
    if (typeof enabled !== "undefined") {
      payload["enabled"] = enabled;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "put",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete collection
   *
   * Delete a collection by its unique ID. Only users with write permissions have access to delete this resource.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteCollection(databaseId, collectionId) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List attributes
   *
   * List attributes in the collection.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.AttributeList>}
   */
  async listAttributes(databaseId, collectionId, queries) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create boolean attribute
       *
       * Create a boolean attribute.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {boolean} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeBoolean>}
       */
  async createBooleanAttribute(databaseId, collectionId, key, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/boolean".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update boolean attribute
   *
   * Update a boolean attribute. Changing the `default` value will not update already existing documents.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @param {boolean} required
   * @param {boolean} xdefault
   * @throws {AppwriteException}
   * @returns {Promise<Models.AttributeBoolean>}
   */
  async updateBooleanAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/boolean/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create datetime attribute
   *
   * Create a date time attribute according to the ISO 8601 standard.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @param {boolean} required
   * @param {string} xdefault
   * @param {boolean} array
   * @throws {AppwriteException}
   * @returns {Promise<Models.AttributeDatetime>}
   */
  async createDatetimeAttribute(databaseId, collectionId, key, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/datetime".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update dateTime attribute
   *
   * Update a date time attribute. Changing the `default` value will not update already existing documents.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @param {boolean} required
   * @param {string} xdefault
   * @throws {AppwriteException}
   * @returns {Promise<Models.AttributeDatetime>}
   */
  async updateDatetimeAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/datetime/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create email attribute
       *
       * Create an email attribute.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeEmail>}
       */
  async createEmailAttribute(databaseId, collectionId, key, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/email".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update email attribute
       *
       * Update an email attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeEmail>}
       */
  async updateEmailAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/email/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create enum attribute
       *
       * Create an enumeration attribute. The `elements` param acts as a white-list of accepted values for this attribute. 
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {string[]} elements
       * @param {boolean} required
       * @param {string} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeEnum>}
       */
  async createEnumAttribute(databaseId, collectionId, key, elements, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof elements === "undefined") {
      throw new AppwriteException('Missing required parameter: "elements"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/enum".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof elements !== "undefined") {
      payload["elements"] = elements;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update enum attribute
       *
       * Update an enum attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {string[]} elements
       * @param {boolean} required
       * @param {string} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeEnum>}
       */
  async updateEnumAttribute(databaseId, collectionId, key, elements, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof elements === "undefined") {
      throw new AppwriteException('Missing required parameter: "elements"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/enum/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof elements !== "undefined") {
      payload["elements"] = elements;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create float attribute
       *
       * Create a float attribute. Optionally, minimum and maximum values can be provided.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {number} min
       * @param {number} max
       * @param {number} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeFloat>}
       */
  async createFloatAttribute(databaseId, collectionId, key, required, min, max, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/float".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof min !== "undefined") {
      payload["min"] = min;
    }
    if (typeof max !== "undefined") {
      payload["max"] = max;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update float attribute
       *
       * Update a float attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {number} min
       * @param {number} max
       * @param {number} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeFloat>}
       */
  async updateFloatAttribute(databaseId, collectionId, key, required, min, max, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof min === "undefined") {
      throw new AppwriteException('Missing required parameter: "min"');
    }
    if (typeof max === "undefined") {
      throw new AppwriteException('Missing required parameter: "max"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/float/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof min !== "undefined") {
      payload["min"] = min;
    }
    if (typeof max !== "undefined") {
      payload["max"] = max;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create integer attribute
       *
       * Create an integer attribute. Optionally, minimum and maximum values can be provided.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {number} min
       * @param {number} max
       * @param {number} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeInteger>}
       */
  async createIntegerAttribute(databaseId, collectionId, key, required, min, max, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/integer".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof min !== "undefined") {
      payload["min"] = min;
    }
    if (typeof max !== "undefined") {
      payload["max"] = max;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update integer attribute
       *
       * Update an integer attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {number} min
       * @param {number} max
       * @param {number} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeInteger>}
       */
  async updateIntegerAttribute(databaseId, collectionId, key, required, min, max, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof min === "undefined") {
      throw new AppwriteException('Missing required parameter: "min"');
    }
    if (typeof max === "undefined") {
      throw new AppwriteException('Missing required parameter: "max"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/integer/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof min !== "undefined") {
      payload["min"] = min;
    }
    if (typeof max !== "undefined") {
      payload["max"] = max;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create IP address attribute
       *
       * Create IP address attribute.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeIp>}
       */
  async createIpAttribute(databaseId, collectionId, key, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/ip".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update IP address attribute
       *
       * Update an ip attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeIp>}
       */
  async updateIpAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/ip/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create relationship attribute
       *
       * Create relationship attribute. [Learn more about relationship attributes](https://appwrite.io/docs/databases-relationships#relationship-attributes).
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} relatedCollectionId
       * @param {RelationshipType} type
       * @param {boolean} twoWay
       * @param {string} key
       * @param {string} twoWayKey
       * @param {RelationMutate} onDelete
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeRelationship>}
       */
  async createRelationshipAttribute(databaseId, collectionId, relatedCollectionId, type, twoWay, key, twoWayKey, onDelete) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof relatedCollectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "relatedCollectionId"');
    }
    if (typeof type === "undefined") {
      throw new AppwriteException('Missing required parameter: "type"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/relationship".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof relatedCollectionId !== "undefined") {
      payload["relatedCollectionId"] = relatedCollectionId;
    }
    if (typeof type !== "undefined") {
      payload["type"] = type;
    }
    if (typeof twoWay !== "undefined") {
      payload["twoWay"] = twoWay;
    }
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof twoWayKey !== "undefined") {
      payload["twoWayKey"] = twoWayKey;
    }
    if (typeof onDelete !== "undefined") {
      payload["onDelete"] = onDelete;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create string attribute
       *
       * Create a string attribute.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {number} size
       * @param {boolean} required
       * @param {string} xdefault
       * @param {boolean} array
       * @param {boolean} encrypt
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeString>}
       */
  async createStringAttribute(databaseId, collectionId, key, size, required, xdefault, array, encrypt) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof size === "undefined") {
      throw new AppwriteException('Missing required parameter: "size"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/string".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof size !== "undefined") {
      payload["size"] = size;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    if (typeof encrypt !== "undefined") {
      payload["encrypt"] = encrypt;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update string attribute
       *
       * Update a string attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeString>}
       */
  async updateStringAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/string/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create URL attribute
       *
       * Create a URL attribute.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @param {boolean} array
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeUrl>}
       */
  async createUrlAttribute(databaseId, collectionId, key, required, xdefault, array) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/url".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    if (typeof array !== "undefined") {
      payload["array"] = array;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update URL attribute
       *
       * Update an url attribute. Changing the `default` value will not update already existing documents.
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {boolean} required
       * @param {string} xdefault
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeUrl>}
       */
  async updateUrlAttribute(databaseId, collectionId, key, required, xdefault) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof required === "undefined") {
      throw new AppwriteException('Missing required parameter: "required"');
    }
    if (typeof xdefault === "undefined") {
      throw new AppwriteException('Missing required parameter: "xdefault"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/url/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof required !== "undefined") {
      payload["required"] = required;
    }
    if (typeof xdefault !== "undefined") {
      payload["default"] = xdefault;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get attribute
   *
   * Get attribute by ID.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async getAttribute(databaseId, collectionId, key) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete attribute
   *
   * Deletes an attribute.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteAttribute(databaseId, collectionId, key) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Update relationship attribute
       *
       * Update relationship attribute. [Learn more about relationship attributes](https://appwrite.io/docs/databases-relationships#relationship-attributes).
  
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {RelationMutate} onDelete
       * @throws {AppwriteException}
       * @returns {Promise<Models.AttributeRelationship>}
       */
  async updateRelationshipAttribute(databaseId, collectionId, key, onDelete) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/attributes/{key}/relationship".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    if (typeof onDelete !== "undefined") {
      payload["onDelete"] = onDelete;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List documents
   *
   * Get a list of all the user&#039;s documents in a given collection. You can use the query params to filter your results.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.DocumentList<Document>>}
   */
  async listDocuments(databaseId, collectionId, queries) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/documents".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Create document
   *
   * Create a new Document. Before using this route, you should create a new collection resource using either a [server integration](https://appwrite.io/docs/server/databases#databasesCreateCollection) API or directly from your database console.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} documentId
   * @param {Omit<Document, keyof Models.Document>} data
   * @param {string[]} permissions
   * @throws {AppwriteException}
   * @returns {Promise<Document>}
   */
  async createDocument(databaseId, collectionId, documentId, data, permissions) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof documentId === "undefined") {
      throw new AppwriteException('Missing required parameter: "documentId"');
    }
    if (typeof data === "undefined") {
      throw new AppwriteException('Missing required parameter: "data"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/documents".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof documentId !== "undefined") {
      payload["documentId"] = documentId;
    }
    if (typeof data !== "undefined") {
      payload["data"] = data;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get document
   *
   * Get a document by its unique ID. This endpoint response returns a JSON object with the document data.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} documentId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Document>}
   */
  async getDocument(databaseId, collectionId, documentId, queries) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof documentId === "undefined") {
      throw new AppwriteException('Missing required parameter: "documentId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/documents/{documentId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{documentId}", documentId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Update document
   *
   * Update a document by its unique ID. Using the patch method you can pass only specific fields that will get updated.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} documentId
   * @param {Partial<Omit<Document, keyof Models.Document>>} data
   * @param {string[]} permissions
   * @throws {AppwriteException}
   * @returns {Promise<Document>}
   */
  async updateDocument(databaseId, collectionId, documentId, data, permissions) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof documentId === "undefined") {
      throw new AppwriteException('Missing required parameter: "documentId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/documents/{documentId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{documentId}", documentId);
    const payload = {};
    if (typeof data !== "undefined") {
      payload["data"] = data;
    }
    if (typeof permissions !== "undefined") {
      payload["permissions"] = permissions;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "patch",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete document
   *
   * Delete a document by its unique ID.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} documentId
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteDocument(databaseId, collectionId, documentId) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof documentId === "undefined") {
      throw new AppwriteException('Missing required parameter: "documentId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/documents/{documentId}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{documentId}", documentId);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * List indexes
   *
   * List indexes in the collection.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string[]} queries
   * @throws {AppwriteException}
   * @returns {Promise<Models.IndexList>}
   */
  async listIndexes(databaseId, collectionId, queries) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/indexes".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof queries !== "undefined") {
      payload["queries"] = queries;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
       * Create index
       *
       * Creates an index on the attributes listed. Your index should include all the attributes you will query in a single request.
  Attributes can be `key`, `fulltext`, and `unique`.
       *
       * @param {string} databaseId
       * @param {string} collectionId
       * @param {string} key
       * @param {IndexType} type
       * @param {string[]} attributes
       * @param {string[]} orders
       * @throws {AppwriteException}
       * @returns {Promise<Models.Index>}
       */
  async createIndex(databaseId, collectionId, key, type, attributes, orders) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    if (typeof type === "undefined") {
      throw new AppwriteException('Missing required parameter: "type"');
    }
    if (typeof attributes === "undefined") {
      throw new AppwriteException('Missing required parameter: "attributes"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/indexes".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId);
    const payload = {};
    if (typeof key !== "undefined") {
      payload["key"] = key;
    }
    if (typeof type !== "undefined") {
      payload["type"] = type;
    }
    if (typeof attributes !== "undefined") {
      payload["attributes"] = attributes;
    }
    if (typeof orders !== "undefined") {
      payload["orders"] = orders;
    }
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "post",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Get index
   *
   * Get index by ID.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @throws {AppwriteException}
   * @returns {Promise<Models.Index>}
   */
  async getIndex(databaseId, collectionId, key) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/indexes/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "get",
      uri,
      apiHeaders,
      payload
    );
  }
  /**
   * Delete index
   *
   * Delete an index.
   *
   * @param {string} databaseId
   * @param {string} collectionId
   * @param {string} key
   * @throws {AppwriteException}
   * @returns {Promise<{}>}
   */
  async deleteIndex(databaseId, collectionId, key) {
    if (typeof databaseId === "undefined") {
      throw new AppwriteException('Missing required parameter: "databaseId"');
    }
    if (typeof collectionId === "undefined") {
      throw new AppwriteException('Missing required parameter: "collectionId"');
    }
    if (typeof key === "undefined") {
      throw new AppwriteException('Missing required parameter: "key"');
    }
    const apiPath = "/databases/{databaseId}/collections/{collectionId}/indexes/{key}".replace("{databaseId}", databaseId).replace("{collectionId}", collectionId).replace("{key}", key);
    const payload = {};
    const uri = new URL(this.client.config.endpoint + apiPath);
    const apiHeaders = {
      "content-type": "application/json"
    };
    return await this.client.call(
      "delete",
      uri,
      apiHeaders,
      payload
    );
  }
};

export { Databases };
//# sourceMappingURL=out.js.map
//# sourceMappingURL=databases.mjs.map