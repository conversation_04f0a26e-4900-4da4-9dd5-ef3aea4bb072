<template>
  <LayoutDashboard>
    <div class="space-y-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-100">Image Gallery</h1>
          <p class="text-gray-400 mt-1">Manage and organize your uploaded images</p>
        </div>
        
        <div class="mt-4 sm:mt-0">
          <router-link
            to="/dashboard"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Upload New Images
          </router-link>
        </div>
      </div>
      
      <ImageGallery />
    </div>
  </LayoutDashboard>
</template>

<script setup>
import LayoutDashboard from '../components/LayoutDashboard.vue'
import ImageGallery from '../components/ImageGallery.vue'
</script>
