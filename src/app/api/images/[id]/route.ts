import { NextRequest, NextResponse } from 'next/server';
import { storage, databases, config } from '@/lib/appwrite';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const imageId = params.id;

    // Get image document to get file_id
    const imageDoc = await databases.getDocument(
      config.appwrite.databaseId,
      config.appwrite.collections.images,
      imageId
    );

    // Delete file from storage
    await storage.deleteFile(config.appwrite.bucketId, imageDoc.file_id);

    // Delete document from database
    await databases.deleteDocument(
      config.appwrite.databaseId,
      config.appwrite.collections.images,
      imageId
    );

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error: any) {
    console.error('Delete image error:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to delete image' },
      { status: 500 }
    );
  }
}
