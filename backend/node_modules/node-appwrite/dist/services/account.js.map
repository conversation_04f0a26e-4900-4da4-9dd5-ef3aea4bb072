{"version": 3, "sources": ["../../src/services/account.ts"], "names": [], "mappings": "AAAA,SAAS,yBAA+D;AAMjE,MAAM,QAAQ;AAAA,EAGjB,YAAY,QAAgB;AACxB,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAiF;AACnF,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,OAA+C,QAAgB,OAAe,UAAkB,MAAkD;AACpJ,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,YAAoD,OAAe,UAAqD;AAC1H,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,SAAkD;AACnE,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,YAAiC;AAClD,QAAI,OAAO,eAAe,aAAa;AACnC,YAAM,IAAI,kBAAkB,0CAA0C;AAAA,IAC1E;AACA,UAAM,UAAU,mCAAmC,QAAQ,gBAAgB,UAAU;AACrF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAiC;AACnC,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,SAAS,SAA6C;AACxD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,UAAkD,KAAiD;AACrG,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,uBAAuB,MAAkD;AAC3E,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,uBAA+D,MAAyB,KAAgD;AAC1I,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,uBAAuB,MAAyB,KAA0B;AAC5E,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU,qCAAqC,QAAQ,UAAU,IAAI;AAC3E,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,mBAAmB,QAA4D;AACjF,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,aAAqB,KAA0B;AACpE,QAAI,OAAO,gBAAgB,aAAa;AACpC,YAAM,IAAI,kBAAkB,2CAA2C;AAAA,IAC3E;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,iBAA6C;AAC/C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,sBAAwD;AAC1D,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,yBAA2D;AAC7D,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,yBAA2D;AAC7D,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,WAAmD,MAAiD;AACtG,QAAI,OAAO,SAAS,aAAa;AAC7B,YAAM,IAAI,kBAAkB,oCAAoC;AAAA,IACpE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,SAAS,aAAa;AAC7B,cAAQ,MAAM,IAAI;AAAA,IACtB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,eAAuD,UAAkB,aAAyD;AACpI,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,QAAI,OAAO,gBAAgB,aAAa;AACpC,cAAQ,aAAa,IAAI;AAAA,IAC7B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,YAAoD,OAAe,UAAqD;AAC1H,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,WAAyE;AAC3E,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAoD,OAAgE;AACtH,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,eAAe,OAAe,KAAoC;AACpE,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,eAAe,QAAgB,QAAgB,UAAyC;AAC1F,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAA4C;AAC9C,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,iBAA8B;AAChC,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,yBAAkD;AACpD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,2BAA2B,OAAe,UAA2C;AACvF,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,cAAQ,UAAU,IAAI;AAAA,IAC1B;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,sBAAsB,QAAgB,QAAyC;AACjF,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,QAAgB,QAAyC;AAC9E,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc,QAAgB,QAAyC;AACzE,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,WAAW,WAA4C;AACzD,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,WAA4C;AAC5D,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,WAAgC;AAChD,QAAI,OAAO,cAAc,aAAa;AAClC,YAAM,IAAI,kBAAkB,yCAAyC;AAAA,IACzE;AACA,UAAM,UAAU,gCAAgC,QAAQ,eAAe,SAAS;AAChF,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAA0F;AAC5F,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,iBAAiB,QAAgB,OAAe,QAAyC;AAC3F,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,oBAAoB,QAAgB,OAAe,KAAc,QAAyC;AAC5G,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,kBAAkB,UAAyB,SAAkB,SAAkB,QAAoC;AACrH,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,IAAI,kBAAkB,wCAAwC;AAAA,IACxE;AACA,UAAM,UAAU,oCAAoC,QAAQ,cAAc,QAAQ;AAClF,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,aAAa;AAChC,cAAQ,SAAS,IAAI;AAAA,IACzB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,iBAAiB,QAAgB,OAAsC;AACzE,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,YAAM,IAAI,kBAAkB,qCAAqC;AAAA,IACrE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,UAAU,aAAa;AAC9B,cAAQ,OAAO,IAAI;AAAA,IACvB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,mBAAmB,KAAoC;AACzD,QAAI,OAAO,QAAQ,aAAa;AAC5B,YAAM,IAAI,kBAAkB,mCAAmC;AAAA,IACnE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,QAAQ,aAAa;AAC5B,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,QAAgB,QAAuC;AAC5E,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,0BAAiD;AACnD,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,wBAAwB,QAAgB,QAAuC;AACjF,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,YAAM,IAAI,kBAAkB,sCAAsC;AAAA,IACtE;AACA,UAAM,UAAU;AAChB,UAAM,UAAmB,CAAC;AAC1B,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,cAAQ,QAAQ,IAAI;AAAA,IACxB;AACA,UAAM,MAAM,IAAI,IAAI,KAAK,OAAO,OAAO,WAAW,OAAO;AAEzD,UAAM,aAA2C;AAAA,MAC7C,gBAAgB;AAAA,IACpB;AAEA,WAAO,MAAM,KAAK,OAAO;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ", "sourcesContent": ["import { AppwriteException, Client, type Payload, UploadProgress } from '../client';\nimport type { Models } from '../models';\nimport { AuthenticatorType } from '../enums/authenticator-type';\nimport { AuthenticationFactor } from '../enums/authentication-factor';\nimport { OAuthProvider } from '../enums/o-auth-provider';\n\nexport class Account {\n    client: Client;\n\n    constructor(client: Client) {\n        this.client = client;\n    }\n\n    /**\n     * Get account\n     *\n     * Get the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async get<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create account\n     *\n     * Use this endpoint to allow a new user to register a new account in your project. After the user registration completes successfully, you can use the [/account/verfication](https://appwrite.io/docs/references/cloud/client-web/account#createVerification) route to start verifying the user email address. To allow the new user to login to their new account, you need to create a new [account session](https://appwrite.io/docs/references/cloud/client-web/account#createEmailSession).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} password\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async create<Preferences extends Models.Preferences>(userId: string, email: string, password: string, name?: string): Promise<Models.User<Preferences>> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update email\n     *\n     * Update currently logged in user account email address. After changing user address, the user confirmation status will get reset. A new confirmation email is not sent automatically however you can use the send confirmation email endpoint again to send the confirmation email. For security measures, user password is required to complete this request.\nThis endpoint can also be used to convert an anonymous account to a normal one, by passing an email address and a new password.\n\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateEmail<Preferences extends Models.Preferences>(email: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List Identities\n     *\n     * Get the list of identities for the currently logged in user.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.IdentityList>}\n     */\n    async listIdentities(queries?: string[]): Promise<Models.IdentityList> {\n        const apiPath = '/account/identities';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete identity\n     *\n     * Delete an identity by its unique ID.\n     *\n     * @param {string} identityId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteIdentity(identityId: string): Promise<{}> {\n        if (typeof identityId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"identityId\"');\n        }\n        const apiPath = '/account/identities/{identityId}'.replace('{identityId}', identityId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create JWT\n     *\n     * Use this endpoint to create a JSON Web Token. You can use the resulting JWT to authenticate on behalf of the current user when working with the Appwrite server-side API and SDKs. The JWT secret is valid for 15 minutes from its creation and will be invalid if the user will logout in that time frame.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Jwt>}\n     */\n    async createJWT(): Promise<Models.Jwt> {\n        const apiPath = '/account/jwt';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List logs\n     *\n     * Get the list of latest security activity logs for the currently logged in user. Each log returns user IP address, location and date and time of log.\n     *\n     * @param {string[]} queries\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.LogList>}\n     */\n    async listLogs(queries?: string[]): Promise<Models.LogList> {\n        const apiPath = '/account/logs';\n        const payload: Payload = {};\n        if (typeof queries !== 'undefined') {\n            payload['queries'] = queries;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update MFA\n     *\n     * Enable or disable MFA on an account.\n     *\n     * @param {boolean} mfa\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateMFA<Preferences extends Models.Preferences>(mfa: boolean): Promise<Models.User<Preferences>> {\n        if (typeof mfa === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"mfa\"');\n        }\n        const apiPath = '/account/mfa';\n        const payload: Payload = {};\n        if (typeof mfa !== 'undefined') {\n            payload['mfa'] = mfa;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Add Authenticator\n     *\n     * Add an authenticator app to be used as an MFA factor. Verify the authenticator using the [verify authenticator](/docs/references/cloud/client-web/account#updateMfaAuthenticator) method.\n     *\n     * @param {AuthenticatorType} type\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaType>}\n     */\n    async createMfaAuthenticator(type: AuthenticatorType): Promise<Models.MfaType> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Verify Authenticator\n     *\n     * Verify an authenticator app after adding it using the [add authenticator](/docs/references/cloud/client-web/account#createMfaAuthenticator) method. add \n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateMfaAuthenticator<Preferences extends Models.Preferences>(type: AuthenticatorType, otp: string): Promise<Models.User<Preferences>> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete Authenticator\n     *\n     * Delete an authenticator for a user by ID.\n     *\n     * @param {AuthenticatorType} type\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteMfaAuthenticator(type: AuthenticatorType, otp: string): Promise<{}> {\n        if (typeof type === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"type\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/authenticators/{type}'.replace('{type}', type);\n        const payload: Payload = {};\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create 2FA Challenge\n     *\n     * Begin the process of MFA verification after sign-in. Finish the flow with [updateMfaChallenge](/docs/references/cloud/client-web/account#updateMfaChallenge) method.\n     *\n     * @param {AuthenticationFactor} factor\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaChallenge>}\n     */\n    async createMfaChallenge(factor: AuthenticationFactor): Promise<Models.MfaChallenge> {\n        if (typeof factor === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"factor\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof factor !== 'undefined') {\n            payload['factor'] = factor;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create MFA Challenge (confirmation)\n     *\n     * Complete the MFA challenge by providing the one-time password. Finish the process of MFA verification by providing the one-time password. To begin the flow, use [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @param {string} challengeId\n     * @param {string} otp\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async updateMfaChallenge(challengeId: string, otp: string): Promise<{}> {\n        if (typeof challengeId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"challengeId\"');\n        }\n        if (typeof otp === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"otp\"');\n        }\n        const apiPath = '/account/mfa/challenge';\n        const payload: Payload = {};\n        if (typeof challengeId !== 'undefined') {\n            payload['challengeId'] = challengeId;\n        }\n        if (typeof otp !== 'undefined') {\n            payload['otp'] = otp;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List Factors\n     *\n     * List the factors available on the account to be used as a MFA challange.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaFactors>}\n     */\n    async listMfaFactors(): Promise<Models.MfaFactors> {\n        const apiPath = '/account/mfa/factors';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get MFA Recovery Codes\n     *\n     * Get recovery codes that can be used as backup for MFA flow. Before getting codes, they must be generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to read recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async getMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create MFA Recovery Codes\n     *\n     * Generate recovery codes as backup for MFA flow. It&#039;s recommended to generate and show then immediately after user successfully adds their authehticator. Recovery codes can be used as a MFA verification type in [createMfaChallenge](/docs/references/cloud/client-web/account#createMfaChallenge) method.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async createMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Regenerate MFA Recovery Codes\n     *\n     * Regenerate recovery codes that can be used as backup for MFA flow. Before regenerating codes, they must be first generated using [createMfaRecoveryCodes](/docs/references/cloud/client-web/account#createMfaRecoveryCodes) method. An OTP challenge is required to regenreate recovery codes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.MfaRecoveryCodes>}\n     */\n    async updateMfaRecoveryCodes(): Promise<Models.MfaRecoveryCodes> {\n        const apiPath = '/account/mfa/recovery-codes';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update name\n     *\n     * Update currently logged in user account name.\n     *\n     * @param {string} name\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateName<Preferences extends Models.Preferences>(name: string): Promise<Models.User<Preferences>> {\n        if (typeof name === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"name\"');\n        }\n        const apiPath = '/account/name';\n        const payload: Payload = {};\n        if (typeof name !== 'undefined') {\n            payload['name'] = name;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update password\n     *\n     * Update currently logged in user password. For validation, user is required to pass in the new password, and the old password. For users created with OAuth, Team Invites and Magic URL, oldPassword is optional.\n     *\n     * @param {string} password\n     * @param {string} oldPassword\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePassword<Preferences extends Models.Preferences>(password: string, oldPassword?: string): Promise<Models.User<Preferences>> {\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/password';\n        const payload: Payload = {};\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        if (typeof oldPassword !== 'undefined') {\n            payload['oldPassword'] = oldPassword;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update phone\n     *\n     * Update the currently logged in user&#039;s phone number. After updating the phone number, the phone verification status will be reset. A confirmation SMS is not sent automatically, however you can use the [POST /account/verification/phone](https://appwrite.io/docs/references/cloud/client-web/account#createPhoneVerification) endpoint to send a confirmation SMS.\n     *\n     * @param {string} phone\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePhone<Preferences extends Models.Preferences>(phone: string, password: string): Promise<Models.User<Preferences>> {\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/phone';\n        const payload: Payload = {};\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get account preferences\n     *\n     * Get the preferences as a key-value object for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Preferences>}\n     */\n    async getPrefs<Preferences extends Models.Preferences>(): Promise<Preferences> {\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update preferences\n     *\n     * Update currently logged in user account preferences. The object you pass is stored as is, and replaces any previous value. The maximum allowed prefs size is 64kB and throws error if exceeded.\n     *\n     * @param {Partial<Preferences>} prefs\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updatePrefs<Preferences extends Models.Preferences>(prefs: Partial<Preferences>): Promise<Models.User<Preferences>> {\n        if (typeof prefs === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"prefs\"');\n        }\n        const apiPath = '/account/prefs';\n        const payload: Payload = {};\n        if (typeof prefs !== 'undefined') {\n            payload['prefs'] = prefs;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create password recovery\n     *\n     * Sends the user an email with a temporary secret key for password reset. When the user clicks the confirmation link he is redirected back to your app password reset URL with the secret key and email address values attached to the URL query string. Use the query string params to submit a request to the [PUT /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#updateRecovery) endpoint to complete the process. The verification link sent to the user&#039;s email address is valid for 1 hour.\n     *\n     * @param {string} email\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createRecovery(email: string, url: string): Promise<Models.Token> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create password recovery (confirmation)\n     *\n     * Use this endpoint to complete the user account password reset. Both the **userId** and **secret** arguments will be passed as query parameters to the redirect URL you have provided when sending your request to the [POST /account/recovery](https://appwrite.io/docs/references/cloud/client-web/account#createRecovery) endpoint.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md) the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updateRecovery(userId: string, secret: string, password: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/recovery';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * List sessions\n     *\n     * Get the list of active sessions across different devices for the currently logged in user.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.SessionList>}\n     */\n    async listSessions(): Promise<Models.SessionList> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete sessions\n     *\n     * Delete all sessions from the user account and remove any sessions cookies from the end client.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSessions(): Promise<{}> {\n        const apiPath = '/account/sessions';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create anonymous session\n     *\n     * Use this endpoint to allow a new user to register an anonymous account in your project. This route will also create a new session for the user. To allow the new user to convert an anonymous account to a normal account, you need to update its [email and password](https://appwrite.io/docs/references/cloud/client-web/account#updateEmail) or create an [OAuth2 session](https://appwrite.io/docs/references/cloud/client-web/account#CreateOAuth2Session).\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createAnonymousSession(): Promise<Models.Session> {\n        const apiPath = '/account/sessions/anonymous';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email password session\n     *\n     * Allow the user to login into their account by providing a valid email and password combination. This route will create a new session for the user.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} email\n     * @param {string} password\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createEmailPasswordSession(email: string, password: string): Promise<Models.Session> {\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        if (typeof password === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"password\"');\n        }\n        const apiPath = '/account/sessions/email';\n        const payload: Payload = {};\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof password !== 'undefined') {\n            payload['password'] = password;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update magic URL session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updateMagicURLSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update phone session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updatePhoneSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create session\n     *\n     * Use this endpoint to create a session from token. Provide the **userId** and **secret** parameters from the successful response of authentication flows initiated by token creation. For example, magic URL and phone login.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async createSession(userId: string, secret: string): Promise<Models.Session> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/sessions/token';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Get session\n     *\n     * Use this endpoint to get a logged in user&#039;s session using a Session ID. Inputting &#039;current&#039; will return the current session being used.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async getSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'get',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update session\n     *\n     * Use this endpoint to extend a session&#039;s length. Extending a session is useful when session expiry is short. If the session was created using an OAuth provider, this endpoint refreshes the access token from the provider.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Session>}\n     */\n    async updateSession(sessionId: string): Promise<Models.Session> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Delete session\n     *\n     * Logout the user. Use &#039;current&#039; as the session ID to logout on this device, use a session ID to logout on another device. If you&#039;re looking to logout the user on all devices, use [Delete Sessions](https://appwrite.io/docs/references/cloud/client-web/account#deleteSessions) instead.\n     *\n     * @param {string} sessionId\n     * @throws {AppwriteException}\n     * @returns {Promise<{}>}\n     */\n    async deleteSession(sessionId: string): Promise<{}> {\n        if (typeof sessionId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"sessionId\"');\n        }\n        const apiPath = '/account/sessions/{sessionId}'.replace('{sessionId}', sessionId);\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'delete',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Update status\n     *\n     * Block the currently logged in user account. Behind the scene, the user record is not deleted but permanently blocked from any access. To completely delete a user, use the Users API instead.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.User<Preferences>>}\n     */\n    async updateStatus<Preferences extends Models.Preferences>(): Promise<Models.User<Preferences>> {\n        const apiPath = '/account/status';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'patch',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email token (OTP)\n     *\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s email is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createEmailToken(userId: string, email: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/email';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create magic URL token\n     *\n     * Sends the user an email with a secret key for creating a session. If the provided user ID has not been registered, a new user will be created. When the user clicks the link in the email, the user is redirected back to the URL you provided with the secret key and userId values attached to the URL query string. Use the query string parameters to submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The link sent to the user&#039;s email address is valid for 1 hour. If you are on a mobile device you can leave the URL parameter empty, so that the login completion will be handled by your Appwrite instance by default.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n\n     *\n     * @param {string} userId\n     * @param {string} email\n     * @param {string} url\n     * @param {boolean} phrase\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createMagicURLToken(userId: string, email: string, url?: string, phrase?: boolean): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof email === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"email\"');\n        }\n        const apiPath = '/account/tokens/magic-url';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof email !== 'undefined') {\n            payload['email'] = email;\n        }\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        if (typeof phrase !== 'undefined') {\n            payload['phrase'] = phrase;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create OAuth2 token\n     *\n     * Allow the user to login to their account using the OAuth2 provider of their choice. Each OAuth2 provider should be enabled from the Appwrite console first. Use the success and failure arguments to provide a redirect URL&#039;s back to your app when login is completed. \n\nIf authentication succeeds, `userId` and `secret` of a token will be appended to the success URL as query parameters. These can be used to create a new session using the [Create session](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {OAuthProvider} provider\n     * @param {string} success\n     * @param {string} failure\n     * @param {string[]} scopes\n     * @throws {AppwriteException}\n     * @returns {Promise<string>}\n     */\n    async createOAuth2Token(provider: OAuthProvider, success?: string, failure?: string, scopes?: string[]): Promise<string> {\n        if (typeof provider === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"provider\"');\n        }\n        const apiPath = '/account/tokens/oauth2/{provider}'.replace('{provider}', provider);\n        const payload: Payload = {};\n        if (typeof success !== 'undefined') {\n            payload['success'] = success;\n        }\n        if (typeof failure !== 'undefined') {\n            payload['failure'] = failure;\n        }\n        if (typeof scopes !== 'undefined') {\n            payload['scopes'] = scopes;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.redirect(\n            'get',\n            uri,\n            apiHeaders,\n            payload\n        );\n    }\n    /**\n     * Create phone token\n     *\n     * Sends the user an SMS with a secret key for creating a session. If the provided user ID has not be registered, a new user will be created. Use the returned user ID and secret and submit a request to the [POST /v1/account/sessions/token](https://appwrite.io/docs/references/cloud/client-web/account#createSession) endpoint to complete the login process. The secret sent to the user&#039;s phone is valid for 15 minutes.\n\nA user is limited to 10 active sessions at a time by default. [Learn more about session limits](https://appwrite.io/docs/authentication-security#limits).\n     *\n     * @param {string} userId\n     * @param {string} phone\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createPhoneToken(userId: string, phone: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof phone === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"phone\"');\n        }\n        const apiPath = '/account/tokens/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof phone !== 'undefined') {\n            payload['phone'] = phone;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email verification\n     *\n     * Use this endpoint to send a verification message to your user email address to confirm they are the valid owners of that address. Both the **userId** and **secret** arguments will be passed as query parameters to the URL you have provided to be attached to the verification email. The provided URL should redirect the user back to your app and allow you to complete the verification process by verifying both the **userId** and **secret** parameters. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updateVerification). The verification link sent to the user&#039;s email address is valid for 7 days.\n\nPlease note that in order to avoid a [Redirect Attack](https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.md), the only valid redirect URLs are the ones from domains you have set when adding your platforms in the console interface.\n\n     *\n     * @param {string} url\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createVerification(url: string): Promise<Models.Token> {\n        if (typeof url === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"url\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof url !== 'undefined') {\n            payload['url'] = url;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create email verification (confirmation)\n     *\n     * Use this endpoint to complete the user email verification process. Use both the **userId** and **secret** parameters that were attached to your app URL to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updateVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create phone verification\n     *\n     * Use this endpoint to send a verification SMS to the currently logged in user. This endpoint is meant for use after updating a user&#039;s phone number using the [accountUpdatePhone](https://appwrite.io/docs/references/cloud/client-web/account#updatePhone) endpoint. Learn more about how to [complete the verification process](https://appwrite.io/docs/references/cloud/client-web/account#updatePhoneVerification). The verification code sent to the user&#039;s phone number is valid for 15 minutes.\n     *\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async createPhoneVerification(): Promise<Models.Token> {\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'post',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n    /**\n     * Create phone verification (confirmation)\n     *\n     * Use this endpoint to complete the user phone verification process. Use the **userId** and **secret** that were sent to your user&#039;s phone number to verify the user email ownership. If confirmed this route will return a 200 status code.\n     *\n     * @param {string} userId\n     * @param {string} secret\n     * @throws {AppwriteException}\n     * @returns {Promise<Models.Token>}\n     */\n    async updatePhoneVerification(userId: string, secret: string): Promise<Models.Token> {\n        if (typeof userId === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"userId\"');\n        }\n        if (typeof secret === 'undefined') {\n            throw new AppwriteException('Missing required parameter: \"secret\"');\n        }\n        const apiPath = '/account/verification/phone';\n        const payload: Payload = {};\n        if (typeof userId !== 'undefined') {\n            payload['userId'] = userId;\n        }\n        if (typeof secret !== 'undefined') {\n            payload['secret'] = secret;\n        }\n        const uri = new URL(this.client.config.endpoint + apiPath);\n\n        const apiHeaders: { [header: string]: string } = {\n            'content-type': 'application/json',\n        }\n\n        return await this.client.call(\n            'put',\n            uri,\n            apiHeaders,\n            payload,\n        );\n    }\n}\n"]}