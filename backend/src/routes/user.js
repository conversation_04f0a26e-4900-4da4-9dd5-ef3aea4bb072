import express from 'express';
import {
  getUserProfile,
  updateUserProfile,
  getUserCredits,
  purchaseCredits,
  purchaseStorage,
  getUserUsage,
  initializeUserProfile,
  getStoragePlans,
  getUserTransactions
} from '../controllers/userController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All user routes require authentication
router.use(authenticateUser);

// Get user profile with stats
router.get('/profile', getUserProfile);

// Update user profile settings
router.put('/profile', updateUserProfile);

// Credits management
router.get('/credits', getUserCredits);
router.post('/credits/purchase', purchaseCredits);

// Storage management
router.get('/storage/plans', getStoragePlans);
router.post('/storage/purchase', purchaseStorage);

// Transactions
router.get('/transactions', getUserTransactions);

// Usage stats
router.get('/usage', getUserUsage);

// Initialize user profile (webhook)
router.post('/initialize', initializeUserProfile);

export default router;
