import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './index.css'
import VueSweetalert2 from 'vue-sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
import { createI18n } from 'vue-i18n'
import EN from './locale/en.json'
import { useAuthStore } from './stores/auth.js'

const i18n = createI18n({
    locale: 'EN',
    messages: {
        EN: EN
    }
})

const pinia = createPinia()

const app = createApp(App)
app.use(pinia)
app.use(router)
app.use(VueSweetalert2);
app.use(i18n)

// Initialize auth store
const authStore = useAuthStore()

app.mount('#app')
