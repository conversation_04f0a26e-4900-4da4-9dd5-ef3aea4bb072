<template>
  <div class="text-center py-12">
    <div class="mx-auto w-24 h-24 bg-gray-800 rounded-full flex items-center justify-center mb-6">
      <Activity class="w-12 h-12 text-gray-400" />
    </div>
    <h3 class="text-xl font-semibold text-white mb-2">Account Activity</h3>
    <p class="text-gray-400 mb-6 max-w-md mx-auto">
      Activity logs, login history, and account timeline will be available soon.
    </p>
    <router-link to="/account" 
                 class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
      <ArrowLeft class="w-4 h-4 mr-2" />
      Back to Settings
    </router-link>
  </div>
</template>

<script setup>
import { Activity, ArrowLeft } from 'lucide-vue-next'
</script>
