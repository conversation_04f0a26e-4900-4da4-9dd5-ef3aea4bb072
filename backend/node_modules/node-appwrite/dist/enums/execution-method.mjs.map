{"version": 3, "sources": ["../../src/enums/execution-method.ts"], "names": ["ExecutionMethod"], "mappings": ";AAAO,IAAK,kBAAL,kBAAKA,qBAAL;AACH,EAAAA,iBAAA,SAAM;AACN,EAAAA,iBAAA,UAAO;AACP,EAAAA,iBAAA,SAAM;AACN,EAAAA,iBAAA,WAAQ;AACR,EAAAA,iBAAA,YAAS;AACT,EAAAA,iBAAA,aAAU;AANF,SAAAA;AAAA,GAAA", "sourcesContent": ["export enum ExecutionMethod {\n    GET = 'GET',\n    POST = 'POST',\n    PUT = 'PUT',\n    PATCH = 'PATCH',\n    DELETE = 'DELETE',\n    OPTIONS = 'OPTIONS',\n}"]}