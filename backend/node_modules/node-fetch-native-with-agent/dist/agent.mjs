var a=Object.defineProperty;var o=(t,r)=>a(t,"name",{value:r,configurable:!0});import{Agent as f}from"node:http";import{Agent as h}from"node:https";import{A as s}from"./shared/node-fetch-native-with-agent.56860586.mjs";import{fetch as g}from"node-fetch-native-with-agent";import"node:assert";import"node:net";import"node:stream";import"node:buffer";import"node:util";import"node:querystring";import"node:diagnostics_channel";import"node:events";import"./shared/node-fetch-native-with-agent.1a4a356d.mjs";import"node:tls";import"node:zlib";import"node:perf_hooks";import"node:util/types";import"node:os";import"node:url";import"node:async_hooks";import"node:console";import"string_decoder";import"node:worker_threads";var u=Object.defineProperty,p=o((t,r)=>u(t,"name",{value:r,configurable:!0}),"r");function m(t,r={}){const e={rejectUnauthorized:r.rejectUnauthorized},i=t?.startsWith("https:")?new h(e):new f,c=new s({connect:e});return{agent:i,dispatcher:c}}o(m,"createAgent"),p(m,"createAgent");function n(t={}){const r=m(void 0,t);return(e,i)=>g(e,{...r,...i})}o(n,"createFetch"),p(n,"createFetch");const A=n({});export{m as createAgent,n as createFetch,A as fetch};
